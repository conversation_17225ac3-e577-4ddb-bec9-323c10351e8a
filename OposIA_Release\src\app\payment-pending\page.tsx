'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';

// Componente que usa useSearchParams envuelto en Suspense
function PaymentPendingContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isChecking, setIsChecking] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'completed' | 'failed'>('pending');

  const sessionId = searchParams.get('session_id');
  const email = searchParams.get('email');
  const planId = searchParams.get('plan'); // Obtener planId para la redirección

  useEffect(() => {
    if (email) {
      setUserEmail(email);
    }

    // Verificar estado del pago cada 5 segundos
    const checkPaymentStatus = async () => {
      if (!sessionId) return;

      try {
        const response = await fetch(`/api/payment/status?session_id=${sessionId}`);
        const data = await response.json();

        if (data.success && data.status === 'completed') {
          setPaymentStatus('completed');
          // Redirigir a thank-you con parámetros de pago exitoso después de 2 segundos
          setTimeout(() => {
            const redirectUrl = `/thank-you?session_id=${sessionId}${planId ? `&plan=${planId}` : ''}&payment_successful=true`;
            router.push(redirectUrl);
          }, 2000);
        } else if (data.success && data.status === 'failed') {
          setPaymentStatus('failed');
        }
      } catch (error) {
        console.error('Error verificando estado del pago:', error);
      }
    };

    // Verificar inmediatamente
    checkPaymentStatus();

    // Verificar cada 5 segundos
    const interval = setInterval(checkPaymentStatus, 5000);

    // Limpiar interval después de 5 minutos
    const timeout = setTimeout(() => {
      clearInterval(interval);
    }, 5 * 60 * 1000);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [sessionId, planId, router, email]);

  const handleCheckStatus = async () => {
    setIsChecking(true);
    
    try {
      if (sessionId) {
        const response = await fetch(`/api/payment/status?session_id=${sessionId}`);
        const data = await response.json();

        if (data.success && data.status === 'completed') {
          setPaymentStatus('completed');
          const redirectUrl = `/thank-you?session_id=${sessionId}${planId ? `&plan=${planId}` : ''}&payment_successful=true`;
          router.push(redirectUrl);
        } else if (data.success && data.status === 'failed') {
          setPaymentStatus('failed');
        }
      }
    } catch (error) {
      console.error('Error verificando estado:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleResendEmail = async () => {
    if (!userEmail) return;

    try {
      const response = await fetch('/api/auth/resend-confirmation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: userEmail }),
      });

      const data = await response.json();
      
      if (data.success) {
        alert('Email de confirmación reenviado exitosamente');
      } else {
        alert('Error reenviando email: ' + data.error);
      }
    } catch (error) {
      console.error('Error reenviando email:', error);
      alert('Error reenviando email');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          
          {paymentStatus === 'pending' && (
            <>
              <div className="text-center mb-6">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Procesando tu Pago
                </h1>
                <p className="text-gray-600">
                  Tu pago está siendo procesado. Una vez completado, tu cuenta será activada automáticamente.
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      ¿Qué está pasando?
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <ul className="list-disc list-inside space-y-1">
                        <li>Tu cuenta ha sido creada</li>
                        <li>Estamos verificando tu pago</li>
                        <li>Tu cuenta será activada automáticamente una vez completado</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handleCheckStatus}
                  disabled={isChecking}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isChecking ? 'Verificando...' : 'Verificar Estado'}
                </button>

                {userEmail && (
                  <button
                    onClick={handleResendEmail}
                    className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    Reenviar Email de Confirmación
                  </button>
                )}
              </div>
            </>
          )}

          {paymentStatus === 'completed' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                ¡Pago Completado!
              </h1>
              <p className="text-gray-600 mb-4">
                Tu pago ha sido procesado exitosamente. Redirigiendo...
              </p>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
            </div>
          )}

          {paymentStatus === 'failed' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Error en el Pago
              </h1>
              <p className="text-gray-600 mb-4">
                Hubo un problema procesando tu pago. Por favor, intenta de nuevo.
              </p>
              <button
                onClick={() => router.push('/upgrade-plan')}
                className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                Intentar de Nuevo
              </button>
            </div>
          )}

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              ¿Necesitas ayuda? <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500">Contáctanos</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente principal que envuelve PaymentPendingContent con Suspense
export default function PaymentPendingPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Cargando...
              </h1>
              <p className="text-gray-600">
                Preparando la página de pago
              </p>
            </div>
          </div>
        </div>
      </div>
    }>
      <PaymentPendingContent />
    </Suspense>
  );
}
