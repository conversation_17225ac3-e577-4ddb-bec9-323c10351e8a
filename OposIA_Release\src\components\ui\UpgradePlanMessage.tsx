'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON>Lock, FiArrowUp, FiStar, FiCheck } from 'react-icons/fi';

interface UpgradePlanMessageProps {
  feature: string;
  featureDescription?: string;
  benefits?: string[];
  className?: string;
}

export default function UpgradePlanMessage({ 
  feature, 
  featureDescription, 
  benefits = [],
  className = ""
}: UpgradePlanMessageProps) {
  
  const defaultBenefits = [
    "Acceso ilimitado a todas las funcionalidades",
    "Generación de contenido sin límites",
    "Soporte prioritario",
    "Nuevas funcionalidades en primicia"
  ];

  const displayBenefits = benefits.length > 0 ? benefits : defaultBenefits;

  const featureNames: Record<string, { name: string; description: string }> = {
    'ai_tutor_chat': {
      name: 'Chat con IA',
      description: 'Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas.'
    },
    'study_planning': {
      name: 'Planificación de Estudios',
      description: 'Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos.'
    },
    'summary_a1_a2': {
      name: 'Resúmenes Avanzados',
      description: 'Genera resúmenes inteligentes y estructurados de tus documentos de estudio.'
    }
  };

  const featureInfo = featureNames[feature] || { 
    name: feature, 
    description: featureDescription || 'Esta funcionalidad avanzada te ayudará a mejorar tu preparación.' 
  };

  return (
    <div className={`min-h-screen bg-gray-50 flex items-center justify-center p-4 ${className}`}>
      <div className="max-w-2xl w-full">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Header con gradiente */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white">
            <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiLock className="w-10 h-10" />
            </div>
            <h1 className="text-3xl font-bold mb-4">
              {featureInfo.name}
            </h1>
            <p className="text-blue-100 text-lg leading-relaxed">
              {featureInfo.description}
            </p>
          </div>

          {/* Contenido */}
          <div className="px-8 py-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6">
                <FiStar className="w-4 h-4 mr-2" />
                Funcionalidad Premium
              </div>
              
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                Actualiza tu plan para acceder
              </h2>
              <p className="text-gray-600 text-lg">
                Esta funcionalidad está disponible para usuarios con planes de pago.
                Actualiza ahora y desbloquea todo el potencial de OposIA.
              </p>
            </div>

            {/* Beneficios */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">
                ¿Qué obtienes al actualizar?
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {displayBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <FiCheck className="w-4 h-4 text-green-600" />
                    </div>
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Botones de acción */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/upgrade-plan"
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <FiArrowUp className="w-5 h-5 mr-2" />
                Actualizar Plan
              </Link>
              
              <Link
                href="/app"
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors"
              >
                Volver al Dashboard
              </Link>
            </div>

            {/* Nota adicional */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-500">
                ¿Tienes preguntas? <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">Contáctanos</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
