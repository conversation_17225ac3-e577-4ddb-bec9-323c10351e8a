import React, { useState, useEffect } from 'react';
import { PreguntaTest } from '@/lib/supabase/supabaseClient';
import { FiChevronLeft, FiChevronRight, FiCheck, FiX, FiClock, FiTarget } from 'react-icons/fi';

interface ConfiguracionTest {
  testId: string;
  cantidad: number;
  maxPreguntas: number;
}

interface TestRepasoViewerProps {
  preguntas: PreguntaTest[];
  configuracion: ConfiguracionTest[];
  onFinalizar: (resultados: ResultadoRepaso) => void;
  onCancelar: () => void;
}

interface RespuestaUsuario {
  preguntaId: string;
  respuestaSeleccionada: 'a' | 'b' | 'c' | 'd' | 'blank' | null;
  esCorrecta: boolean;
  tiempoRespuesta: number;
}

interface ResultadoRepaso {
  totalPreguntas: number;
  respuestasCorrectas: number;
  respuestasIncorrectas: number;
  respuestasEnBlanco?: number;
  tiempoTotal: number;
  porcentajeAcierto: number;
  respuestas: RespuestaUsuario[];
}

export default function TestRepasoViewer({ preguntas, configuracion, onFinalizar, onCancelar }: TestRepasoViewerProps) {
  const [preguntaActual, setPreguntaActual] = useState(0);
  const [respuestasUsuario, setRespuestasUsuario] = useState<Record<string, 'a' | 'b' | 'c' | 'd' | 'blank'>>({});
  const [respuestaSeleccionada, setRespuestaSeleccionada] = useState<'a' | 'b' | 'c' | 'd' | 'blank' | null>(null);
  const [tiempoInicio, setTiempoInicio] = useState<number>(Date.now());
  const [tiemposPregunta, setTiemposPregunta] = useState<Record<string, number>>({});
  const [testFinalizado, setTestFinalizado] = useState(false);
  const [resultadosLocales, setResultadosLocales] = useState<{
    correctas: number;
    incorrectas: number;
    enBlanco: number;
    porcentaje: number;
    tiempoTotal: number;
  } | null>(null);

  useEffect(() => {
    setTiempoInicio(Date.now());
  }, []);

  useEffect(() => {
    // Resetear cuando cambia la pregunta
    const respuestaExistente = respuestasUsuario[preguntas[preguntaActual]?.id];
    setRespuestaSeleccionada(respuestaExistente || null);
  }, [preguntaActual, respuestasUsuario, preguntas]);

  const pregunta = preguntas[preguntaActual];

  const seleccionarRespuesta = (opcion: 'a' | 'b' | 'c' | 'd' | 'blank') => {
    if (testFinalizado) return;

    setRespuestaSeleccionada(opcion);

    // Guardar el tiempo que tardó en responder
    if (tiempoInicio) {
      const tiempoActual = Date.now();
      const tiempoPregunta = tiempoActual - tiempoInicio;
      setTiemposPregunta(prev => ({
        ...prev,
        [preguntas[preguntaActual].id]: tiempoPregunta
      }));
      setTiempoInicio(tiempoActual);
    }

    // Guardar la respuesta del usuario
    setRespuestasUsuario(prev => ({
      ...prev,
      [preguntas[preguntaActual].id]: opcion
    }));
  };

  const siguientePregunta = () => {
    // Si no hay respuesta seleccionada, tratarla como "blank"
    if (!respuestaSeleccionada) {
      seleccionarRespuesta('blank');
    }

    if (preguntaActual < preguntas.length - 1) {
      setPreguntaActual(prev => prev + 1);
    } else {
      finalizarTest();
    }
  };

  const preguntaAnterior = () => {
    if (preguntaActual > 0) {
      setPreguntaActual(prev => prev - 1);
    }
  };

  const finalizarTest = () => {
    setTestFinalizado(true);

    // Calcular resultados
    let correctas = 0;
    let incorrectas = 0;
    let enBlanco = 0;
    let tiempoTotal = 0;
    const respuestasDetalladas: RespuestaUsuario[] = [];

    preguntas.forEach(pregunta => {
      const respuestaUsuario = respuestasUsuario[pregunta.id];
      const tiempoRespuesta = tiemposPregunta[pregunta.id] || 0;

      if (respuestaUsuario) {
        if (respuestaUsuario === 'blank') {
          enBlanco++;
        } else if (respuestaUsuario === pregunta.respuesta_correcta) {
          correctas++;
        } else {
          incorrectas++;
        }
      } else {
        enBlanco++;
      }

      respuestasDetalladas.push({
        preguntaId: pregunta.id,
        respuestaSeleccionada: respuestaUsuario || 'blank',
        esCorrecta: respuestaUsuario === pregunta.respuesta_correcta,
        tiempoRespuesta
      });

      tiempoTotal += tiempoRespuesta;
    });

    const porcentajeAcierto = (correctas / preguntas.length) * 100;

    // Guardar resultados localmente para mostrar
    setResultadosLocales({
      correctas,
      incorrectas,
      enBlanco,
      porcentaje: porcentajeAcierto,
      tiempoTotal
    });

    // También enviar al componente padre
    const resultados: ResultadoRepaso = {
      totalPreguntas: preguntas.length,
      respuestasCorrectas: correctas,
      respuestasIncorrectas: incorrectas,
      respuestasEnBlanco: enBlanco,
      tiempoTotal,
      porcentajeAcierto,
      respuestas: respuestasDetalladas
    };

    onFinalizar(resultados);
  };

  const getOpcionClass = (opcion: 'a' | 'b' | 'c' | 'd') => {
    if (testFinalizado) {
      // Mostrar resultados solo cuando el test ha finalizado
      if (opcion === pregunta.respuesta_correcta) {
        return 'bg-green-100 border-green-500 text-green-800';
      }

      if (opcion === respuestaSeleccionada && opcion !== pregunta.respuesta_correcta) {
        return 'bg-red-100 border-red-500 text-red-800';
      }

      return 'bg-gray-50';
    }

    // Durante el test, solo mostrar selección
    const esSeleccionada = respuestaSeleccionada === opcion;
    return esSeleccionada
      ? 'bg-indigo-100 border-indigo-500'
      : 'hover:bg-gray-50 cursor-pointer';
  };

  const getOpcionIcon = (opcion: 'a' | 'b' | 'c' | 'd') => {
    if (!testFinalizado) return null;

    if (opcion === pregunta.respuesta_correcta) {
      return <FiCheck className="text-green-600" />;
    }

    if (opcion === respuestaSeleccionada && opcion !== pregunta.respuesta_correcta) {
      return <FiX className="text-red-600" />;
    }

    return null;
  };

  const formatearTiempo = (milisegundos: number): string => {
    const minutos = Math.floor(milisegundos / 60000);
    const segundos = Math.floor((milisegundos % 60000) / 1000);
    return `${minutos}:${segundos.toString().padStart(2, '0')}`;
  };

  const reiniciarTest = () => {
    setPreguntaActual(0);
    setRespuestasUsuario({});
    setRespuestaSeleccionada(null);
    setTiempoInicio(Date.now());
    setTiemposPregunta({});
    setTestFinalizado(false);
    setResultadosLocales(null);
  };

  const progreso = ((preguntaActual + 1) / preguntas.length) * 100;
  const tiempoTranscurrido = Math.floor((Date.now() - tiempoInicio) / 1000);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header con progreso */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Test de Repaso</h2>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <FiTarget className="h-4 w-4" />
              {preguntaActual + 1} de {preguntas.length}
            </div>
            {!testFinalizado && (
              <div className="flex items-center gap-1">
                <FiClock className="h-4 w-4" />
                {Math.floor(tiempoTranscurrido / 60)}:{(tiempoTranscurrido % 60).toString().padStart(2, '0')}
              </div>
            )}
          </div>
        </div>
        
        {/* Barra de progreso */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progreso}%` }}
          ></div>
        </div>
      </div>

      {/* Resultados del Test */}
      {testFinalizado && resultadosLocales && (
        <div className="bg-gray-50 border rounded-lg p-4">
          <h4 className="font-semibold text-lg mb-3">Resultados del Test</h4>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm font-medium text-green-800">Correctas</p>
              <p className="text-2xl font-bold text-green-700">{resultadosLocales.correctas}</p>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm font-medium text-red-800">Incorrectas</p>
              <p className="text-2xl font-bold text-red-700">{resultadosLocales.incorrectas}</p>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-sm font-medium text-yellow-800">En Blanco</p>
              <p className="text-2xl font-bold text-yellow-700">{resultadosLocales.enBlanco}</p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm font-medium text-blue-800">Porcentaje</p>
              <p className="text-2xl font-bold text-blue-700">{resultadosLocales.porcentaje.toFixed(1)}%</p>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <p className="text-sm font-medium text-purple-800">Tiempo Total</p>
              <p className="text-2xl font-bold text-purple-700">{formatearTiempo(resultadosLocales.tiempoTotal)}</p>
            </div>
          </div>
          <div className="mt-4 flex justify-center">
            <button
              onClick={reiniciarTest}
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
            >
              Realizar de nuevo
            </button>
          </div>
        </div>
      )}

      {/* Pregunta actual */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-medium mb-6">{pregunta.pregunta}</h3>
        
        <div className="space-y-3">
          {(['a', 'b', 'c', 'd'] as const).map((opcion) => {
            const esSeleccionada = respuestaSeleccionada === opcion;

            return (
              <div
                key={opcion}
                className={`p-3 border rounded-lg cursor-pointer transition-all ${getOpcionClass(opcion)}`}
                onClick={() => !testFinalizado && seleccionarRespuesta(opcion)}
              >
                <div className="flex items-start">
                  <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                    testFinalizado && opcion === pregunta.respuesta_correcta ? 'bg-green-500 text-white' :
                    testFinalizado && esSeleccionada && opcion !== pregunta.respuesta_correcta ? 'bg-red-500 text-white' :
                    esSeleccionada ? 'bg-indigo-500 text-white' :
                    'bg-gray-200 text-gray-700'
                  }`}>
                    {opcion.toUpperCase()}
                  </div>
                  <div className="flex-grow">
                    {pregunta[`opcion_${opcion}` as keyof PreguntaTest] as string}
                  </div>
                  {getOpcionIcon(opcion)}
                </div>
              </div>
            );
          })}

          {/* Opción para dejar en blanco */}
          {!testFinalizado && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div
                className={`p-3 border rounded-lg cursor-pointer flex items-center ${
                  respuestaSeleccionada === 'blank'
                    ? 'bg-yellow-50 border-yellow-300'
                    : 'hover:bg-gray-50 border-gray-300'
                }`}
                onClick={() => seleccionarRespuesta('blank')}
              >
                <input
                  type="checkbox"
                  checked={respuestaSeleccionada === 'blank'}
                  onChange={() => seleccionarRespuesta('blank')}
                  className="mr-3 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  onClick={(e) => e.stopPropagation()}
                />
                <span className="text-sm text-gray-600">
                  Marque si quiere dejar en blanco
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Controles de navegación */}
      <div className="flex justify-between items-center">
        <button
          onClick={preguntaAnterior}
          disabled={preguntaActual === 0}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
            preguntaActual === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <FiChevronLeft className="h-4 w-4" />
          Anterior
        </button>

        <div className="flex gap-2">
          <button
            onClick={onCancelar}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancelar
          </button>

          {!testFinalizado && (
            <button
              onClick={siguientePregunta}
              className="bg-indigo-600 text-white py-2 px-4 rounded flex items-center hover:bg-indigo-700"
            >
              {preguntaActual === preguntas.length - 1 ? 'Finalizar Test' : 'Siguiente'}
              {preguntaActual < preguntas.length - 1 && <FiChevronRight className="ml-1" />}
            </button>
          )}

          {testFinalizado && preguntaActual < preguntas.length - 1 && (
            <button
              onClick={() => setPreguntaActual(prev => prev + 1)}
              className="text-indigo-600 hover:text-indigo-800 flex items-center"
            >
              Siguiente <FiChevronRight className="ml-1" />
            </button>
          )}
        </div>
      </div>

      {/* Indicador de respuesta */}
      {!testFinalizado && !respuestaSeleccionada && (
        <div className="text-center text-gray-500 text-sm">
          Selecciona una respuesta para continuar
        </div>
      )}
    </div>
  );
}
