═══════════════════════════════════════════════════════════════════════════════
                                   OposIA
                        Sistema de Preparación de Oposiciones
                              con Inteligencia Artificial
═══════════════════════════════════════════════════════════════════════════════

🚀 INSTRUCCIONES DE INICIO RÁPIDO

Para iniciar la aplicación OposIA, siga estos pasos:

┌─────────────────────────────────────────────────────────────────────────────┐
│                              OPCIÓN 1: AUTOMÁTICO                          │
│                                (RECOMENDADO)                               │
└─────────────────────────────────────────────────────────────────────────────┘

1. Hacer DOBLE CLIC en el archivo:
   
   📁 INICIAR_APLICACION.bat  (Windows)
   📁 iniciar_aplicacion.sh   (Mac/Linux)

2. Seguir las instrucciones que aparecen en pantalla

3. La aplicación se abrirá automáticamente en:
   🌐 http://localhost:3000

┌─────────────────────────────────────────────────────────────────────────────┐
│                              OPCIÓN 2: MANUAL                              │
│                          (Para usuarios técnicos)                          │
└─────────────────────────────────────────────────────────────────────────────┘

1. Abrir terminal/símbolo del sistema en esta carpeta

2. Ejecutar los siguientes comandos:
   
   npm install
   npm run dev

3. Abrir navegador en: http://localhost:3000

┌─────────────────────────────────────────────────────────────────────────────┐
│                                REQUISITOS                                  │
└─────────────────────────────────────────────────────────────────────────────┘

✅ Node.js versión 18 o superior
   📥 Descargar desde: https://nodejs.org/

✅ Navegador web moderno (Chrome, Firefox, Safari, Edge)

✅ Conexión a internet (para funciones de IA)

┌─────────────────────────────────────────────────────────────────────────────┐
│                            CONFIGURACIÓN                                   │
└─────────────────────────────────────────────────────────────────────────────┘

⚠️  IMPORTANTE: Para usar todas las funciones, necesita configurar:

1. Variables de entorno en archivo .env.local
2. Cuentas en servicios externos:
   - Supabase (base de datos)
   - OpenAI (inteligencia artificial)
   - Stripe (pagos)

📝 Se creará automáticamente un archivo .env.example con la estructura necesaria.

┌─────────────────────────────────────────────────────────────────────────────┐
│                              SOLUCIÓN DE PROBLEMAS                         │
└─────────────────────────────────────────────────────────────────────────────┘

❌ "Node.js no está instalado"
   → Instalar Node.js desde https://nodejs.org/

❌ "Error instalando dependencias"
   → Verificar conexión a internet
   → Ejecutar: npm cache clean --force

❌ "Puerto 3000 en uso"
   → La aplicación se abrirá en otro puerto automáticamente
   → O cerrar otras aplicaciones que usen el puerto 3000

❌ "Página no carga"
   → Verificar que el servidor esté ejecutándose
   → Revisar la terminal por errores
   → Intentar refrescar la página

┌─────────────────────────────────────────────────────────────────────────────┐
│                                FUNCIONES                                   │
└─────────────────────────────────────────────────────────────────────────────┘

🎯 Gestión de documentos PDF
📝 Generación de tests con IA
🃏 Creación de flashcards
🧠 Mapas mentales automáticos
💬 Chat tutor inteligente
📅 Planificación de estudios
👤 Sistema de usuarios
💳 Gestión de suscripciones
🔐 Panel administrativo

┌─────────────────────────────────────────────────────────────────────────────┐
│                              INFORMACIÓN                                   │
└─────────────────────────────────────────────────────────────────────────────┘

📄 Documentación completa: README.md
📋 Instrucciones de registro PI: INSTRUCCIONES_REGISTRO_PI.md
🏗️ Arquitectura: Next.js + React + TypeScript + Supabase
🤖 IA: OpenAI GPT-4
💰 Pagos: Stripe
🔒 Seguridad: Autenticación JWT + RLS

═══════════════════════════════════════════════════════════════════════════════

¡Gracias por usar OposIA! 🚀

═══════════════════════════════════════════════════════════════════════════════
