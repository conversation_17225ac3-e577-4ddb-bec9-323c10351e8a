/**
 * Tipos relacionados con APIs y respuestas de servicios
 *
 * Este archivo contiene interfaces para respuestas de APIs,
 * requests, y tipos relacionados con comunicación externa.
 */

// ============================================================================
// TIPOS DE RESPUESTAS DE IA (GEMINI/OPENAI)
// ============================================================================

export interface PreguntaGenerada {
  pregunta: string;
  opciones: {
    a: string;
    b: string;
    c: string;
    d: string;
  };
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}

// ============================================================================
// TIPOS DE NOTIFICACIONES Y WEBHOOKS
// ============================================================================

export interface UsageWarning {
  feature: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface Alert {
  type: 'error' | 'warning' | 'info';
  message: string;
  action: 'upgrade' | 'reminder';
}

export interface SubscriptionRequestData {
  type: 'subscription_request';
  email: string;
  customerName?: string;
  planName: string;
}

export interface SubscriptionCancelledData {
  type: 'subscription_cancelled';
  userEmail: string;
  userName?: string;
  subscriptionPlan: string;
  periodEnd: string;
}

export type NotificationData = SubscriptionRequestData | SubscriptionCancelledData;

// ============================================================================
// TIPOS DE CONFIGURACIÓN DE TESTS Y REPASOS
// ============================================================================

export interface ConfiguracionTest {
  testId: string;
  cantidad: number;
  maxPreguntas: number;
}

export interface RespuestaUsuario {
  preguntaId: string;
  respuestaSeleccionada: 'a' | 'b' | 'c' | 'd' | 'blank' | null;
  esCorrecta: boolean;
  tiempoRespuesta: number;
}

export interface ResultadoRepaso {
  totalPreguntas: number;
  respuestasCorrectas: number;
  respuestasIncorrectas: number;
  tiempoTotal: number;
  porcentajeAcierto: number;
  respuestas: RespuestaUsuario[];
}

export interface TestRepasoViewerProps {
  preguntas: any[]; // TODO: Importar tipo PreguntaTest desde database
  configuracion: ConfiguracionTest[];
  onFinalizar: (resultados: ResultadoRepaso) => void;
  onCancelar: () => void;
}
