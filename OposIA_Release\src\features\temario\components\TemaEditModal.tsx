import React, { useState, useEffect } from 'react';
import { FiX, FiSave } from 'react-icons/fi';
import { Tema } from '@/lib/supabase/supabaseClient';
import { actualizarTema } from '../services/temarioService';
import { toast } from 'react-hot-toast';

interface TemaEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  tema: Tema;
  onSave: (temaActualizado: Tema) => void;
}

const TemaEditModal: React.FC<TemaEditModalProps> = ({
  isOpen,
  onClose,
  tema,
  onSave
}) => {
  const [titulo, setTitulo] = useState('');
  const [descripcion, setDescripcion] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Cargar datos del tema cuando se abre el modal
  useEffect(() => {
    if (isOpen && tema) {
      setTitulo(tema.titulo);
      setDescripcion(tema.descripcion || '');
    }
  }, [isOpen, tema]);

  const handleSave = async () => {
    if (!titulo.trim()) {
      toast.error('El título del tema es obligatorio');
      return;
    }

    setIsLoading(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Actualizando tema...');

      const success = await actualizarTema(tema.id, titulo.trim(), descripcion.trim());

      if (success) {
        toast.success('Tema actualizado exitosamente', { id: loadingToastId });

        // Crear el tema actualizado para pasar al componente padre
        const temaActualizado: Tema = {
          ...tema,
          titulo: titulo.trim(),
          descripcion: descripcion.trim(),
          actualizado_en: new Date().toISOString()
        };

        onSave(temaActualizado);
        onClose();
      } else {
        toast.error('Error al actualizar el tema', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al actualizar tema:', error);
      toast.error('Error al actualizar el tema', { id: loadingToastId });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Restaurar valores originales
    setTitulo(tema.titulo);
    setDescripcion(tema.descripcion || '');
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Editar Tema {tema.numero}
          </h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4" onKeyDown={handleKeyDown}>
          {/* Título */}
          <div>
            <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 mb-2">
              Título del tema *
            </label>
            <input
              type="text"
              id="titulo"
              value={titulo}
              onChange={(e) => setTitulo(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Ej: Introducción al Derecho Administrativo"
              disabled={isLoading}
              autoFocus
            />
          </div>

          {/* Descripción */}
          <div>
            <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
              Descripción (opcional)
            </label>
            <textarea
              id="descripcion"
              value={descripcion}
              onChange={(e) => setDescripcion(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="Describe brevemente el contenido del tema..."
              disabled={isLoading}
            />
          </div>

          {/* Información adicional */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-800">
                  <strong>Nota:</strong> El número del tema y su estado de completado no se pueden modificar desde aquí.
                  Solo puedes editar el título y la descripción.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading || !titulo.trim()}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Guardando...
              </>
            ) : (
              <>
                <FiSave className="w-4 h-4 mr-2" />
                Guardar cambios
              </>
            )}
          </button>
        </div>

        {/* Atajos de teclado */}
        <div className="px-6 pb-4">
          <p className="text-xs text-gray-500">
            <strong>Atajos:</strong> Esc para cancelar • Ctrl+Enter para guardar
          </p>
        </div>
      </div>
    </div>
  );
};

export default TemaEditModal;
