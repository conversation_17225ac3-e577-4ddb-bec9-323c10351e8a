import { createClient } from './client';
import { TokenTrackingData } from '@/lib/ai/tokenTracker';
import {
  getPlanConfiguration,
  hasFeatureAccess,
  ACTIVITY_TO_FEATURE_MAP,
  TOKEN_LIMITS,
  ERROR_MESSAGES
} from '@/config';

export interface TokenUsageRecord {
  id: string;
  user_id: string;
  activity_type: string;
  model_name: string;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  estimated_cost: number;
  usage_month: string;
  created_at: string;
}

export interface TokenUsageStats {
  totalSessions: number;
  totalTokens: number;
  totalCost: number;
  byActivity: Record<string, { tokens: number; cost: number; count: number }>;
  byModel: Record<string, { tokens: number; cost: number; count: number }>;
}

export interface UserProfile {
  id: string;
  user_id: string;
  subscription_plan: 'free' | 'usuario' | 'pro';
  monthly_token_limit: number;
  current_month_tokens: number;
  current_month: string;
  payment_verified: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Guarda el uso de tokens en Supabase con validación de plan
 */
export async function saveTokenUsage(data: TokenTrackingData): Promise<void> {
  try {
    console.log('🔄 saveTokenUsage (cliente) iniciado con data:', data);

    // Este servicio solo funciona en el cliente
    const supabase = createClient();
    console.log('✅ Cliente Supabase creado');

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');

    if (userError || !user) {
      console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);
      return;
    }

    // Validar que el usuario tiene acceso a la actividad
    const accessValidation = await validateActivityAccess(user.id, data.activity, data.usage.totalTokens);
    if (!accessValidation.allowed) {
      console.warn('❌ Acceso denegado para actividad:', accessValidation.reason);
      throw new Error(accessValidation.reason);
    }

    const usageRecord = {
      user_id: user.id,
      activity_type: data.activity,
      model_name: data.model,
      prompt_tokens: data.usage.promptTokens,
      completion_tokens: data.usage.completionTokens,
      total_tokens: data.usage.totalTokens,
      estimated_cost: data.usage.estimatedCost || 0,
      usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01
    };

    console.log('📝 Registro a insertar:', usageRecord);

    const { error } = await supabase
      .from('user_token_usage')
      .insert([usageRecord]);

    if (error) {
      console.error('❌ Error al guardar uso de tokens:', error);
      return;
    }

    console.log('✅ Registro insertado exitosamente en user_token_usage');

    // Actualizar contador mensual del usuario
    await updateMonthlyTokenCount(user.id, data.usage.totalTokens);

  } catch (error) {
    console.error('Error en saveTokenUsage:', error);
  }
}

/**
 * Actualiza el contador mensual de tokens del usuario
 */
async function updateMonthlyTokenCount(userId: string, tokens: number): Promise<void> {
  try {
    const supabase = createClient();
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    // Obtener o crear perfil del usuario
    let { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error al obtener perfil:', profileError);
      return;
    }

    if (!profile) {
      // Crear perfil nuevo
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: userId,
          subscription_plan: 'free',
          monthly_token_limit: TOKEN_LIMITS.DEFAULT_FREE_LIMIT,
          current_month_tokens: tokens,
          current_month: currentMonth
        }]);

      if (insertError) {
        console.error('Error al crear perfil:', insertError);
      }
    } else {
      // Actualizar perfil existente
      const newTokenCount = profile.current_month === currentMonth 
        ? profile.current_month_tokens + tokens 
        : tokens; // Reset si es nuevo mes

      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          current_month_tokens: newTokenCount,
          current_month: currentMonth,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error al actualizar perfil:', updateError);
      }
    }
  } catch (error) {
    console.error('Error en updateMonthlyTokenCount:', error);
  }
}

/**
 * Obtiene estadísticas de uso de tokens del usuario actual
 */
export async function getUserTokenStats(): Promise<TokenUsageStats> {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return getEmptyStats();
    }

    const { data: records, error } = await supabase
      .from('user_token_usage')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error al obtener estadísticas:', error);
      return getEmptyStats();
    }

    return calculateStats(records || []);

  } catch (error) {
    console.error('Error en getUserTokenStats:', error);
    return getEmptyStats();
  }
}

/**
 * Calcula estadísticas a partir de los registros
 */
function calculateStats(records: TokenUsageRecord[]): TokenUsageStats {
  const stats: TokenUsageStats = {
    totalSessions: records.length,
    totalTokens: 0,
    totalCost: 0,
    byActivity: {},
    byModel: {}
  };

  records.forEach(record => {
    const tokens = record.total_tokens;
    const cost = record.estimated_cost;

    stats.totalTokens += tokens;
    stats.totalCost += cost;

    // Por actividad
    if (!stats.byActivity[record.activity_type]) {
      stats.byActivity[record.activity_type] = { tokens: 0, cost: 0, count: 0 };
    }
    stats.byActivity[record.activity_type].tokens += tokens;
    stats.byActivity[record.activity_type].cost += cost;
    stats.byActivity[record.activity_type].count += 1;

    // Por modelo
    if (!stats.byModel[record.model_name]) {
      stats.byModel[record.model_name] = { tokens: 0, cost: 0, count: 0 };
    }
    stats.byModel[record.model_name].tokens += tokens;
    stats.byModel[record.model_name].cost += cost;
    stats.byModel[record.model_name].count += 1;
  });

  return stats;
}

/**
 * Retorna estadísticas vacías
 */
function getEmptyStats(): TokenUsageStats {
  return {
    totalSessions: 0,
    totalTokens: 0,
    totalCost: 0,
    byActivity: {},
    byModel: {}
  };
}

/**
 * Obtiene el perfil del usuario actual
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return null;
    }

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error al obtener perfil:', error);
      return null;
    }

    return profile;

  } catch (error) {
    console.error('Error en getUserProfile:', error);
    return null;
  }
}

/**
 * Verifica si el usuario ha alcanzado su límite mensual
 */
export async function checkTokenLimit(): Promise<{ 
  hasReachedLimit: boolean; 
  currentTokens: number; 
  limit: number; 
  percentage: number 
}> {
  try {
    const profile = await getUserProfile();
    
    if (!profile) {
      return { hasReachedLimit: false, currentTokens: 0, limit: TOKEN_LIMITS.DEFAULT_FREE_LIMIT, percentage: 0 };
    }

    const currentTokens = profile.current_month_tokens || 0;
    const monthlyLimit = profile.monthly_token_limit || 0;
    const percentage = monthlyLimit > 0 ? (currentTokens / monthlyLimit) * 100 : 0;
    const hasReachedLimit = currentTokens >= monthlyLimit;

    return {
      hasReachedLimit,
      currentTokens,
      limit: monthlyLimit,
      percentage
    };

  } catch (error) {
    console.error('Error en checkTokenLimit:', error);
    return { hasReachedLimit: false, currentTokens: 0, limit: TOKEN_LIMITS.DEFAULT_FREE_LIMIT, percentage: 0 };
  }
}

/**
 * Valida si un usuario tiene acceso a una actividad específica
 */
async function validateActivityAccess(
  userId: string,
  activity: string,
  tokensToUse: number
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    const supabase = createClient();

    // Obtener perfil del usuario
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, payment_verified, current_month_tokens, monthly_token_limit, current_month')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return { allowed: false, reason: ERROR_MESSAGES.PROFILE_NOT_FOUND };
    }

    // Mapear actividades a características usando configuración centralizada
    const featureName = ACTIVITY_TO_FEATURE_MAP[activity] || activity;

    // Verificar acceso a la característica según el plan
    if (!hasFeatureAccess(profile.subscription_plan, featureName)) {
      return {
        allowed: false,
        reason: `La actividad ${activity} no está disponible en el plan ${profile.subscription_plan}`
      };
    }

    // Verificar pago para planes de pago
    if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
      return {
        allowed: false,
        reason: 'Pago no verificado. Complete el proceso de pago para usar esta función.'
      };
    }

    // Verificar límites de tokens
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    let currentTokens = profile.current_month_tokens;

    // Reset si es nuevo mes
    if (profile.current_month !== currentMonth) {
      currentTokens = 0;
    }

    if (currentTokens + tokensToUse > profile.monthly_token_limit) {
      return {
        allowed: false,
        reason: `Límite mensual de tokens alcanzado. Usado: ${currentTokens}/${profile.monthly_token_limit}`
      };
    }

    return { allowed: true };

  } catch (error) {
    console.error('Error validating activity access:', error);
    return { allowed: false, reason: 'Error interno de validación' };
  }
}

/**
 * Obtiene información detallada del plan del usuario
 */
export async function getUserPlanInfo(): Promise<{
  plan: string;
  planName: string;
  features: string[];
  tokenUsage: {
    current: number;
    limit: number;
    percentage: number;
    remaining: number;
  };
  paymentVerified: boolean;
} | null> {
  try {
    const profile = await getUserProfile();

    if (!profile) {
      return null;
    }

    const planConfig = getPlanConfiguration(profile.subscription_plan);

    if (!planConfig) {
      return null;
    }

    const currentTokens = profile.current_month_tokens || 0;
    const monthlyLimit = profile.monthly_token_limit || 0;
    const percentage = monthlyLimit > 0 ? (currentTokens / monthlyLimit) * 100 : 0;

    return {
      plan: profile.subscription_plan,
      planName: planConfig.name,
      features: planConfig.features,
      tokenUsage: {
        current: currentTokens,
        limit: monthlyLimit,
        percentage: Math.round(percentage),
        remaining: Math.max(0, monthlyLimit - currentTokens)
      },
      paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'
    };

  } catch (error) {
    console.error('Error getting user plan info:', error);
    return null;
  }
}

/**
 * Verifica si el usuario puede realizar una actividad específica antes de ejecutarla
 */
export async function canPerformActivity(
  activity: string,
  estimatedTokens: number = 0
): Promise<{
  allowed: boolean;
  reason?: string;
  planInfo?: any;
}> {
  try {
    const supabase = createClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { allowed: false, reason: 'Usuario no autenticado' };
    }

    const validation = await validateActivityAccess(user.id, activity, estimatedTokens);

    if (!validation.allowed) {
      const planInfo = await getUserPlanInfo();
      return {
        allowed: false,
        reason: validation.reason,
        planInfo
      };
    }

    return { allowed: true };

  } catch (error) {
    console.error('Error checking activity permission:', error);
    return { allowed: false, reason: 'Error interno de validación' };
  }
}

/**
 * Obtiene datos de progreso de tokens para estadísticas avanzadas
 */
export async function getTokenUsageProgress(): Promise<{
  percentage: number;
  limit: number;
  used: number;
  remaining: number;
  dailyHistory: Array<{
    date: string;
    tokens: number;
  }>;
} | null> {
  try {
    const supabase = createClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return null;
    }

    // Obtener perfil del usuario
    const profile = await getUserProfile();
    if (!profile) {
      return null;
    }

    // Calcular porcentaje de uso
    const percentage = (profile.current_month_tokens / profile.monthly_token_limit) * 100;

    // Obtener historial diario de los últimos 30 días
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: dailyUsage, error: historyError } = await supabase
      .from('user_token_usage')
      .select('created_at, total_tokens')
      .eq('user_id', user.id)
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: true });

    if (historyError) {
      console.error('Error al obtener historial diario:', historyError);
    }

    // Agrupar por día
    const dailyHistory: Array<{ date: string; tokens: number }> = [];
    const dailyMap = new Map<string, number>();

    if (dailyUsage) {
      dailyUsage.forEach(record => {
        const date = new Date(record.created_at).toISOString().split('T')[0];
        const currentTokens = dailyMap.get(date) || 0;
        dailyMap.set(date, currentTokens + record.total_tokens);
      });

      // Convertir a array ordenado
      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        dailyHistory.push({
          date: dateStr,
          tokens: dailyMap.get(dateStr) || 0
        });
      }
    }

    return {
      percentage: Math.round(percentage),
      limit: profile.monthly_token_limit,
      used: profile.current_month_tokens,
      remaining: profile.monthly_token_limit - profile.current_month_tokens,
      dailyHistory
    };

  } catch (error) {
    console.error('Error en getTokenUsageProgress:', error);
    return null;
  }
}

/**
 * Obtiene historial de compras de tokens del usuario
 */
export async function getTokenPurchaseHistory(): Promise<Array<{
  id: string;
  amount: number;
  price: number;
  created_at: string;
  status: string;
}> | null> {
  try {
    const supabase = createClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return null;
    }

    const { data: purchases, error } = await supabase
      .from('token_purchases')
      .select('id, amount, price, created_at, status')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error al obtener historial de compras:', error);
      return null;
    }

    return purchases || [];

  } catch (error) {
    console.error('Error en getTokenPurchaseHistory:', error);
    return null;
  }
}
