// src/lib/auth/validateUserAccess.ts
// Funciones de validación de acceso de usuario

import { createClient } from '@/lib/supabase/supabaseClient';
import { PlanValidationService } from '@/lib/services/planValidation';

export interface AccessValidationResult {
  hasAccess: boolean;
  user?: any;
  profile?: any;
  reason?: string;
  redirectTo?: string;
}

export interface FeatureAccessResult {
  allowed: boolean;
  reason?: string;
  remainingUsage?: number;
  upgradeRequired?: boolean;
  suggestedPlan?: string;
}

/**
 * Validar acceso básico del usuario autenticado
 */
export async function validateUserAccess(): Promise<AccessValidationResult> {
  try {
    const supabase = createClient();
    
    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return {
        hasAccess: false,
        reason: 'Usuario no autenticado',
        redirectTo: '/login'
      };
    }
    
    // Obtener perfil del usuario
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      return {
        hasAccess: false,
        reason: 'Perfil de usuario no encontrado',
        redirectTo: '/payment'
      };
    }
    
    // Verificar pago para planes de pago
    if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
      return {
        hasAccess: false,
        reason: 'Pago no verificado',
        redirectTo: '/payment'
      };
    }
    
    return {
      hasAccess: true,
      user,
      profile
    };
    
  } catch (error) {
    console.error('Error validating user access:', error);
    return {
      hasAccess: false,
      reason: 'Error interno de validación',
      redirectTo: '/login'
    };
  }
}

/**
 * Validar acceso a una característica específica
 */
export async function validateFeatureAccess(
  featureName: string,
  tokensToUse: number = 0
): Promise<FeatureAccessResult> {
  try {
    // Primero validar acceso básico
    const accessValidation = await validateUserAccess();
    
    if (!accessValidation.hasAccess) {
      return {
        allowed: false,
        reason: accessValidation.reason
      };
    }
    
    const userId = accessValidation.user!.id;
    
    // Validar acceso específico a la característica
    const validation = await PlanValidationService.validateFeatureAccess(
      userId,
      featureName,
      tokensToUse
    );
    
    if (!validation.allowed) {
      // Verificar si necesita upgrade
      const upgradeCheck = await PlanValidationService.checkUpgradeNeeded(userId);
      
      return {
        allowed: false,
        reason: validation.reason,
        upgradeRequired: upgradeCheck.needsUpgrade,
        suggestedPlan: upgradeCheck.suggestedPlan
      };
    }
    
    return {
      allowed: true,
      remainingUsage: validation.remainingUsage
    };
    
  } catch (error) {
    console.error('Error validating feature access:', error);
    return {
      allowed: false,
      reason: 'Error interno de validación'
    };
  }
}

/**
 * Validar acceso a rutas protegidas
 */
export async function validateRouteAccess(route: string): Promise<AccessValidationResult> {
  try {
    // Rutas que requieren autenticación básica
    const protectedRoutes = ['/app', '/dashboard', '/profile'];
    
    // Rutas que requieren planes específicos
    const premiumRoutes = {
      '/plan-estudios': ['pro'],
      '/app/ai-tutor': ['usuario', 'pro'],
      '/app/summaries': ['pro']
    };
    
    // Validar acceso básico
    const basicAccess = await validateUserAccess();
    
    if (!basicAccess.hasAccess) {
      return basicAccess;
    }
    
    // Si es una ruta protegida básica, ya tiene acceso
    if (protectedRoutes.some(protectedRoute => route.startsWith(protectedRoute))) {
      // Verificar rutas premium específicas
      for (const [premiumRoute, allowedPlans] of Object.entries(premiumRoutes)) {
        if (route.startsWith(premiumRoute)) {
          const userPlan = basicAccess.profile!.subscription_plan;
          
          if (!allowedPlans.includes(userPlan)) {
            return {
              hasAccess: false,
              reason: `Esta función requiere plan ${allowedPlans.join(' o ')}`,
              redirectTo: '/payment'
            };
          }
        }
      }
      
      return basicAccess;
    }
    
    return basicAccess;
    
  } catch (error) {
    console.error('Error validating route access:', error);
    return {
      hasAccess: false,
      reason: 'Error interno de validación',
      redirectTo: '/login'
    };
  }
}

/**
 * Hook para usar en componentes React
 */
export function useUserAccess() {
  const [accessInfo, setAccessInfo] = React.useState<AccessValidationResult | null>(null);
  const [loading, setLoading] = React.useState(true);
  
  React.useEffect(() => {
    validateUserAccess().then(result => {
      setAccessInfo(result);
      setLoading(false);
    });
  }, []);
  
  return { accessInfo, loading };
}

/**
 * Middleware helper para validación de acceso
 */
export async function withAccessValidation<T>(
  handler: (accessInfo: AccessValidationResult) => Promise<T>,
  requiredFeature?: string
): Promise<T | { error: string; redirectTo?: string }> {
  try {
    const accessValidation = await validateUserAccess();
    
    if (!accessValidation.hasAccess) {
      return {
        error: accessValidation.reason || 'Acceso denegado',
        redirectTo: accessValidation.redirectTo
      };
    }
    
    // Si requiere una característica específica, validarla
    if (requiredFeature) {
      const featureValidation = await validateFeatureAccess(requiredFeature);
      
      if (!featureValidation.allowed) {
        return {
          error: featureValidation.reason || 'Característica no disponible'
        };
      }
    }
    
    return await handler(accessValidation);
    
  } catch (error) {
    console.error('Error in access validation wrapper:', error);
    return {
      error: 'Error interno de validación'
    };
  }
}

/**
 * Validar y actualizar uso de tokens
 */
export async function validateAndUseTokens(
  featureName: string,
  tokensToUse: number,
  activity: string
): Promise<{ success: boolean; error?: string; remainingTokens?: number }> {
  try {
    // Validar acceso
    const validation = await validateFeatureAccess(featureName, tokensToUse);
    
    if (!validation.allowed) {
      return {
        success: false,
        error: validation.reason
      };
    }
    
    // Obtener usuario actual
    const accessInfo = await validateUserAccess();
    
    if (!accessInfo.hasAccess) {
      return {
        success: false,
        error: 'Usuario no autenticado'
      };
    }
    
    // Actualizar uso de tokens
    const updateSuccess = await PlanValidationService.updateTokenUsage(
      accessInfo.user!.id,
      tokensToUse,
      activity
    );
    
    if (!updateSuccess) {
      return {
        success: false,
        error: 'Error actualizando uso de tokens'
      };
    }
    
    return {
      success: true,
      remainingTokens: (validation.remainingUsage || 0) - tokensToUse
    };
    
  } catch (error) {
    console.error('Error validating and using tokens:', error);
    return {
      success: false,
      error: 'Error interno'
    };
  }
}

// Importar React para el hook
import React from 'react';
