'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function DebugUserPage() {
  const { user } = useAuth();
  const [userPlan, setUserPlan] = useState<any>(null);
  const [freeAccountStatus, setFreeAccountStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        setLoading(true);

        // Obtener plan del usuario
        const planResponse = await fetch('/api/user/plan');
        if (planResponse.ok) {
          const planData = await planResponse.json();
          setUserPlan(planData);
        }

        // Obtener estado de cuenta gratuita
        const freeResponse = await fetch('/api/auth/free-account-status');
        if (freeResponse.ok) {
          const freeData = await freeResponse.json();
          setFreeAccountStatus(freeData);
        }

      } catch (error) {
        console.error('Error cargando información del usuario:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadUserInfo();
    }
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Cargando información del usuario...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">No hay usuario autenticado</h1>
          <p className="text-gray-600">Debes iniciar sesión para ver esta información.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Debug: Información del Usuario</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Información básica del usuario */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Usuario Autenticado</h2>
            <div className="space-y-2">
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Creado:</strong> {new Date(user.created_at).toLocaleString()}</p>
              <p><strong>Última conexión:</strong> {user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'N/A'}</p>
            </div>
          </div>

          {/* Plan del usuario */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Plan Actual</h2>
            {userPlan ? (
              <div className="space-y-2">
                <p><strong>Plan:</strong> <span className={`px-2 py-1 rounded text-sm font-medium ${
                  userPlan.plan === 'free' ? 'bg-yellow-100 text-yellow-800' :
                  userPlan.plan === 'usuario' ? 'bg-blue-100 text-blue-800' :
                  userPlan.plan === 'pro' ? 'bg-purple-100 text-purple-800' :
                  'bg-gray-100 text-gray-800'
                }`}>{userPlan.plan || 'No definido'}</span></p>
              </div>
            ) : (
              <p className="text-gray-500">No se pudo cargar información del plan</p>
            )}
          </div>

          {/* Estado de cuenta gratuita */}
          <div className="bg-white rounded-lg shadow p-6 md:col-span-2">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Estado de Cuenta Gratuita</h2>
            {freeAccountStatus ? (
              <div className="space-y-4">
                <p><strong>Es cuenta gratuita:</strong> {freeAccountStatus.isFreeAccount ? 'Sí' : 'No'}</p>
                
                {freeAccountStatus.isFreeAccount && freeAccountStatus.status && (
                  <>
                    <div>
                      <p><strong>Estado:</strong> {freeAccountStatus.status.isActive ? 'Activa' : 'Inactiva'}</p>
                      <p><strong>Expira:</strong> {new Date(freeAccountStatus.status.expiresAt).toLocaleString()}</p>
                      <p><strong>Días restantes:</strong> {freeAccountStatus.status.daysRemaining}</p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-gray-800 mb-2">Uso Actual:</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm text-gray-600">Documentos</p>
                          <p className="text-lg font-semibold">{freeAccountStatus.status.usageCount.documents}/{freeAccountStatus.status.limits.documents}</p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm text-gray-600">Tests</p>
                          <p className="text-lg font-semibold">{freeAccountStatus.status.usageCount.tests}/{freeAccountStatus.status.limits.tests}</p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm text-gray-600">Flashcards</p>
                          <p className="text-lg font-semibold">{freeAccountStatus.status.usageCount.flashcards}/{freeAccountStatus.status.limits.flashcards}</p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm text-gray-600">Mapas Mentales</p>
                          <p className="text-lg font-semibold">{freeAccountStatus.status.usageCount.mindMaps}/{freeAccountStatus.status.limits.mindMaps}</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No se pudo cargar información de cuenta gratuita</p>
            )}
          </div>
        </div>

        <div className="mt-8 text-center">
          <button
            onClick={() => window.location.href = '/app'}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Volver a la aplicación
          </button>
        </div>
      </div>
    </div>
  );
}
