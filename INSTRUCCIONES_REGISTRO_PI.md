# OposIA - Instrucciones para Registro de Propiedad Intelectual

## 📋 Información del Proyecto

**Nombre:** OposIA  
**Descripción:** Sistema integral de preparación de oposiciones con Inteligencia Artificial  
**Tipo:** Aplicación web completa (Frontend + Backend + Base de datos)  
**Tecnología:** Next.js 14, React, TypeScript, Supabase, OpenAI, Stripe  

## 🚀 Inicio Rápido para Evaluación

### Opción 1: Inicio Automático (Recomendado)
1. Hacer doble clic en el archivo `INICIAR_APLICACION.bat`
2. Seguir las instrucciones en pantalla
3. La aplicación se abrirá automáticamente en http://localhost:3000

### Opción 2: Inicio Manual
```bash
# Instalar dependencias
npm install

# Iniciar servidor de desarrollo
npm run dev
```

## 📁 Estructura del Proyecto

```
OposIA/
├── 📄 INICIAR_APLICACION.bat          # Iniciador automático
├── 📄 INSTRUCCIONES_REGISTRO_PI.md    # Este archivo
├── 📄 README.md                       # Documentación técnica
├── 📄 package.json                    # Dependencias y scripts
├── 📁 src/                           # Código fuente principal
│   ├── 📁 app/                       # Rutas y páginas (App Router)
│   │   ├── 📁 api/                   # Endpoints de API
│   │   ├── 📁 auth/                  # Autenticación
│   │   ├── 📁 dashboard/             # Panel principal
│   │   ├── 📁 documents/             # Gestión de documentos
│   │   ├── 📁 tests/                 # Generación de tests
│   │   ├── 📁 flashcards/            # Sistema de flashcards
│   │   ├── 📁 mindmaps/              # Mapas mentales
│   │   ├── 📁 chat/                  # Chat tutor IA
│   │   ├── 📁 planning/              # Planificación de estudios
│   │   └── 📁 admin/                 # Panel administrativo
│   ├── 📁 components/                # Componentes reutilizables
│   ├── 📁 lib/                       # Librerías y utilidades
│   └── 📁 types/                     # Definiciones TypeScript
├── 📁 public/                        # Archivos estáticos
└── 📁 docs/                          # Documentación adicional
```

## 🔧 Requisitos del Sistema

- **Node.js:** Versión 18.x o superior
- **npm:** Incluido con Node.js
- **Navegador:** Chrome, Firefox, Safari, Edge (versiones recientes)
- **Sistema Operativo:** Windows, macOS, Linux

## 🌟 Características Principales

### 1. **Gestión de Documentos PDF**
- Subida y procesamiento de documentos
- Extracción de texto con IA
- Organización por temarios

### 2. **Generación de Contenido con IA**
- **Tests:** Generación automática de preguntas
- **Flashcards:** Tarjetas de estudio personalizadas
- **Mapas Mentales:** Visualización de conceptos
- **Resúmenes:** Síntesis inteligente de contenido

### 3. **Chat Tutor Inteligente**
- Asistente de IA especializado en oposiciones
- Respuestas contextuales basadas en documentos
- Historial de conversaciones

### 4. **Planificación de Estudios**
- Calendario inteligente de estudios
- Seguimiento de progreso
- Recordatorios automáticos

### 5. **Sistema de Autenticación**
- Registro y login seguro
- Gestión de perfiles de usuario
- Recuperación de contraseñas

### 6. **Sistema de Pagos**
- Integración con Stripe
- Múltiples planes de suscripción
- Gestión de facturación

### 7. **Panel Administrativo**
- Gestión de usuarios
- Estadísticas de uso
- Configuración del sistema

## 💳 Planes de Suscripción

1. **Plan Free** (Prueba 5 días)
   - Acceso limitado a funciones básicas
   - 1,000 tokens de IA

2. **Plan Usuario** (€10/mes)
   - Acceso completo excepto planificación
   - 50,000 tokens de IA

3. **Plan Pro** (€15/mes)
   - Acceso completo a todas las funciones
   - 100,000 tokens de IA
   - Planificación avanzada de estudios

## 🔒 Seguridad Implementada

- **Autenticación JWT** con Supabase
- **Row Level Security (RLS)** en base de datos
- **Validación server-side** en todos los endpoints
- **Protección CSRF** y sanitización de datos
- **Encriptación** de datos sensibles
- **Auditoría** de accesos administrativos

## 🏗️ Arquitectura Técnica

### Frontend
- **Next.js 14** con App Router
- **React 18** con TypeScript
- **Tailwind CSS** para estilos
- **Shadcn/ui** para componentes

### Backend
- **Next.js API Routes** para endpoints
- **Supabase** como base de datos PostgreSQL
- **OpenAI API** para funciones de IA
- **Stripe API** para pagos

### Servicios Externos
- **Supabase:** Base de datos y autenticación
- **OpenAI:** Procesamiento de IA
- **Stripe:** Procesamiento de pagos
- **Resend:** Envío de emails (opcional)

## 📊 Métricas de Desarrollo

- **Líneas de código:** ~15,000+ líneas
- **Componentes:** 50+ componentes React
- **Endpoints API:** 25+ endpoints
- **Páginas:** 20+ páginas funcionales
- **Tiempo de desarrollo:** 6+ meses
- **Funcionalidades:** 7 módulos principales

## 🧪 Testing y Calidad

- Tests de seguridad implementados
- Validación de vulnerabilidades
- Pruebas de integración con APIs
- Testing de componentes React

## 📝 Documentación Incluida

- **README.md:** Documentación técnica completa
- **API Documentation:** Documentación de endpoints
- **Component Documentation:** Guía de componentes
- **Deployment Guide:** Guía de despliegue

## 🎯 Innovación y Valor Único

1. **IA Especializada:** Entrenada específicamente para oposiciones
2. **Integración Completa:** Todas las herramientas en una plataforma
3. **Planificación Inteligente:** Algoritmos adaptativos de estudio
4. **Interfaz Intuitiva:** Diseño centrado en el usuario
5. **Escalabilidad:** Arquitectura preparada para crecimiento

## 📞 Información de Contacto

**Desarrollador:** [Nombre del desarrollador]  
**Email:** [Email de contacto]  
**Fecha de creación:** 2024  
**Versión:** 1.0.0  

---

**Nota:** Este proyecto representa una solución completa e innovadora para la preparación de oposiciones, combinando tecnologías modernas con inteligencia artificial para crear una experiencia de aprendizaje única y efectiva.
