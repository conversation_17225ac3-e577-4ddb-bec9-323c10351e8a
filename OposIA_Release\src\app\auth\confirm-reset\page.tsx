'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { FiShield, FiArrowRight, FiLoader, FiAlertTriangle } from 'react-icons/fi';

function ConfirmResetContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    // El token de reseteo de Supabase ahora vendrá en la URL como un parámetro llamado 'token'
    // (esto lo configuraremos en la plantilla de email de Supabase)
    const recoveryToken = searchParams.get('token');

    if (recoveryToken) {
      setToken(recoveryToken);
      setError(null);
    } else {
      console.error('🔐 [ConfirmReset] No se encontró el token de recuperación en la URL.');
      setError('El enlace de recuperación es inválido o está incompleto. Por favor, solicita un nuevo enlace.');
    }
  }, [searchParams]);

  const handleProceedToReset = () => {
    if (token && !isRedirecting) {
      setIsRedirecting(true);
      // Construimos la URL final a la que Supabase espera que se acceda
      // para procesar la recuperación de contraseña. Esta URL debe incluir
      // el token en el fragmento hash (#) como access_token.
      const appUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
      const finalResetUrl = `${appUrl}/auth/reset-password#access_token=${token}&type=recovery`;

      // Usamos window.location.href para asegurar que el fragmento hash se procese correctamente
      // al cargar la nueva página. router.push puede no manejar el fragmento hash
      // de la misma manera para este tipo de flujo de autenticación de Supabase.
      window.location.href = finalResetUrl;
    } else if (!token) {
      setError('No se puede proceder sin un token de recuperación válido.');
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-red-50 flex flex-col justify-center items-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <FiAlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Enlace Inválido</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Volver a Inicio de Sesión
          </button>
        </div>
      </div>
    );
  }

  if (!token && !error) { // Aún cargando el token desde searchParams
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
        <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />
        <h2 className="text-xl font-semibold text-gray-800">Verificando enlace...</h2>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        <FiShield className="w-16 h-16 text-blue-600 mx-auto mb-6" />
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Establece tu Contraseña
        </h1>
        <p className="text-gray-600 mb-8">
          ¡Bienvenido a OposIA! Estás a un paso de activar tu cuenta gratuita. Haz clic en el botón de abajo para establecer tu contraseña.
        </p>
        <button
          onClick={handleProceedToReset}
          disabled={isRedirecting || !token}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold text-lg flex items-center justify-center disabled:opacity-50"
        >
          {isRedirecting ? (
            <FiLoader className="animate-spin mr-2" />
          ) : (
            <FiArrowRight className="mr-2" />
          )}
          {isRedirecting ? 'Redirigiendo...' : 'Establecer mi Contraseña'}
        </button>
        {isRedirecting && (
          <p className="text-sm text-gray-500 mt-4">
            Si no eres redirigido automáticamente, por favor verifica que las ventanas emergentes no estén bloqueadas.
          </p>
        )}
      </div>
      <p className="text-sm text-gray-500 mt-8">
        Este paso ayuda a proteger tu cuenta y completar tu registro.
      </p>
    </div>
  );
}

// Es buena práctica envolver con Suspense si usas useSearchParams
export default function ConfirmResetPageWrapper() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
        <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />
        <h2 className="text-xl font-semibold text-gray-800">Cargando...</h2>
      </div>
    }>
      <ConfirmResetContent />
    </Suspense>
  );
}
