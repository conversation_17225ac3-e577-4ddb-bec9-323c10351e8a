import { createServerSupabaseClient } from './server';
import { TokenTrackingData } from '@/lib/ai/tokenTracker';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/config/plans';

/**
 * Guarda el uso de tokens en Supabase (versión servidor)
 */
export async function saveTokenUsageServer(data: TokenTrackingData): Promise<void> {
  try {
    console.log('🔄 saveTokenUsageServer iniciado con data:', data);
    
    // Crear cliente de Supabase para el servidor
    const supabase = await createServerSupabaseClient();
    console.log('✅ Cliente Supabase del servidor creado');

    // Obtener el usuario actual
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');

    if (userError || !user) {
      console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);
      return;
    }

    const usageRecord = {
      user_id: user.id,
      activity_type: data.activity,
      model_name: data.model,
      prompt_tokens: data.usage.promptTokens,
      completion_tokens: data.usage.completionTokens,
      total_tokens: data.usage.totalTokens,
      estimated_cost: data.usage.estimatedCost || 0,
      usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01
    };

    console.log('📝 Registro a insertar:', usageRecord);

    const { error } = await supabase
      .from('user_token_usage')
      .insert([usageRecord]);

    if (error) {
      console.error('❌ Error al guardar uso de tokens:', error);
      return;
    }

    console.log('✅ Registro insertado exitosamente en user_token_usage');

    // Validar límites antes de actualizar contador
    const canUpdate = await validateTokenLimits(supabase, user.id, data.usage.totalTokens);

    if (!canUpdate.allowed) {
      console.warn('⚠️ Límite de tokens alcanzado:', canUpdate.reason);
      // Aún así guardamos el registro para auditoría, pero marcamos el exceso
      return;
    }

    // Actualizar contador mensual del usuario
    await updateMonthlyTokenCount(supabase, user.id, data.usage.totalTokens);

  } catch (error) {
    console.error('❌ Error en saveTokenUsageServer:', error);
  }
}

/**
 * Actualiza el contador mensual de tokens del usuario
 */
async function updateMonthlyTokenCount(supabase: any, userId: string, tokens: number): Promise<void> {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    // Obtener o crear perfil del usuario
    let { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error al obtener perfil:', profileError);
      return;
    }

    if (!profile) {
      // Crear perfil nuevo con límites dinámicos
      const defaultPlan = 'free';
      const tokenLimit = getTokenLimitForPlan(defaultPlan);

      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: userId,
          subscription_plan: defaultPlan,
          monthly_token_limit: tokenLimit,
          current_month_tokens: tokens,
          current_month: currentMonth,
          payment_verified: false
        }]);

      if (insertError) {
        console.error('Error al crear perfil:', insertError);
      } else {
        console.log('✅ Perfil de usuario creado con límite dinámico:', tokenLimit);
      }
    } else {
      // Actualizar perfil existente
      const newTokenCount = profile.current_month === currentMonth 
        ? profile.current_month_tokens + tokens 
        : tokens; // Reset si es nuevo mes

      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          current_month_tokens: newTokenCount,
          current_month: currentMonth,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error al actualizar perfil:', updateError);
      } else {
        console.log('✅ Perfil de usuario actualizado');
      }
    }
  } catch (error) {
    console.error('Error en updateMonthlyTokenCount:', error);
  }
}

/**
 * Valida si el usuario puede usar la cantidad de tokens especificada
 */
async function validateTokenLimits(
  supabase: any,
  userId: string,
  tokensToUse: number
): Promise<{ allowed: boolean; reason?: string; currentUsage?: any }> {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    // Obtener perfil del usuario
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('subscription_plan, monthly_token_limit, current_month_tokens, current_month, payment_verified')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return { allowed: false, reason: 'Perfil de usuario no encontrado' };
    }

    // Verificar pago para planes de pago
    if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
      return { allowed: false, reason: 'Pago no verificado' };
    }

    // Calcular tokens actuales (reset si es nuevo mes)
    let currentTokens = profile.current_month === currentMonth
      ? profile.current_month_tokens
      : 0;

    // Verificar límite
    if (currentTokens + tokensToUse > profile.monthly_token_limit) {
      return {
        allowed: false,
        reason: `Límite mensual alcanzado: ${currentTokens + tokensToUse}/${profile.monthly_token_limit}`,
        currentUsage: {
          current: currentTokens,
          limit: profile.monthly_token_limit,
          requested: tokensToUse,
          plan: profile.subscription_plan
        }
      };
    }

    return {
      allowed: true,
      currentUsage: {
        current: currentTokens,
        limit: profile.monthly_token_limit,
        remaining: profile.monthly_token_limit - currentTokens - tokensToUse
      }
    };

  } catch (error) {
    console.error('Error validating token limits:', error);
    return { allowed: false, reason: 'Error de validación' };
  }
}

/**
 * Obtiene estadísticas de uso de tokens del usuario
 */
export async function getTokenUsageStats(userId: string): Promise<{
  currentMonth: {
    used: number;
    limit: number;
    percentage: number;
    remaining: number;
  };
  plan: {
    name: string;
    features: string[];
  };
  paymentVerified: boolean;
} | null> {
  try {
    const supabase = await createServerSupabaseClient();
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !profile) {
      return null;
    }

    // Reset si es nuevo mes
    let currentTokens = profile.current_month === currentMonth
      ? profile.current_month_tokens
      : 0;

    const planConfig = getPlanConfiguration(profile.subscription_plan);
    const percentage = profile.monthly_token_limit > 0
      ? (currentTokens / profile.monthly_token_limit) * 100
      : 0;

    return {
      currentMonth: {
        used: currentTokens,
        limit: profile.monthly_token_limit,
        percentage: Math.round(percentage),
        remaining: profile.monthly_token_limit - currentTokens
      },
      plan: {
        name: planConfig?.name || profile.subscription_plan,
        features: planConfig?.features || []
      },
      paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'
    };

  } catch (error) {
    console.error('Error getting token usage stats:', error);
    return null;
  }
}

/**
 * Actualiza los límites de tokens cuando cambia el plan del usuario
 */
export async function updateUserPlanLimits(
  userId: string,
  newPlan: 'free' | 'usuario' | 'pro'
): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    const newTokenLimit = getTokenLimitForPlan(newPlan);

    const { error } = await supabase
      .from('user_profiles')
      .update({
        subscription_plan: newPlan,
        monthly_token_limit: newTokenLimit,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user plan limits:', error);
      return false;
    }

    console.log(`✅ Plan actualizado para usuario ${userId}: ${newPlan} (${newTokenLimit} tokens)`);
    return true;

  } catch (error) {
    console.error('Error updating user plan limits:', error);
    return false;
  }
}
