/**
 * Modal/Drawer para mostrar el calendario en dispositivos móviles
 */

import React, { useEffect } from 'react';
import { FiX } from 'react-icons/fi';
import PlanCalendario from './PlanCalendario';
import TareasDelDia from './TareasDelDia';
import { PlanEstudiosEstructurado } from '../services/planGeneratorService';
import { ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';
import { TareaDelDia } from '../types/calendarTypes';

interface CalendarioModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: PlanEstudiosEstructurado;
  progresoPlan: ProgresoPlanEstudios[];
  fechaSeleccionada: Date | null;
  onFechaSeleccionada: (fecha: Date) => void;
  tareasDelDia: TareaDelDia[];
  onTareaClick?: (tarea: TareaDelDia) => void;
  onTareaCompletadaChange?: (tarea: TareaDelDia, completada: boolean) => Promise<void>;
}

const CalendarioModal: React.FC<CalendarioModalProps> = ({
  isOpen,
  onClose,
  plan,
  progresoPlan,
  fechaSeleccionada,
  onFechaSeleccionada,
  tareasDelDia,
  onTareaClick,
  onTareaCompletadaChange
}) => {

  // Cerrar modal con Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevenir scroll del body
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Manejar clic en tarea y cerrar modal
  const handleTareaClick = (tarea: TareaDelDia) => {
    if (onTareaClick) {
      onTareaClick(tarea);
    }
    onClose(); // Cerrar modal después de seleccionar tarea
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden calendario-modal-overlay"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 lg:hidden">
        <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          {/* Drawer desde abajo en móvil, modal centrado en tablet */}
          <div className="inline-block align-bottom bg-white rounded-t-lg sm:rounded-lg text-left overflow-hidden shadow-xl calendario-modal-content sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            {/* Header */}
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Calendario del Plan
              </h3>
              <button
                onClick={onClose}
                className="p-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors"
                aria-label="Cerrar calendario"
              >
                <FiX className="w-5 h-5 text-gray-600" />
              </button>
            </div>

            {/* Contenido */}
            <div className="bg-white px-4 py-4 space-y-4 max-h-[70vh] overflow-y-auto">
              {/* Calendario */}
              <PlanCalendario
                plan={plan}
                progresoPlan={progresoPlan}
                fechaSeleccionada={fechaSeleccionada}
                onFechaSeleccionada={onFechaSeleccionada}
              />

              {/* Tareas del día seleccionado */}
              {fechaSeleccionada && (
                <TareasDelDia
                  fecha={fechaSeleccionada}
                  tareas={tareasDelDia}
                  onTareaClick={handleTareaClick}
                  onTareaCompletadaChange={onTareaCompletadaChange}
                />
              )}
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
              <button
                onClick={onClose}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CalendarioModal;
