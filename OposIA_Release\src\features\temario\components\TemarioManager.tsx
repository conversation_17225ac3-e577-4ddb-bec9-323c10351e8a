import React, { useState, useEffect } from 'react';
import { FiBook, FiEdit, FiCheck, FiTrendingUp, FiZap, FiSettings, FiTrash2 } from 'react-icons/fi';
import {
  obtenerTemarioUsuario,
  obtenerTemas,
  actualizarEstadoTema,
  obtenerEstadisticasTemario,
  eliminarTemario
} from '../services/temarioService';
import { tienePlanificacionConfigurada } from '@/features/planificacion/services/planificacionService';
import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/lib/supabase/authService';
import { Temario, Tema } from '@/lib/supabase/supabaseClient';
import { toast } from 'react-hot-toast';
import PlanificacionAsistente from '@/features/planificacion/components/PlanificacionAsistente';
import TemarioEditModal from './TemarioEditModal';
import TemaEditModal from './TemaEditModal';
import TemaActions from './TemaActions';

const TemarioManager: React.FC = () => {
  const [temario, setTemario] = useState<Temario | null>(null);
  const [temas, setTemas] = useState<Tema[]>([]);
  const [estadisticas, setEstadisticas] = useState<{
    totalTemas: number;
    temasCompletados: number;
    porcentajeCompletado: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [actualizandoTema, setActualizandoTema] = useState<string | null>(null);
  const [tienePlanificacion, setTienePlanificacion] = useState<boolean>(false);
  const [mostrarAsistentePlanificacion, setMostrarAsistentePlanificacion] = useState(false);
  const [mostrarModalEdicion, setMostrarModalEdicion] = useState(false);
  const [mostrarModalEdicionTema, setMostrarModalEdicionTema] = useState(false);
  const [temaSeleccionado, setTemaSeleccionado] = useState<Tema | null>(null);
  const [mostrarConfirmacionEliminar, setMostrarConfirmacionEliminar] = useState(false);
  const [eliminandoTemario, setEliminandoTemario] = useState(false);

  useEffect(() => {
    cargarDatos();
  }, []);

  const cargarDatos = async () => {
    setIsLoading(true);
    try {
      const temarioData = await obtenerTemarioUsuario();
      if (temarioData) {
        setTemario(temarioData);

        const [temasData, estadisticasData, planificacionConfigurada] = await Promise.all([
          obtenerTemas(temarioData.id),
          obtenerEstadisticasTemario(temarioData.id),
          tienePlanificacionConfigurada(temarioData.id)
        ]);

        setTemas(temasData);
        setEstadisticas(estadisticasData);
        setTienePlanificacion(planificacionConfigurada);
      }
    } catch (error) {
      console.error('Error al cargar datos del temario:', error);
      toast.error('Error al cargar el temario');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleCompletado = async (temaId: string, completado: boolean) => {
    setActualizandoTema(temaId);
    try {
      const exito = await actualizarEstadoTema(temaId, !completado);
      if (exito) {
        // Actualizar el estado local
        setTemas(temas.map(tema => 
          tema.id === temaId 
            ? { 
                ...tema, 
                completado: !completado,
                fecha_completado: !completado ? new Date().toISOString() : undefined
              }
            : tema
        ));
        
        // Recalcular estadísticas
        if (temario) {
          const nuevasEstadisticas = await obtenerEstadisticasTemario(temario.id);
          setEstadisticas(nuevasEstadisticas);
        }
        
        toast.success(!completado ? 'Tema marcado como completado' : 'Tema marcado como pendiente');
      } else {
        toast.error('Error al actualizar el estado del tema');
      }
    } catch (error) {
      console.error('Error al actualizar tema:', error);
      toast.error('Error al actualizar el tema');
    } finally {
      setActualizandoTema(null);
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handleIniciarPlanificacion = () => {
    setMostrarAsistentePlanificacion(true);
  };

  const handleModificarPlanificacion = () => {
    setMostrarAsistentePlanificacion(true);
  };

  const handleCompletarPlanificacion = () => {
    setMostrarAsistentePlanificacion(false);
    setTienePlanificacion(true);
    // Recargar datos para reflejar los cambios
    cargarDatos();
  };

  const handleCancelarPlanificacion = () => {
    setMostrarAsistentePlanificacion(false);
  };

  const handleEditarTemario = () => {
    setMostrarModalEdicion(true);
  };

  const handleGuardarTemario = (temarioActualizado: Temario) => {
    setTemario(temarioActualizado);
    setMostrarModalEdicion(false);
  };

  const handleCancelarEdicion = () => {
    setMostrarModalEdicion(false);
  };

  const handleEditarTema = (tema: Tema) => {
    setTemaSeleccionado(tema);
    setMostrarModalEdicionTema(true);
  };

  const handleGuardarTema = (temaActualizado: Tema) => {
    setTemas(temas.map(tema =>
      tema.id === temaActualizado.id ? temaActualizado : tema
    ));
    setMostrarModalEdicionTema(false);
    setTemaSeleccionado(null);
  };

  const handleCancelarEdicionTema = () => {
    setMostrarModalEdicionTema(false);
    setTemaSeleccionado(null);
  };

  const handleEliminarTema = async (temaId: string) => {
    // Eliminar tema del estado local
    setTemas(temas.filter(tema => tema.id !== temaId));

    // Recalcular estadísticas
    if (temario) {
      const nuevasEstadisticas = await obtenerEstadisticasTemario(temario.id);
      setEstadisticas(nuevasEstadisticas);
    }
  };

  const handleEliminarTemario = () => {
    setMostrarConfirmacionEliminar(true);
  };

  const handleConfirmarEliminarTemario = async () => {
    if (!temario) return;

    setEliminandoTemario(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Eliminando temario y desactivando plan de estudios...');

      // 1. Eliminar planificación si existe
      const { user } = await obtenerUsuarioActual();
      if (user) {
        await supabase
          .from('planificacion_usuario')
          .delete()
          .eq('user_id', user.id)
          .eq('temario_id', temario.id);
      }

      // 2. Desactivar planes de estudios si existen (en lugar de eliminarlos)
      await supabase
        .from('planes_estudios')
        .update({ activo: false })
        .eq('temario_id', temario.id);

      // 3. Eliminar el temario (esto eliminará automáticamente los temas por CASCADE)
      const success = await eliminarTemario(temario.id);

      if (success) {
        toast.success('Temario eliminado y plan de estudios desactivado exitosamente', { id: loadingToastId });
        // Limpiar estado y recargar
        setTemario(null);
        setTemas([]);
        setEstadisticas(null);
        setTienePlanificacion(false);
      } else {
        toast.error('Error al eliminar el temario', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al eliminar temario:', error);
      toast.error('Error al eliminar el temario', { id: loadingToastId });
    } finally {
      setEliminandoTemario(false);
      setMostrarConfirmacionEliminar(false);
    }
  };

  const handleCancelarEliminarTemario = () => {
    setMostrarConfirmacionEliminar(false);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!temario) {
    return (
      <div className="text-center py-12">
        <FiBook className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No hay temario configurado</h3>
        <p className="text-gray-500">Configura tu temario desde el dashboard para comenzar.</p>
      </div>
    );
  }

  // Mostrar asistente de planificación si está activo
  if (mostrarAsistentePlanificacion) {
    return (
      <PlanificacionAsistente
        temario={temario}
        onComplete={handleCompletarPlanificacion}
        onCancel={handleCancelarPlanificacion}
        isEditing={tienePlanificacion}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header del temario */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{temario.titulo}</h1>
            {temario.descripcion && (
              <p className="text-gray-600">{temario.descripcion}</p>
            )}
            <div className="flex items-center mt-2 space-x-4">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                temario.tipo === 'completo' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-orange-100 text-orange-800'
              }`}>
                {temario.tipo === 'completo' ? 'Temario Completo' : 'Temas Sueltos'}
              </span>
              <span className="text-sm text-gray-500">
                Creado el {formatearFecha(temario.creado_en)}
              </span>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleEditarTemario}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              title="Editar temario"
            >
              <FiEdit className="w-5 h-5" />
            </button>
            <button
              onClick={handleEliminarTemario}
              className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors"
              title="Eliminar temario"
            >
              <FiTrash2 className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Estadísticas */}
        {estadisticas && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <FiBook className="w-5 h-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Total Temas</p>
                  <p className="text-2xl font-bold text-blue-600">{estadisticas.totalTemas}</p>
                </div>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <FiCheck className="w-5 h-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-green-800">Completados</p>
                  <p className="text-2xl font-bold text-green-600">{estadisticas.temasCompletados}</p>
                </div>
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center">
                <FiTrendingUp className="w-5 h-5 text-purple-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-purple-800">Progreso</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {estadisticas.porcentajeCompletado.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Barra de progreso */}
        {estadisticas && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progreso del temario</span>
              <span>{estadisticas.porcentajeCompletado.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${estadisticas.porcentajeCompletado}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      {/* Sección de Planificación Inteligente */}
      {temario.tipo === 'completo' && (
        <div className={`border rounded-xl p-6 ${
          tienePlanificacion
            ? 'bg-green-50 border-green-200'
            : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-start justify-between">
            <div className="flex items-start">
              {tienePlanificacion ? (
                <FiCheck className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
              ) : (
                <FiZap className="w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
              )}
              <div>
                <h3 className={`text-lg font-medium mb-2 ${
                  tienePlanificacion ? 'text-green-900' : 'text-blue-900'
                }`}>
                  {tienePlanificacion ? 'Planificación Configurada' : 'Planificación Inteligente con IA'}
                </h3>
                <p className={`text-sm mb-3 ${
                  tienePlanificacion ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {tienePlanificacion
                    ? 'Ya tienes configurada tu planificación de estudio personalizada. Pronto podrás ver tu calendario y seguimiento.'
                    : 'Configura tu planificación personalizada con nuestro asistente inteligente:'
                  }
                </p>
                {!tienePlanificacion && (
                  <ul className="text-blue-800 text-sm space-y-1">
                    <li>• Planificación automática de estudio con IA</li>
                    <li>• Seguimiento de progreso personalizado</li>
                    <li>• Recomendaciones de orden de estudio</li>
                    <li>• Estimación de tiempos por tema</li>
                  </ul>
                )}
              </div>
            </div>

            <div className="flex gap-2">
              {!tienePlanificacion ? (
                <button
                  onClick={handleIniciarPlanificacion}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FiZap className="w-4 h-4 mr-2" />
                  Configurar Planificación
                </button>
              ) : (
                <button
                  onClick={handleModificarPlanificacion}
                  className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <FiSettings className="w-4 h-4 mr-2" />
                  Modificar Planificación
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Lista de temas */}
      <div className="bg-white rounded-xl shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Temas del Temario</h2>
          <p className="text-sm text-gray-500 mt-1">
            Marca los temas como completados según vayas estudiándolos
          </p>
        </div>
        
        <div className="divide-y divide-gray-200">
          {temas.map((tema) => (
            <div key={tema.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
                      {tema.numero}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className={`text-lg font-medium ${tema.completado ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                      {tema.titulo}
                    </h3>
                    {tema.descripcion && (
                      <p className="text-sm text-gray-600 mt-1">{tema.descripcion}</p>
                    )}
                    {tema.fecha_completado && (
                      <div className="flex items-center mt-2 text-sm text-green-600">
                        <FiCheck className="w-4 h-4 mr-1" />
                        Completado el {formatearFecha(tema.fecha_completado)}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <TemaActions
                    tema={tema}
                    onEdit={handleEditarTema}
                    onDelete={handleEliminarTema}
                    onToggleCompletado={handleToggleCompletado}
                    isUpdating={actualizandoTema === tema.id}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal de edición del temario */}
      {temario && (
        <TemarioEditModal
          isOpen={mostrarModalEdicion}
          onClose={handleCancelarEdicion}
          temario={temario}
          onSave={handleGuardarTemario}
        />
      )}

      {/* Modal de edición de tema */}
      {temaSeleccionado && (
        <TemaEditModal
          isOpen={mostrarModalEdicionTema}
          onClose={handleCancelarEdicionTema}
          tema={temaSeleccionado}
          onSave={handleGuardarTema}
        />
      )}

      {/* Modal de confirmación de eliminación */}
      {mostrarConfirmacionEliminar && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <FiTrash2 className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">
                  Eliminar Temario
                </h3>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-3">
                ¿Estás seguro de que quieres eliminar este temario? Esta acción:
              </p>
              <ul className="text-sm text-red-600 space-y-1 ml-4">
                <li>• Eliminará permanentemente el temario "<strong>{temario?.titulo}</strong>"</li>
                <li>• Eliminará todos los temas asociados</li>
                <li>• Eliminará la planificación de estudios configurada</li>
                <li>• Desactivará los planes de estudios generados (se conservan en el historial)</li>
              </ul>
              <p className="text-sm text-gray-600 mt-3 font-medium">
                Esta acción no se puede deshacer.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelarEliminarTemario}
                disabled={eliminandoTemario}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"
              >
                Cancelar
              </button>
              <button
                onClick={handleConfirmarEliminarTemario}
                disabled={eliminandoTemario}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center"
              >
                {eliminandoTemario ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Eliminando...
                  </>
                ) : (
                  <>
                    <FiTrash2 className="w-4 h-4 mr-2" />
                    Eliminar Temario
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemarioManager;
