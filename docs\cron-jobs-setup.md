# Configuración de Cron Jobs para Períodos de Gracia

Este documento explica cómo configurar los cron jobs necesarios para el manejo automático de períodos de gracia en suscripciones canceladas.

## 📋 Cron Jobs Requeridos

### 1. **Procesamiento de Períodos de Gracia Expirados**
**Frecuencia**: Di<PERSON> (recomendado: 2:00 AM)
**Endpoint**: `POST /api/admin/process-expired-grace-periods`
**Propósito**: Degradar usuarios cuyo período de gracia haya expirado

### 2. **Envío de Recordatorios de Período de Gracia**
**Frecuencia**: Diario (recomendado: 10:00 AM)
**Endpoint**: `POST /api/admin/send-grace-period-reminders`
**Propósito**: Enviar recordatorios a usuarios cuyo período de gracia está por terminar

### 3. **Reintentos de Notificaciones Fallidas**
**Frecuencia**: Cada 6 horas (opcional)
**Endpoint**: `POST /api/admin/email-failures`
**Propósito**: Reintentar envío de notificaciones que fallaron

## 🔧 Opciones de Configuración

### Opción 1: Vercel Cron Jobs (Recomendado para Vercel)

1. **Crear archivo `vercel.json` en la raíz del proyecto:**

```json
{
  "crons": [
    {
      "path": "/api/admin/process-expired-grace-periods",
      "schedule": "0 2 * * *"
    },
    {
      "path": "/api/admin/send-grace-period-reminders",
      "schedule": "0 10 * * *"
    },
    {
      "path": "/api/admin/email-failures",
      "schedule": "0 */6 * * *"
    }
  ]
}
```

2. **Configurar variable de entorno:**
```env
CRON_SECRET=tu_clave_secreta_muy_segura_aqui
```

3. **Desplegar a Vercel** - Los cron jobs se activarán automáticamente

### Opción 2: GitHub Actions

1. **Crear archivo `.github/workflows/cron-jobs.yml`:**

```yaml
name: Cron Jobs

on:
  schedule:
    # Procesar períodos expirados a las 2:00 AM UTC diariamente
    - cron: '0 2 * * *'
    # Enviar recordatorios a las 10:00 AM UTC diariamente  
    - cron: '0 10 * * *'
  workflow_dispatch: # Permite ejecución manual

jobs:
  process-expired-grace-periods:
    if: github.event.schedule == '0 2 * * *' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    steps:
      - name: Process Expired Grace Periods
        run: |
          curl -X POST ${{ secrets.APP_URL }}/api/admin/process-expired-grace-periods \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json"

  send-grace-period-reminders:
    if: github.event.schedule == '0 10 * * *' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    steps:
      - name: Send Grace Period Reminders
        run: |
          curl -X POST ${{ secrets.APP_URL }}/api/admin/send-grace-period-reminders \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json" \
            -d '{"hoursBeforeExpiration": 24}'
```

2. **Configurar secrets en GitHub:**
   - `APP_URL`: URL de tu aplicación (ej: https://tu-app.vercel.app)
   - `CRON_SECRET`: La misma clave secreta configurada en tu app

### Opción 3: EasyCron (Servicio Externo)

1. **Registrarse en [EasyCron](https://www.easycron.com/)**

2. **Crear cron job para períodos expirados:**
   - URL: `https://tu-app.com/api/admin/process-expired-grace-periods`
   - Método: POST
   - Headers: `Authorization: Bearer TU_CRON_SECRET`
   - Horario: `0 2 * * *` (diario a las 2:00 AM)

3. **Crear cron job para recordatorios:**
   - URL: `https://tu-app.com/api/admin/send-grace-period-reminders`
   - Método: POST
   - Headers: `Authorization: Bearer TU_CRON_SECRET`
   - Body: `{"hoursBeforeExpiration": 24}`
   - Horario: `0 10 * * *` (diario a las 10:00 AM)

### Opción 4: Servidor Propio con Crontab

```bash
# Editar crontab
crontab -e

# Agregar estas líneas:
# Procesar períodos expirados a las 2:00 AM diariamente
0 2 * * * curl -X POST https://tu-app.com/api/admin/process-expired-grace-periods -H "Authorization: Bearer TU_CRON_SECRET"

# Enviar recordatorios a las 10:00 AM diariamente
0 10 * * * curl -X POST https://tu-app.com/api/admin/send-grace-period-reminders -H "Authorization: Bearer TU_CRON_SECRET" -H "Content-Type: application/json" -d '{"hoursBeforeExpiration": 24}'
```

## 🔐 Configuración de Seguridad

### Variables de Entorno Requeridas

```env
# Clave secreta para autenticación de cron jobs
CRON_SECRET=genera_una_clave_muy_segura_aqui

# URLs de la aplicación (para emails)
NEXT_PUBLIC_APP_URL=https://tu-app.com

# Configuración de Supabase (ya existentes)
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=tu_service_role_key
```

### Generar Clave Secreta Segura

```bash
# Opción 1: OpenSSL
openssl rand -base64 32

# Opción 2: Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Opción 3: Online
# Usar https://generate-secret.vercel.app/32
```

## 📊 Monitoreo y Logs

### Verificar Funcionamiento

1. **Logs de ejecución:**
```bash
# Ver logs en Vercel
vercel logs

# Ver logs en GitHub Actions
# Ir a Actions tab en tu repositorio
```

2. **Endpoint de estadísticas:**
```bash
# Obtener estadísticas de períodos de gracia
GET /api/admin/process-expired-grace-periods

# Obtener estadísticas de recordatorios
GET /api/admin/send-grace-period-reminders
```

3. **Ejecución manual para pruebas:**
```bash
# Procesar períodos expirados manualmente
curl -X POST https://tu-app.com/api/admin/process-expired-grace-periods \
  -H "Authorization: Bearer TU_CRON_SECRET"

# Enviar recordatorios manualmente
curl -X POST https://tu-app.com/api/admin/send-grace-period-reminders \
  -H "Authorization: Bearer TU_CRON_SECRET" \
  -H "Content-Type: application/json" \
  -d '{"hoursBeforeExpiration": 1}'
```

## ⚠️ Consideraciones Importantes

1. **Zona Horaria**: Los horarios están en UTC. Ajusta según tu zona horaria.

2. **Frecuencia**: 
   - Procesamiento diario es suficiente para la mayoría de casos
   - Para mayor precisión, puedes ejecutar cada 6-12 horas

3. **Límites de Rate**:
   - Los endpoints procesan máximo 100 usuarios por ejecución
   - Para volúmenes altos, considera ejecutar más frecuentemente

4. **Monitoreo**:
   - Configura alertas si los cron jobs fallan
   - Revisa logs regularmente para detectar problemas

5. **Backup**:
   - Siempre ten un método manual de respaldo
   - Los endpoints funcionan con autenticación de admin también

## 🚀 Próximos Pasos

1. **Elegir opción de cron jobs** según tu infraestructura
2. **Configurar variables de entorno**
3. **Probar ejecución manual** antes de activar cron jobs
4. **Monitorear logs** las primeras semanas
5. **Configurar alertas** para fallos de cron jobs

## 📞 Soporte

Si tienes problemas con la configuración:
1. Verifica que las variables de entorno estén correctas
2. Prueba la ejecución manual primero
3. Revisa los logs de la aplicación
4. Verifica que los endpoints respondan correctamente
