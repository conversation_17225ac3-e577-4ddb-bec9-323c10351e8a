import React from 'react';

interface EstadisticasFlashcard {
  total: number;
  nuevas: number;
  aprendiendo: number;
  repasando: number;
  aprendidas: number;
  paraHoy: number;
}

interface FlashcardStatisticsProps {
  estadisticas: EstadisticasFlashcard | null;
}

const FlashcardStatistics: React.FC<FlashcardStatisticsProps> = ({ estadisticas }) => {
  if (!estadisticas) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="font-semibold text-lg mb-2">Estadísticas</h3>
      <div className="flex flex-wrap gap-2">
        <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
          Total: {estadisticas.total} tarjetas
        </div>
        <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
          Nuevas: {estadisticas.nuevas}
        </div>
        <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
          Aprendiendo: {estadisticas.aprendiendo}
        </div>
        <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
          Repasando: {estadisticas.repasando}
        </div>
        <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
          Aprendidas: {estadisticas.aprendidas}
        </div>
        <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
          Para hoy: {estadisticas.paraHoy}
        </div>
      </div>
    </div>
  );
};

export default FlashcardStatistics;
