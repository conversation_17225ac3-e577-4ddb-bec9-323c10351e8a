import React, { useState } from 'react';
import { FiEdit, FiTrash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lock, FiAlertTriangle } from 'react-icons/fi';
import { Tema } from '@/lib/supabase/supabaseClient';
import { eliminarTema } from '../services/temarioService';
import { toast } from 'react-hot-toast';

interface TemaActionsProps {
  tema: Tema;
  onEdit: (tema: Tema) => void;
  onDelete: (temaId: string) => void;
  onToggleCompletado: (temaId: string, completado: boolean) => void;
  isUpdating: boolean;
}

const TemaActions: React.FC<TemaActionsProps> = ({
  tema,
  onEdit,
  onDelete,
  onToggleCompletado,
  isUpdating
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleEdit = () => {
    onEdit(tema);
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Eliminando tema...');

      const success = await eliminarTema(tema.id);

      if (success) {
        toast.success('Tema eliminado exitosamente', { id: loadingToastId });
        onDelete(tema.id);
      } else {
        toast.error('Error al eliminar el tema', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al eliminar tema:', error);
      toast.error('Error al eliminar el tema', { id: loadingToastId });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
  };

  const handleToggleCompletado = () => {
    onToggleCompletado(tema.id, tema.completado);
  };

  if (showDeleteConfirm) {
    return (
      <div className="flex items-center space-x-2 bg-red-50 border border-red-200 rounded-lg p-3">
        <FiAlertTriangle className="w-4 h-4 text-red-600 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <p className="text-sm text-red-800">
            ¿Eliminar tema "{tema.titulo}"?
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleDeleteCancel}
            className="px-2 py-1 text-xs text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            disabled={isDeleting}
          >
            Cancelar
          </button>
          <button
            onClick={handleDeleteConfirm}
            disabled={isDeleting}
            className="px-2 py-1 text-xs text-white bg-red-600 rounded hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b border-white mr-1"></div>
                Eliminando...
              </>
            ) : (
              'Eliminar'
            )}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      {/* Botón de editar */}
      <button
        onClick={handleEdit}
        className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
        title="Editar tema"
        disabled={isUpdating}
      >
        <FiEdit className="w-4 h-4" />
      </button>

      {/* Botón de eliminar */}
      <button
        onClick={handleDeleteClick}
        className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors"
        title="Eliminar tema"
        disabled={isUpdating}
      >
        <FiTrash2 className="w-4 h-4" />
      </button>

      {/* Botón de completado */}
      <button
        onClick={handleToggleCompletado}
        disabled={isUpdating}
        className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
          tema.completado
            ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            : 'bg-green-100 text-green-700 hover:bg-green-200'
        } disabled:opacity-50 disabled:cursor-not-allowed`}
      >
        {isUpdating ? (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1"></div>
        ) : tema.completado ? (
          <FiClock className="w-4 h-4 mr-1" />
        ) : (
          <FiCheck className="w-4 h-4 mr-1" />
        )}
        {tema.completado ? 'Marcar pendiente' : 'Marcar completado'}
      </button>
    </div>
  );
};

export default TemaActions;
