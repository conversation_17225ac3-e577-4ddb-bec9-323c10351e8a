// src/app/api/stripe/create-checkout-session/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { stripe, getPlanById, isValidPlan, APP_URLS } from '@/lib/stripe/config';

export async function POST(request: NextRequest) {
  try {
    console.log('Stripe API called');

    // --- INICIO: CONTROL DE ACCESO ---
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No necesitamos setear cookies en este endpoint
          },
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.warn('Acceso denegado a create-checkout-session: Usuario no autenticado.');
      return NextResponse.json({
        error: 'No autorizado. Debes iniciar sesión para realizar un pago.'
      }, { status: 401 });
    }
    // --- FIN: CONTROL DE ACCESO ---

    const body = await request.json();
    console.log('Request body:', body);

    // Obtener userId del usuario autenticado (NO del cliente)
    const userId = user.id;
    const { planId, email, customerName, registrationData } = body;

    // Validaciones básicas
    if (!planId || !email) {
      console.log('Missing planId or email');
      return NextResponse.json({
        error: 'Plan ID y email son requeridos'
      }, { status: 400 });
    }

    console.log('Validating plan:', planId);
    
    if (!isValidPlan(planId)) {
      console.log('Invalid plan ID:', planId);
      return NextResponse.json({
        error: 'Plan ID no válido'
      }, { status: 400 });
    }

    const plan = getPlanById(planId);
    console.log('Plan found:', plan);
    
    if (!plan) {
      console.log('Plan not found');
      return NextResponse.json({
        error: 'Plan no encontrado'
      }, { status: 404 });
    }

    // El plan gratuito no requiere pago
    if (planId === 'free') {
      console.log('Free plan does not require payment');
      return NextResponse.json({
        error: 'El plan gratuito no requiere pago'
      }, { status: 400 });
    }

    console.log('Creating checkout session for:', { planId, email, customerName });

    // Usar el precio fijo configurado
    const priceId = plan.stripePriceId;
    
    if (!priceId) {
      console.log('No price ID configured for plan:', planId);
      return NextResponse.json({
        error: 'Precio no configurado para este plan'
      }, { status: 500 });
    }

    console.log('Using price ID:', priceId);

    // Determinar el modo de pago basado en el plan
    // Ahora 'usuario' y 'pro' son recurrentes (suscripciones mensuales)
    const isRecurring = planId === 'pro' || planId === 'usuario';
    const mode = isRecurring ? 'subscription' : 'payment';

    console.log('Payment mode:', mode, 'for plan:', planId);

    // Crear sesión de checkout
    const sessionConfig: any = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: mode,
      customer_email: email,
      client_reference_id: planId,
      metadata: {
        planId: planId,
        customerEmail: email,
        customerName: customerName || '',
        userId: userId, // ID del usuario autenticado obtenido del servidor
        registrationData: registrationData ? JSON.stringify(registrationData) : '', // Datos de registro para crear cuenta después del pago
        createdAt: new Date().toISOString(),
        source: 'OposiAI_website',
        autoActivate: 'true'
      },
      success_url: `${APP_URLS.success}?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,
      cancel_url: `${APP_URLS.cancel}?plan=${planId}`,
      automatic_tax: {
        enabled: true,
      },
      billing_address_collection: 'required',
      allow_promotion_codes: true,
    };

    // Agregar configuración específica para suscripciones (AHORA APLICA A 'usuario' Y 'pro')
    if (isRecurring) {
      sessionConfig.subscription_data = {
        metadata: {
          planId: planId,
          customerEmail: email,
          customerName: customerName || '',
          userId: userId, // ID del usuario autenticado obtenido del servidor
          registrationData: registrationData ? JSON.stringify(registrationData) : '', // Datos de registro para crear cuenta después del pago
          source: 'OposiAI_website',
          autoActivate: 'true'
        },
        // trial_period_days: 7, // Opcional: si quieres ofrecer un periodo de prueba
      };
    }

    if (!stripe) {
      console.error('Stripe not initialized');
      return NextResponse.json({
        error: 'Error de configuración de Stripe'
      }, { status: 500 });
    }

    const session = await stripe.checkout.sessions.create(sessionConfig);

    console.log('Checkout session created:', session.id);

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    
    let errorMessage = 'Error al crear la sesión de pago';
    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('Error message:', errorMessage);
    }

    return NextResponse.json({
      error: errorMessage,
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
