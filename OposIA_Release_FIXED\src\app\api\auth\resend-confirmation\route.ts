// src/app/api/auth/resend-confirmation/route.ts
// API para reenviar email de confirmación

import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService } from '@/lib/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({
        error: 'Email es requerido'
      }, { status: 400 });
    }

    console.log('📧 Reenviando email de confirmación para:', email);

    // 1. Verificar que el usuario existe
    const existingUser = await SupabaseAdminService.getUserByEmail(email);
    
    if (!existingUser) {
      return NextResponse.json({
        error: 'No se encontró una cuenta con este email'
      }, { status: 404 });
    }

    // 2. Verificar que el usuario no esté ya confirmado
    if (existingUser.email_confirmed_at) {
      return NextResponse.json({
        error: 'Esta cuenta ya está confirmada'
      }, { status: 400 });
    }

    // 3. Verificar que el usuario tenga un pago verificado (para usuarios de pago)
    const userProfile = await SupabaseAdminService.getUserProfile(existingUser.id);
    
    if (userProfile && userProfile.subscription_plan !== 'free' && !userProfile.payment_verified) {
      return NextResponse.json({
        error: 'El pago aún no ha sido verificado. Por favor, espera a que se complete el proceso de pago.'
      }, { status: 400 });
    }

    // 4. Enviar email de confirmación
    const emailResult = await SupabaseAdminService.sendConfirmationEmailForUser(existingUser.id);

    if (!emailResult.success) {
      console.error('Error enviando email de confirmación:', emailResult.error);
      return NextResponse.json({
        error: 'Error enviando email de confirmación: ' + emailResult.error
      }, { status: 500 });
    }

    console.log('✅ Email de confirmación reenviado exitosamente para:', email);

    return NextResponse.json({
      success: true,
      message: 'Email de confirmación enviado exitosamente'
    });

  } catch (error) {
    console.error('Error reenviando email de confirmación:', error);
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
