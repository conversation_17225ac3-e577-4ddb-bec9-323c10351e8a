/**
 * Tipos relacionados con la base de datos Supabase
 *
 * Este archivo contiene todas las interfaces y tipos que representan
 * las entidades de la base de datos y sus relaciones.
 */

// ============================================================================
// TIPOS BÁSICOS Y ENUMS
// ============================================================================

export type DificultadRespuesta = 'dificil' | 'normal' | 'facil';

// ============================================================================
// PERFILES Y AUTENTICACIÓN
// ============================================================================

export interface ExtendedUserProfile {
  id?: string;
  user_id: string;
  subscription_plan: 'free' | 'usuario' | 'pro';
  monthly_token_limit: number;
  current_month_tokens: number;
  current_month: string;
  payment_verified: boolean;
  stripe_customer_id?: string;
  stripe_subscription_id?: string; // ID de la suscripción activa en Stripe
  last_payment_date?: string;
  plan_expires_at?: string;
  auto_renew: boolean;
  plan_features: string[];
  security_flags?: any;
  created_at?: string;
  updated_at?: string;
}

export interface StripeTransaction {
  id?: string;
  stripe_session_id: string;
  stripe_customer_id?: string;
  user_email: string;
  user_name?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  plan_type: string;
  metadata?: any;
  created_at?: string;
  updated_at?: string;
}

export interface UserPlanHistory {
  id?: string;
  user_id: string;
  old_plan?: string;
  new_plan: string;
  changed_by: 'system' | 'admin' | 'user';
  reason: string;
  transaction_id?: string;
  created_at?: string;
}

export interface FeatureAccessLog {
  id?: string;
  user_id: string;
  feature: string;
  access_granted: boolean;
  reason?: string;
  tokens_used?: number;
  plan_at_time: string;
  created_at?: string;
}

// ============================================================================
// DOCUMENTOS Y CONTENIDO
// ============================================================================

export interface Documento {
  id: string;
  titulo: string;
  contenido: string;
  categoria?: string;
  numero_tema?: number;
  creado_en: string;
  actualizado_en: string;
  user_id: string;
  tipo_original?: string;
}

export interface Resumen {
  id: string;
  user_id: string;
  documento_id: string;
  titulo: string;
  contenido: string;
  contenido_editado?: string;
  editado: boolean;
  fecha_edicion?: string;
  tipo: 'a1' | 'a2' | 'personalizado';
  instrucciones?: string;
  creado_en: string;
  actualizado_en: string;
}

// ============================================================================
// CONVERSACIONES Y MENSAJES
// ============================================================================

export interface Conversacion {
  id: string;
  titulo: string;
  creado_en: string;
  actualizado_en: string;
  activa?: boolean;
  user_id: string;
}

export interface Mensaje {
  id: string;
  conversacion_id: string;
  tipo: 'usuario' | 'ia';
  contenido: string;
  timestamp: string;
}

// ============================================================================
// FLASHCARDS Y PROGRESO
// ============================================================================

export interface Flashcard {
  id: string;
  coleccion_id: string;
  pregunta: string;
  respuesta: string;
  creado_en: string;
  actualizado_en: string;
}

export interface ColeccionFlashcards {
  id: string;
  titulo: string;
  descripcion?: string;
  creado_en: string;
  actualizado_en: string;
  user_id: string;
  numero_flashcards: number; // Added to store the count of flashcards
  pendientes_hoy?: number; // Added to store the count of flashcards pending for today
}

export interface ProgresoFlashcardDB {
  id: string;
  flashcard_id: string;
  factor_facilidad: number;
  intervalo: number;
  repeticiones: number;
  estado: string;
  ultima_revision: string;
  proxima_revision: string;
}

export interface FlashcardConProgreso extends Flashcard {
  debeEstudiar: boolean;
  progreso?: {
    factor_facilidad: number;
    intervalo: number;
    repeticiones: number;
    estado: string;
    proxima_revision: string;
  };
}

export interface RevisionHistorial {
  id: string;
  flashcard_id: string;
  dificultad: DificultadRespuesta;
  factor_facilidad: number;
  intervalo: number;
  repeticiones: number;
  fecha: string;
}

// ============================================================================
// TESTS Y EVALUACIONES
// ============================================================================

export interface Test {
  id: string;
  titulo: string;
  descripcion?: string;
  creado_en: string;
  documentos_ids?: string[];
  user_id: string;
  numero_preguntas?: number; // Added to store the count of questions
}

export interface PreguntaTest {
  id: string;
  test_id: string;
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}

export interface EstadisticaTest {
  id: string;
  test_id: string;
  pregunta_id: string;
  respuesta_seleccionada: 'a' | 'b' | 'c' | 'd' | 'x'; // 'x' representa respuesta en blanco
  es_correcta: boolean;
  fecha_respuesta: string;
  tiempo_respuesta?: number;
  created_at?: string;
}

// ============================================================================
// TEMARIOS Y PLANIFICACIÓN
// ============================================================================

export interface Temario {
  id: string;
  titulo: string;
  descripcion?: string;
  tipo: 'completo' | 'temas_sueltos';
  user_id: string;
  creado_en: string;
  actualizado_en: string;
}

export interface Tema {
  id: string;
  temario_id: string;
  numero: number;
  titulo: string;
  descripcion?: string;
  orden: number;
  completado: boolean;
  fecha_completado?: string;
  creado_en: string;
  actualizado_en: string;
}

export interface PlanificacionEstudio {
  id: string;
  temario_id: string;
  tema_id: string;
  fecha_planificada: string;
  tiempo_estimado?: number;
  completado: boolean;
  fecha_completado?: string;
  notas?: string;
  creado_en: string;
  actualizado_en: string;
}

export interface PlanificacionUsuario {
  id: string;
  user_id: string;
  temario_id: string;
  tiempo_diario_promedio?: number;
  tiempo_por_dia?: {
    lunes?: number;
    martes?: number;
    miercoles?: number;
    jueves?: number;
    viernes?: number;
    sabado?: number;
    domingo?: number;
  };
  fecha_examen?: string;
  fecha_examen_aproximada?: string;
  familiaridad_general?: number;
  preferencias_horario?: string[];
  frecuencia_repasos?: string;
  fecha_inicio: string;
  fecha_fin: string;
  completado: boolean;
  creado_en: string;
  actualizado_en: string;
}

export interface EstimacionTema {
  id: string;
  planificacion_id: string;
  tema_id: string;
  horas_estimadas?: number;
  es_dificil: boolean;
  prioridad: number;
  notas?: string;
  creado_en: string;
  actualizado_en: string;
}

export interface PlanEstudios {
  id: string;
  user_id: string;
  temario_id: string;
  titulo: string;
  plan_data: any; // JSON con la estructura PlanEstudiosEstructurado
  fecha_inicio: string;
  fecha_fin: string;
  notas?: string;
  creado_en: string;
  actualizado_en: string;
}

export interface ProgresoPlanEstudios {
  id: string;
  plan_id: string;
  user_id: string;
  semana_numero: number;
  dia_nombre: string;
  tarea_titulo: string;
  tarea_tipo: 'estudio' | 'repaso' | 'practica' | 'evaluacion';
  completado: boolean;
  fecha_completado?: string;
  tiempo_real_minutos?: number;
  notas_progreso?: string;
  calificacion?: number; // 1-5 estrellas
  creado_en: string;
  actualizado_en: string;
}

// ============================================================================
// ESTADÍSTICAS Y MÉTRICAS
// ============================================================================

export interface EstadisticasGeneralesTest {
  totalTests: number;
  totalPreguntas: number;
  totalRespuestasCorrectas: number;
  totalRespuestasIncorrectas: number;
  porcentajeAcierto: number;
}

export interface EstadisticasTestEspecifico {
  testId: string;
  totalPreguntas: number;
  totalCorrectas: number;
  totalIncorrectas: number;
  porcentajeAcierto: number;
  fechasRealizacion: string[];
  preguntasMasFalladas: {
    preguntaId: string;
    pregunta: string;
    totalFallos: number;
    totalAciertos: number;
  }[];
}

export interface EstadisticasEstudio {
  totalSesiones: number;
  totalRevisiones: number;
  distribucionDificultad: {
    dificil: number;
    normal: number;
    facil: number;
  };
  progresoTiempo: {
    fecha: string;
    nuevas: number;
    aprendiendo: number;
    repasando: number;
    aprendidas: number;
  }[];
  tarjetasMasDificiles: {
    id: string;
    pregunta: string;
    dificil: number;
    normal: number;
    facil: number;
    totalRevisiones: number;
  }[];
}
