// src/app/api/auth/confirm-plan-upgrade/route.ts
// Endpoint para confirmar actualizaciones de plan (SEGURIDAD)

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { UserManagementService } from '@/lib/services/userManagement';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    console.log(`🔑 [SECURITY] Procesando confirmación de upgrade con token: ${token?.substring(0, 8)}...`);

    if (!token) {
      console.log('❌ [SECURITY] Token de confirmación no proporcionado');
      return NextResponse.json({ 
        error: 'Token de confirmación requerido' 
      }, { status: 400 });
    }

    // 1. Buscar la transacción con el token de confirmación
    const { data: transaction, error } = await supabaseAdmin
      .from('stripe_transactions')
      .select('*')
      // Usar el operador ->> para consultar dentro del JSON de metadata
      .eq('metadata->pending_upgrade->>confirmationToken', token)
      .single();

    if (error || !transaction || !transaction.metadata?.pending_upgrade) {
      console.log(`❌ [SECURITY] Token inválido o no encontrado: ${token?.substring(0, 8)}...`);
      return NextResponse.json({ 
        error: 'Token inválido o expirado' 
      }, { status: 404 });
    }

    const upgradeInfo = transaction.metadata.pending_upgrade;
    console.log(`🔍 [SECURITY] Upgrade info encontrada para usuario: ${upgradeInfo.userId}, plan: ${upgradeInfo.newPlanId}`);

    // 2. Validar que el token no haya expirado
    if (new Date(upgradeInfo.tokenExpiresAt) < new Date()) {
      console.log(`⏰ [SECURITY] Token expirado para usuario: ${upgradeInfo.userId}`);
      return NextResponse.json({ 
        error: 'El enlace de confirmación ha expirado. Por favor, contacta con soporte si necesitas ayuda.' 
      }, { status: 410 });
    }

    // 3. Validar que el upgrade no haya sido ya confirmado
    if (upgradeInfo.status === 'confirmed') {
      console.log(`✅ [SECURITY] Upgrade ya confirmado previamente para usuario: ${upgradeInfo.userId}`);
      return NextResponse.json({ 
        success: true,
        message: 'El plan ya ha sido actualizado previamente',
        alreadyConfirmed: true
      });
    }

    // 4. Aplicar la actualización del plan
    console.log(`🔄 [SECURITY] Aplicando actualización de plan para usuario: ${upgradeInfo.userId}`);
    const updateResult = await UserManagementService.updateUserPlan(
      upgradeInfo.userId,
      upgradeInfo.newPlanId,
      transaction.id,
      'Plan upgrade confirmed by user via email'
    );

    if (!updateResult.success) {
      console.error(`❌ [SECURITY] Error actualizando plan para usuario ${upgradeInfo.userId}:`, updateResult.error);
      return NextResponse.json({
        error: 'No se pudo actualizar el plan. Por favor, contacta con soporte.'
      }, { status: 500 });
    }
    
    // 5. Actualizar el estado de la transacción a "completado"
    const { error: updateError } = await supabaseAdmin.from('stripe_transactions').update({
      user_id: upgradeInfo.userId, // Asegurarse de que el user_id esté asignado
      metadata: {
        ...transaction.metadata,
        pending_upgrade: {
          ...upgradeInfo,
          status: 'confirmed',
          confirmedAt: new Date().toISOString()
        }
      }
    }).eq('id', transaction.id);

    if (updateError) {
      console.error(`❌ [SECURITY] Error actualizando metadata de transacción:`, updateError);
      // No fallar por esto, el plan ya se actualizó
    }

    // 6. También actualizar el perfil con los datos de Stripe
    const { error: profileUpdateError } = await supabaseAdmin.from('user_profiles').update({
      stripe_customer_id: upgradeInfo.stripeCustomerId,
      stripe_subscription_id: upgradeInfo.stripeSubscriptionId,
      payment_verified: true,
      updated_at: new Date().toISOString()
    }).eq('user_id', upgradeInfo.userId);

    if (profileUpdateError) {
      console.error(`❌ [SECURITY] Error actualizando perfil con datos de Stripe:`, profileUpdateError);
      // No fallar por esto, el plan ya se actualizó
    }

    console.log(`✅ [SECURITY] Plan actualizado exitosamente para usuario: ${upgradeInfo.userId} al plan: ${upgradeInfo.newPlanId}`);

    return NextResponse.json({ 
      success: true, 
      message: 'Plan actualizado exitosamente',
      planId: upgradeInfo.newPlanId
    });

  } catch (err) {
    console.error('❌ [SECURITY] Error al confirmar el upgrade de plan:', err);
    return NextResponse.json({ 
      error: 'Error interno del servidor. Por favor, contacta con soporte.' 
    }, { status: 500 });
  }
}

// Método GET para validar tokens sin confirmar (opcional, para debugging)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json({ 
        error: 'Token requerido' 
      }, { status: 400 });
    }

    // Buscar la transacción con el token
    const { data: transaction, error } = await supabaseAdmin
      .from('stripe_transactions')
      .select('metadata')
      .eq('metadata->pending_upgrade->>confirmationToken', token)
      .single();

    if (error || !transaction?.metadata?.pending_upgrade) {
      return NextResponse.json({ 
        valid: false,
        error: 'Token no encontrado'
      });
    }

    const upgradeInfo = transaction.metadata.pending_upgrade;
    const isExpired = new Date(upgradeInfo.tokenExpiresAt) < new Date();
    const isConfirmed = upgradeInfo.status === 'confirmed';

    return NextResponse.json({
      valid: !isExpired && !isConfirmed,
      expired: isExpired,
      confirmed: isConfirmed,
      planId: upgradeInfo.newPlanId,
      expiresAt: upgradeInfo.tokenExpiresAt
    });

  } catch (err) {
    console.error('Error validando token:', err);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}
