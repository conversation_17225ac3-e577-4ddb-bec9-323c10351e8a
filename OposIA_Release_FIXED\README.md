# OposIA - Sistema de Preparación de Oposiciones

## Descripción
OposIA es una aplicación completa para la preparación de oposiciones que utiliza inteligencia artificial para generar contenido educativo personalizado.

## Características Principales
- **Gestión de Documentos**: Subida y organización de PDFs de estudio
- **Generación de Tests**: Creación automática de tests con IA
- **Flashcards Inteligentes**: Tarjetas de estudio generadas automáticamente
- **Mapas Mentales**: Visualización de conceptos clave
- **Chat Tutor**: Asistente de IA para resolver dudas
- **Planificación de Estudios**: Sistema de planificación personalizada
- **Estadísticas**: Seguimiento del progreso de estudio
- **Sistema de Temarios**: Gestión de temarios predefinidos

## Planes Disponibles
- **Free**: Acceso limitado con trial de 5 días
- **Usuario**: €10/mes - Acceso completo a funciones básicas
- **Pro**: €15/mes - Acceso completo + planificación de estudios

## Requisitos del Sistema
- Node.js 18.0 o superior
- npm 8.0 o superior
- Conexión a internet para servicios de IA

## Instalación y Ejecución

### Método 1: Usando el archivo de inicio (Recomendado)
1. Ejecuta `INICIAR_APLICACION.bat` (Windows)
2. El script instalará automáticamente las dependencias y iniciará la aplicación

### Método 2: Manual
1. Instalar dependencias:
   ```bash
   npm install
   ```

2. Iniciar la aplicación:
   ```bash
   npm run dev
   ```

3. Abrir navegador en: http://localhost:3000

## Configuración
La aplicación utiliza el archivo `.env.local` para la configuración. Este archivo ya está incluido con todas las variables necesarias configuradas.

## Estructura del Proyecto
```
OposIA/
├── src/
│   ├── app/                 # Páginas de Next.js
│   ├── components/          # Componentes UI reutilizables
│   ├── features/            # Módulos de funcionalidades
│   ├── lib/                 # Librerías y servicios
│   ├── hooks/               # React hooks personalizados
│   └── types/               # Definiciones de tipos TypeScript
├── public/                  # Archivos estáticos
└── package.json            # Dependencias del proyecto
```

## Tecnologías Utilizadas
- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Base de Datos**: Supabase (PostgreSQL)
- **Autenticación**: Supabase Auth
- **Pagos**: Stripe
- **IA**: Google Gemini, OpenAI
- **Almacenamiento**: Supabase Storage

## Funcionalidades por Módulo

### Documentos
- Subida de archivos PDF
- Gestión y organización de documentos
- Límites por plan de usuario

### Tests
- Generación automática con IA
- Múltiples tipos de preguntas
- Estadísticas de rendimiento
- Sistema de repaso inteligente

### Flashcards
- Creación automática desde documentos
- Modos de estudio personalizables
- Algoritmo de repetición espaciada
- Estadísticas detalladas

### Planificación (Solo Plan Pro)
- Asistente de planificación con IA
- Calendario de estudios
- Seguimiento de progreso
- Tareas diarias personalizadas

### Chat Tutor
- Asistente de IA especializado
- Resolución de dudas
- Explicaciones personalizadas
- Historial de conversaciones

## Soporte
Para soporte técnico o consultas, contacta con el equipo de desarrollo.

## Versión
Versión actual: 2.6 (Julio 2025)

---
© 2025 OposIA. Todos los derechos reservados.
