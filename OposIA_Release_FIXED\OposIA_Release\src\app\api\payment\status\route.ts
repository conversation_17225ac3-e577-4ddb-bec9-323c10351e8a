// src/app/api/payment/status/route.ts
// API para verificar el estado de un pago

import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { stripe } from '@/lib/stripe/config';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json({
        error: 'Session ID es requerido'
      }, { status: 400 });
    }

    console.log('🔍 Verificando estado del pago para sesión:', sessionId);

    // 1. Verificar en Stripe
    let stripeSession;
    try {
      if (stripe) {
        stripeSession = await stripe.checkout.sessions.retrieve(sessionId);
      }
    } catch (stripeError) {
      console.error('Error obteniendo sesión de Stripe:', stripeError);
      return NextResponse.json({
        error: 'Error verificando estado en Stripe'
      }, { status: 500 });
    }

    // 2. Verificar en nuestra base de datos
    const transaction = await SupabaseAdminService.getTransactionBySessionId(sessionId);

    // 3. Determinar estado
    let status = 'pending';
    let userActivated = false;

    if (stripeSession?.payment_status === 'paid' && transaction?.activated_at) {
      status = 'completed';
      userActivated = true;
    } else if (stripeSession?.payment_status === 'paid' && transaction) {
      status = 'processing'; // Pago completado pero usuario no activado aún
    } else if (stripeSession?.payment_status === 'unpaid') {
      status = 'failed';
    }

    // 4. Si el pago está completado pero el usuario no está activado, verificar el usuario
    if (stripeSession?.payment_status === 'paid' && !userActivated && transaction?.user_id) {
      try {
        const userProfile = await SupabaseAdminService.getUserProfile(transaction.user_id);
        if (userProfile?.payment_verified) {
          status = 'completed';
          userActivated = true;
        }
      } catch (profileError) {
        console.error('Error verificando perfil de usuario:', profileError);
      }
    }

    console.log('📊 Estado del pago:', {
      sessionId,
      stripeStatus: stripeSession?.payment_status,
      hasTransaction: !!transaction,
      transactionActivated: !!transaction?.activated_at,
      finalStatus: status,
      userActivated
    });

    return NextResponse.json({
      success: true,
      status: status,
      data: {
        sessionId: sessionId,
        stripePaymentStatus: stripeSession?.payment_status,
        transactionExists: !!transaction,
        transactionActivated: !!transaction?.activated_at,
        userActivated: userActivated,
        customerEmail: stripeSession?.customer_details?.email || transaction?.user_email
      }
    });

  } catch (error) {
    console.error('Error verificando estado del pago:', error);
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
