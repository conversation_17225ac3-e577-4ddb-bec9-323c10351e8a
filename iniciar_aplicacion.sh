#!/bin/bash

# Configurar colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Función para imprimir con colores
print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                                   OposIA                                    ║"
    echo "║                        Sistema de Preparación de Oposiciones                ║"
    echo "║                              con Inteligencia Artificial                     ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}🔧 $1${NC}"
}

# Función para verificar si un comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Función para pausar (equivalente a pause en Windows)
pause() {
    read -p "Presione Enter para continuar..."
}

# Limpiar pantalla y mostrar header
clear
print_header
echo
echo -e "${BLUE}🚀 Iniciando OposIA...${NC}"
echo

# Verificar si Node.js está instalado
if ! command_exists node; then
    print_error "Node.js no está instalado en este sistema."
    echo
    echo -e "${CYAN}📥 Por favor, instale Node.js desde: https://nodejs.org/${NC}"
    echo -e "${CYAN}   Versión recomendada: 18.x o superior${NC}"
    echo
    pause
    exit 1
fi

print_success "Node.js detectado correctamente"
echo -e "${CYAN}   Versión: $(node --version)${NC}"

# Verificar si npm está disponible
if ! command_exists npm; then
    print_error "npm no está disponible."
    echo
    pause
    exit 1
fi

print_success "npm detectado correctamente"
echo -e "${CYAN}   Versión: $(npm --version)${NC}"
echo

# Verificar si existe package.json
if [ ! -f "package.json" ]; then
    print_error "No se encontró package.json en el directorio actual."
    echo -e "${CYAN}   Asegúrese de ejecutar este archivo desde la carpeta raíz del proyecto.${NC}"
    echo
    pause
    exit 1
fi

print_success "Proyecto OposIA encontrado"
echo

# Verificar si node_modules existe
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 Instalando dependencias por primera vez...${NC}"
    echo -e "${CYAN}   Esto puede tomar varios minutos...${NC}"
    echo
    
    if ! npm install; then
        print_error "Error instalando dependencias."
        echo
        pause
        exit 1
    fi
    
    print_success "Dependencias instaladas correctamente"
    echo
else
    print_success "Dependencias ya instaladas"
    echo
fi

# Verificar si existe .env.local
if [ ! -f ".env.local" ]; then
    print_warning "No se encontró archivo .env.local"
    echo -e "${CYAN}   La aplicación necesita variables de entorno configuradas.${NC}"
    echo
    echo -e "${BLUE}📝 Creando archivo .env.example...${NC}"
    echo
    
    cat > .env.example << 'EOF'
# Configuración de OposIA
# Copie este archivo como .env.local y configure las variables reales

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Admin Configuration
ADMIN_EMAILS=<EMAIL>

# Email Configuration (opcional)
RESEND_API_KEY=your_resend_api_key_here
EOF
    
    print_success "Archivo .env.example creado"
    echo -e "${CYAN}   Configure las variables reales en .env.local antes de usar la aplicación${NC}"
    echo
fi

print_info "Verificando configuración del proyecto..."
echo

# Verificar scripts en package.json
if ! grep -q '"dev"' package.json; then
    print_error "Script 'dev' no encontrado en package.json"
    pause
    exit 1
fi

print_success "Configuración del proyecto verificada"
echo

echo -e "${BLUE}🌐 Iniciando servidor de desarrollo...${NC}"
echo
echo -e "${CYAN}   📍 La aplicación estará disponible en: http://localhost:3000${NC}"
echo -e "${CYAN}   🔄 El servidor se recargará automáticamente al hacer cambios${NC}"
echo -e "${CYAN}   🛑 Para detener el servidor, presione Ctrl+C${NC}"
echo
echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║  IMPORTANTE: Mantenga esta terminal abierta mientras usa la aplicación       ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo

# Hacer el script ejecutable
chmod +x "$0" 2>/dev/null

# Iniciar el servidor de desarrollo
npm run dev

# Si el servidor se detiene, mostrar mensaje
echo
echo -e "${YELLOW}🛑 Servidor detenido.${NC}"
echo
pause
