// src/lib/utils/securityHelpers.ts
// Helpers de seguridad para validación y protección

import { createClient } from '@/lib/supabase/supabaseClient';
import {
  RATE_LIMITS,
  SECURITY_RISK_SCORES,
  ERROR_MESSAGES,
  APP_URLS
} from '@/config/constants';

export interface SecurityValidationResult {
  valid: boolean;
  reason?: string;
  code?: string;
  metadata?: any;
}

export class SecurityHelpers {
  
  /**
   * Validar que un usuario tiene acceso a una ruta específica
   */
  static async validateRouteAccess(
    userId: string,
    route: string
  ): Promise<SecurityValidationResult> {
    try {
      // Import dinámico para evitar problemas en el cliente
      const { supabaseAdmin } = await import('@/lib/supabase/admin');

      // Obtener perfil del usuario
      const { data: profile, error } = await supabaseAdmin
        .from('user_profiles')
        .select('subscription_plan, payment_verified')
        .eq('user_id', userId)
        .single();

      if (error || !profile) {
        return {
          valid: false,
          reason: 'User profile not found',
          code: 'PROFILE_NOT_FOUND'
        };
      }

      // Definir rutas protegidas y sus requisitos
      const routeRequirements: Record<string, string[]> = {
        '/plan-estudios': ['pro'],
        '/app/ai-tutor': ['usuario', 'pro'],
        '/app/summaries': ['pro'],
        '/app/advanced-features': ['pro']
      };

      // Verificar si la ruta requiere planes específicos
      for (const [protectedRoute, requiredPlans] of Object.entries(routeRequirements)) {
        if (route.startsWith(protectedRoute)) {
          if (!requiredPlans.includes(profile.subscription_plan)) {
            return {
              valid: false,
              reason: `Route requires plan: ${requiredPlans.join(' or ')}`,
              code: 'INSUFFICIENT_PLAN',
              metadata: {
                userPlan: profile.subscription_plan,
                requiredPlans
              }
            };
          }

          // Verificar pago para planes de pago
          if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
            return {
              valid: false,
              reason: 'Payment verification required',
              code: 'PAYMENT_NOT_VERIFIED'
            };
          }
        }
      }

      return { valid: true };

    } catch (error) {
      console.error('Error validating route access:', error);
      return {
        valid: false,
        reason: 'Internal validation error',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Validar límites de rate limiting por usuario
   */
  static async validateRateLimit(
    userId: string,
    action: string,
    windowMinutes: number = RATE_LIMITS.DEFAULT_WINDOW_MINUTES,
    maxRequests: number = RATE_LIMITS.DEFAULT_MAX_REQUESTS
  ): Promise<SecurityValidationResult> {
    try {
      // Import dinámico para evitar problemas en el cliente
      const { supabaseAdmin } = await import('@/lib/supabase/admin');

      const windowStart = new Date(Date.now() - windowMinutes * 60 * 1000);

      // Contar requests en la ventana de tiempo
      const { count, error } = await supabaseAdmin
        .from('feature_access_log')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('feature_name', action)
        .gte('created_at', windowStart.toISOString());

      if (error) {
        console.error('Error checking rate limit:', error);
        return {
          valid: false,
          reason: 'Rate limit check failed',
          code: 'RATE_LIMIT_ERROR'
        };
      }

      if ((count || 0) >= maxRequests) {
        return {
          valid: false,
          reason: `Rate limit exceeded: ${count}/${maxRequests} requests in ${windowMinutes} minutes`,
          code: 'RATE_LIMIT_EXCEEDED',
          metadata: {
            currentCount: count,
            maxRequests,
            windowMinutes,
            resetTime: new Date(Date.now() + windowMinutes * 60 * 1000)
          }
        };
      }

      return {
        valid: true,
        metadata: {
          currentCount: count,
          maxRequests,
          remaining: maxRequests - (count || 0)
        }
      };

    } catch (error) {
      console.error('Error validating rate limit:', error);
      return {
        valid: false,
        reason: 'Rate limit validation error',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Validar integridad de sesión
   */
  static async validateSessionIntegrity(
    sessionToken: string
  ): Promise<SecurityValidationResult> {
    try {
      const supabase = createClient();
      
      // Verificar que la sesión es válida
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        return {
          valid: false,
          reason: 'Invalid or expired session',
          code: 'INVALID_SESSION'
        };
      }

      // Import dinámico para evitar problemas en el cliente
      const { supabaseAdmin } = await import('@/lib/supabase/admin');

      // Verificar que el usuario no está bloqueado
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('security_flags')
        .eq('user_id', user.id)
        .single();

      if (profile?.security_flags?.blocked) {
        return {
          valid: false,
          reason: 'User account is blocked',
          code: 'ACCOUNT_BLOCKED'
        };
      }

      return { valid: true };

    } catch (error) {
      console.error('Error validating session integrity:', error);
      return {
        valid: false,
        reason: 'Session validation error',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Sanitizar entrada de usuario
   */
  static sanitizeInput(input: string, maxLength: number = 1000): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    return input
      .trim()
      .slice(0, maxLength)
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remover scripts
      .replace(/javascript:/gi, '') // Remover javascript:
      .replace(/on\w+\s*=/gi, ''); // Remover event handlers
  }

  /**
   * Validar formato de email
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generar token seguro
   */
  static generateSecureToken(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * Validar fortaleza de contraseña
   */
  static validatePasswordStrength(password: string): {
    valid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    if (password.length < 8) {
      feedback.push('La contraseña debe tener al menos 8 caracteres');
    } else {
      score += 1;
    }

    if (!/[a-z]/.test(password)) {
      feedback.push('Debe incluir al menos una letra minúscula');
    } else {
      score += 1;
    }

    if (!/[A-Z]/.test(password)) {
      feedback.push('Debe incluir al menos una letra mayúscula');
    } else {
      score += 1;
    }

    if (!/\d/.test(password)) {
      feedback.push('Debe incluir al menos un número');
    } else {
      score += 1;
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      feedback.push('Debe incluir al menos un carácter especial');
    } else {
      score += 1;
    }

    return {
      valid: score >= 4,
      score,
      feedback
    };
  }

  /**
   * Log de evento de seguridad
   */
  static async logSecurityEvent(
    userId: string,
    eventType: string,
    details: any,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    try {
      // Import dinámico para evitar problemas en el cliente
      const { supabaseAdmin } = await import('@/lib/supabase/admin');

      await supabaseAdmin
        .from('feature_access_log')
        .insert({
          user_id: userId,
          feature_name: `security_${eventType}`,
          access_granted: false,
          plan_at_time: 'security',
          tokens_used: 0,
          denial_reason: JSON.stringify({
            eventType,
            severity,
            details,
            timestamp: new Date().toISOString()
          })
        });

      // Log en consola para eventos críticos
      if (severity === 'critical') {
        console.error(`🚨 CRITICAL SECURITY EVENT: ${eventType}`, {
          userId,
          details,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Error logging security event:', error);
    }
  }

  /**
   * Verificar si una IP está en lista negra (placeholder)
   */
  static async isIPBlacklisted(ip: string): Promise<boolean> {
    // Implementar lógica de lista negra de IPs
    // Por ahora retorna false
    return false;
  }

  /**
   * Detectar patrones sospechosos en requests
   */
  static detectSuspiciousActivity(
    userAgent: string,
    referer: string,
    requestPattern: any
  ): {
    suspicious: boolean;
    reasons: string[];
    riskScore: number;
  } {
    const reasons: string[] = [];
    let riskScore = 0;

    // Detectar user agents sospechosos
    if (!userAgent || userAgent.length < 10) {
      reasons.push('User agent missing or too short');
      riskScore += SECURITY_RISK_SCORES.MISSING_USER_AGENT;
    }

    if (userAgent && /bot|crawler|spider/i.test(userAgent)) {
      reasons.push('Bot-like user agent detected');
      riskScore += SECURITY_RISK_SCORES.BOT_USER_AGENT;
    }

    // Detectar referers sospechosos
    if (referer && !referer.includes(APP_URLS.BASE)) {
      reasons.push('External referer detected');
      riskScore += SECURITY_RISK_SCORES.EXTERNAL_REFERER;
    }

    // Detectar patrones de request sospechosos
    if (requestPattern?.frequency > 100) {
      reasons.push('High request frequency');
      riskScore += 40;
    }

    return {
      suspicious: riskScore >= 50,
      reasons,
      riskScore
    };
  }
}
