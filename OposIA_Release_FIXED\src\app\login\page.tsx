'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import ForgotPasswordModal from '@/features/auth/components/ForgotPasswordModal';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');
  const [isForgotPasswordModalOpen, setIsForgotPasswordModalOpen] = useState(false);
  const { iniciarSesion, error, isLoading, user } = useAuth();
  const router = useRouter();

  // El middleware y AuthContext ya manejan la redirección si hay una sesión activa
  // No necesitamos un useEffect adicional para esto

  // Actualizar el error del formulario cuando cambia el error de autenticación
  useEffect(() => {
    if (error) {
      // Los mensajes de error ya vienen traducidos desde authService.ts
      setFormError(error);

      // Si es un error de sincronización de tiempo, mostrar información adicional
      if (error.includes('sincronización de tiempo')) {
        console.info('Sugerencia: Verifica que la hora de tu dispositivo esté correctamente configurada y sincronizada con un servidor de tiempo.');
      }
    }
  }, [error]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    // Validaciones básicas
    if (!email.trim()) {
      setFormError('Por favor, ingresa tu email');
      return;
    }

    if (!password.trim()) {
      setFormError('Por favor, ingresa tu contraseña');
      return;
    }

    // Intentar iniciar sesión
    await iniciarSesion(email, password);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h1 className="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          OposiAI
        </h1>
        <h2 className="mt-2 text-center text-xl font-semibold text-gray-900">
          Inicia sesión en tu cuenta
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Accede a tu asistente inteligente para oposiciones
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  disabled={isLoading}
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Contraseña
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Enlace para olvidar contraseña */}
            <div className="flex items-center justify-end">
              <button
                type="button"
                onClick={() => setIsForgotPasswordModalOpen(true)}
                disabled={isLoading}
                className="text-sm text-blue-600 hover:text-blue-500 focus:outline-none focus:underline disabled:opacity-50 disabled:cursor-not-allowed"
              >
                ¿Olvidaste tu contraseña?
              </button>
            </div>

            {formError && (
              <div className="text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200">
                {formError}
                {formError.includes('sincronización de tiempo') && (
                  <div className="mt-2 text-gray-600 text-xs">
                    <p><strong>Sugerencia:</strong> Este error puede ocurrir cuando la hora de tu dispositivo no está sincronizada correctamente.</p>
                    <ol className="list-decimal pl-5 mt-1">
                      <li>Verifica que la fecha y hora de tu dispositivo estén configuradas correctamente</li>
                      <li>Activa la sincronización automática de hora en tu sistema</li>
                      <li>Reinicia el navegador e intenta nuevamente</li>
                    </ol>
                  </div>
                )}
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Iniciando sesión...
                  </div>
                ) : (
                  'Iniciar sesión'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Enlace para volver al inicio */}
      <div className="mt-6 text-center">
        <Link
          href="/"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
        >
          ← Volver al inicio
        </Link>
      </div>

      {/* Modal de recuperación de contraseña */}
      <ForgotPasswordModal
        isOpen={isForgotPasswordModalOpen}
        onClose={() => setIsForgotPasswordModalOpen(false)}
      />
    </div>
  );
}
