// src/__tests__/setup/testConfig.ts
// Configuración avanzada para tests

import { jest } from '@jest/globals';

// Configuración de timeouts para diferentes tipos de tests
export const TEST_TIMEOUTS = {
  UNIT: 5000,
  INTEGRATION: 15000,
  E2E: 30000,
  API: 10000
} as const;

// Configuración de delays para simular operaciones async
export const TEST_DELAYS = {
  FAST: 100,
  MEDIUM: 500,
  SLOW: 1000,
  NETWORK: 2000
} as const;

// Configuración de datos de prueba
export const TEST_DATA = {
  USER: {
    ID: 'test-user-id',
    EMAIL: '<EMAIL>',
    NAME: 'Test User'
  },
  PLANS: {
    FREE: {
      name: 'free',
      tokenLimit: 50000,
      features: ['test_generation', 'document_upload']
    },
    USUARIO: {
      name: 'usuario',
      tokenLimit: 500000,
      features: ['test_generation', 'document_upload', 'ai_tutor_chat', 'flashcards']
    },
    PRO: {
      name: 'pro',
      tokenLimit: 5000000,
      features: ['test_generation', 'document_upload', 'ai_tutor_chat', 'flashcards', 'study_planning', 'mind_maps']
    }
  },
  TOKENS: {
    LOW_USAGE: 1000,
    MEDIUM_USAGE: 25000,
    HIGH_USAGE: 45000,
    EXCEEDED: 55000
  }
} as const;

// Utilidades para crear mocks consistentes
export class MockFactory {
  static createUserProfile(overrides: Partial<any> = {}) {
    return {
      user_id: TEST_DATA.USER.ID,
      subscription_plan: 'free',
      payment_verified: false,
      monthly_token_limit: TEST_DATA.PLANS.FREE.tokenLimit,
      current_month_tokens: TEST_DATA.TOKENS.LOW_USAGE,
      current_month: new Date().toISOString().slice(0, 7) + '-01',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  static createLimitStatus(overrides: Partial<any> = {}) {
    return {
      type: 'tokens',
      severity: 'warning',
      current: TEST_DATA.TOKENS.HIGH_USAGE,
      limit: TEST_DATA.PLANS.FREE.tokenLimit,
      percentage: 90,
      message: 'Has usado 90% de tus tokens mensuales',
      actionRequired: true,
      upgradeOptions: [],
      ...overrides
    };
  }

  static createPermissionResult(overrides: Partial<any> = {}) {
    return {
      granted: true,
      userPlan: 'free',
      tokenInfo: {
        current: TEST_DATA.TOKENS.LOW_USAGE,
        limit: TEST_DATA.PLANS.FREE.tokenLimit,
        remaining: TEST_DATA.PLANS.FREE.tokenLimit - TEST_DATA.TOKENS.LOW_USAGE
      },
      ...overrides
    };
  }

  static createApiResponse<T>(data: T, overrides: Partial<any> = {}) {
    return {
      ok: true,
      status: 200,
      statusText: 'OK',
      json: jest.fn().mockResolvedValue(data),
      text: jest.fn().mockResolvedValue(JSON.stringify(data)),
      ...overrides
    };
  }

  static createApiError(message: string, status = 400) {
    const error = new Error(message);
    (error as any).status = status;
    return error;
  }
}

// Utilidades para testing de hooks
export class HookTestUtils {
  static async waitForNextUpdate() {
    await new Promise(resolve => setTimeout(resolve, 0));
  }

  static async waitForAsyncUpdate(delay = TEST_DELAYS.FAST) {
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  static createMockHookReturn<T>(data: T, loading = false, error: Error | null = null) {
    return {
      data,
      loading,
      error,
      refetch: jest.fn(),
      mutate: jest.fn()
    };
  }
}

// Utilidades para testing de componentes
export class ComponentTestUtils {
  static async waitForElement(getByTestId: any, testId: string, timeout = TEST_TIMEOUTS.UNIT) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const checkElement = () => {
        try {
          const element = getByTestId(testId);
          resolve(element);
        } catch (error) {
          if (Date.now() - startTime > timeout) {
            reject(new Error(`Element with testId "${testId}" not found within ${timeout}ms`));
          } else {
            setTimeout(checkElement, 100);
          }
        }
      };
      checkElement();
    });
  }

  static async fillFormField(getByLabelText: any, label: string, value: string) {
    const field = getByLabelText(label);
    field.focus();
    field.value = '';
    field.value = value;
    field.dispatchEvent(new Event('input', { bubbles: true }));
    field.dispatchEvent(new Event('change', { bubbles: true }));
  }

  static async submitForm(getByRole: any, buttonText = 'Submit') {
    const submitButton = getByRole('button', { name: buttonText });
    submitButton.click();
  }
}

// Utilidades para testing de servicios
export class ServiceTestUtils {
  static createMockSupabaseResponse<T>(data: T, error: any = null) {
    return {
      data: error ? null : data,
      error,
      status: error ? 400 : 200,
      statusText: error ? 'Bad Request' : 'OK'
    };
  }

  static createMockSupabaseClient() {
    return {
      from: jest.fn(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(),
            limit: jest.fn()
          })),
          limit: jest.fn(),
          single: jest.fn()
        })),
        insert: jest.fn(),
        update: jest.fn(() => ({
          eq: jest.fn()
        })),
        delete: jest.fn(() => ({
          eq: jest.fn()
        }))
      })),
      auth: {
        getUser: jest.fn(),
        signOut: jest.fn(),
        signInWithPassword: jest.fn(),
        signUp: jest.fn()
      }
    };
  }
}

// Utilidades para testing de fechas
export class DateTestUtils {
  static mockCurrentDate(dateString: string) {
    const mockDate = new Date(dateString);
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
    return mockDate;
  }

  static restoreDate() {
    jest.restoreAllMocks();
  }

  static createDateInPast(daysAgo: number) {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date;
  }

  static createDateInFuture(daysFromNow: number) {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date;
  }
}

// Utilidades para testing de localStorage
export class StorageTestUtils {
  static mockLocalStorage() {
    const store: Record<string, string> = {};
    
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key: string) => store[key] || null),
        setItem: jest.fn((key: string, value: string) => {
          store[key] = value;
        }),
        removeItem: jest.fn((key: string) => {
          delete store[key];
        }),
        clear: jest.fn(() => {
          Object.keys(store).forEach(key => delete store[key]);
        })
      },
      writable: true
    });

    return window.localStorage;
  }

  static clearMockStorage() {
    if (window.localStorage && typeof window.localStorage.clear === 'function') {
      window.localStorage.clear();
    }
  }
}

// Configuración global para tests
export const setupTestEnvironment = () => {
  // Mock console methods
  jest.spyOn(console, 'error').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'log').mockImplementation(() => {});

  // Mock timers
  jest.useFakeTimers();

  // Mock localStorage
  StorageTestUtils.mockLocalStorage();

  // Mock fetch
  global.fetch = jest.fn();

  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
  }));

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
  }));
};

export const cleanupTestEnvironment = () => {
  jest.restoreAllMocks();
  jest.useRealTimers();
  StorageTestUtils.clearMockStorage();
};
