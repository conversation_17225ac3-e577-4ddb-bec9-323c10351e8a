import React, { useState, useEffect } from 'react';
import { Fi<PERSON>, <PERSON>Bar<PERSON>hart2, <PERSON><PERSON><PERSON>, <PERSON>TrendingUp, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FiTarget } from 'react-icons/fi';
import { obtenerColeccionesFlashcards, obtenerEstadisticasColeccion, ColeccionFlashcards } from '@/lib/supabase';

interface EstadisticasGeneralesFlashcards {
  totalColecciones: number;
  totalFlashcards: number;
  totalNuevas: number;
  totalAprendiendo: number;
  totalRepasando: number;
  totalAprendidas: number;
  totalParaHoy: number;
  coleccionesConMasActividad: {
    id: string;
    titulo: string;
    totalRevisiones: number;
    paraHoy: number;
  }[];
}

interface FlashcardGeneralStatisticsProps {
  onClose: () => void;
}

const FlashcardGeneralStatistics: React.FC<FlashcardGeneralStatisticsProps> = ({ onClose }) => {
  const [estadisticas, setEstadisticas] = useState<EstadisticasGeneralesFlashcards | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    cargarEstadisticasGenerales();
  }, []);

  const cargarEstadisticasGenerales = async () => {
    try {
      setIsLoading(true);
      
      // Obtener todas las colecciones
      const colecciones = await obtenerColeccionesFlashcards();
      
      if (colecciones.length === 0) {
        setEstadisticas({
          totalColecciones: 0,
          totalFlashcards: 0,
          totalNuevas: 0,
          totalAprendiendo: 0,
          totalRepasando: 0,
          totalAprendidas: 0,
          totalParaHoy: 0,
          coleccionesConMasActividad: []
        });
        return;
      }

      // Obtener estadísticas de cada colección
      const estadisticasColecciones = await Promise.all(
        colecciones.map(async (coleccion) => {
          const stats = await obtenerEstadisticasColeccion(coleccion.id);
          return {
            coleccion,
            estadisticas: stats
          };
        })
      );

      // Calcular estadísticas generales
      const estadisticasGenerales: EstadisticasGeneralesFlashcards = {
        totalColecciones: colecciones.length,
        totalFlashcards: estadisticasColecciones.reduce((sum, item) => sum + item.estadisticas.total, 0),
        totalNuevas: estadisticasColecciones.reduce((sum, item) => sum + item.estadisticas.nuevas, 0),
        totalAprendiendo: estadisticasColecciones.reduce((sum, item) => sum + item.estadisticas.aprendiendo, 0),
        totalRepasando: estadisticasColecciones.reduce((sum, item) => sum + item.estadisticas.repasando, 0),
        totalAprendidas: estadisticasColecciones.reduce((sum, item) => sum + item.estadisticas.aprendidas, 0),
        totalParaHoy: estadisticasColecciones.reduce((sum, item) => sum + item.estadisticas.paraHoy, 0),
        coleccionesConMasActividad: estadisticasColecciones
          .map(item => ({
            id: item.coleccion.id,
            titulo: item.coleccion.titulo,
            totalRevisiones: item.estadisticas.total,
            paraHoy: item.estadisticas.paraHoy
          }))
          .sort((a, b) => b.paraHoy - a.paraHoy)
          .slice(0, 5)
      };

      setEstadisticas(estadisticasGenerales);
    } catch (error) {
      console.error('Error al cargar estadísticas generales:', error);
      setError('No se pudieron cargar las estadísticas generales');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Error</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <FiX size={24} />
            </button>
          </div>
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    );
  }

  if (!estadisticas) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Estadísticas Generales de Flashcards</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Estadísticas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiBookOpen className="text-blue-600 mr-2 text-xl" />
              <h4 className="font-semibold">Total Colecciones</h4>
            </div>
            <p className="text-3xl font-bold text-blue-700">{estadisticas.totalColecciones}</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiTarget className="text-green-600 mr-2 text-xl" />
              <h4 className="font-semibold">Total Flashcards</h4>
            </div>
            <p className="text-3xl font-bold text-green-700">{estadisticas.totalFlashcards}</p>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiClock className="text-orange-600 mr-2 text-xl" />
              <h4 className="font-semibold">Para Estudiar Hoy</h4>
            </div>
            <p className="text-3xl font-bold text-orange-700">{estadisticas.totalParaHoy}</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiAward className="text-purple-600 mr-2 text-xl" />
              <h4 className="font-semibold">Aprendidas</h4>
            </div>
            <p className="text-3xl font-bold text-purple-700">{estadisticas.totalAprendidas}</p>
          </div>
        </div>

        {/* Distribución por estado */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold mb-3">Distribución por Estado</h4>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{estadisticas.totalNuevas}</div>
                <div className="text-sm text-gray-600">Nuevas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{estadisticas.totalAprendiendo}</div>
                <div className="text-sm text-gray-600">Aprendiendo</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{estadisticas.totalRepasando}</div>
                <div className="text-sm text-gray-600">Repasando</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{estadisticas.totalAprendidas}</div>
                <div className="text-sm text-gray-600">Aprendidas</div>
              </div>
            </div>
          </div>
        </div>

        {/* Colecciones con más actividad */}
        {estadisticas.coleccionesConMasActividad.length > 0 && (
          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-3">Colecciones con Más Actividad</h4>
            <div className="space-y-3">
              {estadisticas.coleccionesConMasActividad.map((coleccion, index) => (
                <div key={coleccion.id} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="text-lg font-bold text-blue-600 mr-3">#{index + 1}</div>
                      <div>
                        <p className="font-medium text-gray-900">{coleccion.titulo}</p>
                        <p className="text-sm text-gray-600">
                          {coleccion.totalRevisiones} flashcards total
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-orange-600">{coleccion.paraHoy}</div>
                      <div className="text-sm text-gray-600">para hoy</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Resumen de progreso */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold mb-3">Resumen de Progreso</h4>
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {estadisticas.totalFlashcards > 0 
                    ? Math.round((estadisticas.totalAprendidas / estadisticas.totalFlashcards) * 100)
                    : 0}%
                </div>
                <div className="text-sm text-gray-600">Progreso General</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {estadisticas.totalFlashcards > 0 
                    ? Math.round(((estadisticas.totalAprendidas + estadisticas.totalRepasando) / estadisticas.totalFlashcards) * 100)
                    : 0}%
                </div>
                <div className="text-sm text-gray-600">En Proceso</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {estadisticas.totalParaHoy}
                </div>
                <div className="text-sm text-gray-600">Pendientes Hoy</div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlashcardGeneralStatistics;
