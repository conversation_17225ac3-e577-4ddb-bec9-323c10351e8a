// src/app/api/user/profile/route.ts
// API para gestión de perfiles de usuario

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { PlanValidationService } from '@/lib/services/planValidation';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );
    
    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }
    
    // Obtener información completa del usuario
    const accessInfo = await PlanValidationService.getUserAccessInfo(user.id);
    console.log('[API /user/profile] accessInfo recuperado:', JSON.stringify(accessInfo, null, 2));

    // Consulta directa para comparar
    const { data: profileFromDB, error: profileErrorDB } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();
    console.log('[API /user/profile] Perfil directo de BD:', JSON.stringify(profileFromDB, null, 2));
    if (profileErrorDB) console.error('[API /user/profile] Error perfil directo BD:', profileErrorDB);

    if (!accessInfo) {
      return NextResponse.json(
        { error: 'Perfil no encontrado' },
        { status: 404 }
      );
    }
    
    // Obtener perfil completo de la base de datos
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json(
        { error: 'Error obteniendo perfil' },
        { status: 500 }
      );
    }
    
    // Verificar si necesita upgrade
    const upgradeCheck = await PlanValidationService.checkUpgradeNeeded(user.id);
    
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.user_metadata?.name || user.email?.split('@')[0],
        created_at: user.created_at
      },
      profile: {
        ...profile,
        plan_name: profile.subscription_plan
      },
      access: {
        plan: accessInfo.plan || 'free',
        features: Array.isArray(accessInfo.features) ? accessInfo.features : [],
        limits: accessInfo.limits || {},
        currentUsage: accessInfo.currentUsage || {},
        paymentVerified: accessInfo.paymentVerified || false
      },
      upgrade: upgradeCheck || { needsUpgrade: false },
      tokenUsage: {
        current: profile.current_month_tokens || 0,
        limit: profile.monthly_token_limit || 0,
        percentage: profile.monthly_token_limit > 0
          ? Math.round(((profile.current_month_tokens || 0) / profile.monthly_token_limit) * 100)
          : 0,
        remaining: Math.max(0, (profile.monthly_token_limit || 0) - (profile.current_month_tokens || 0))
      }
    });
    
  } catch (error) {
    console.error('Error in profile API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );
    
    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { name, preferences } = body;
    
    // Actualizar metadata del usuario en Auth usando admin client
    if (name) {
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        user.id,
        { user_metadata: { name } }
      );

      if (updateError) {
        console.error('Error updating user metadata:', updateError);
      }
    }
    
    // Actualizar preferencias en el perfil
    if (preferences) {
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          security_flags: {
            ...preferences,
            updated_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id);
      
      if (profileError) {
        console.error('Error updating profile preferences:', profileError);
        return NextResponse.json(
          { error: 'Error actualizando preferencias' },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Perfil actualizado correctamente'
    });
    
  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
