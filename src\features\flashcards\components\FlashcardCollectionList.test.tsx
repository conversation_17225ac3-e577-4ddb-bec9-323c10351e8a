import React from 'react';
import { render, screen } from '@testing-library/react';
import FlashcardCollectionList from './FlashcardCollectionList';
import { ColeccionFlashcards } from '@/lib/supabase'; // Adjust path as necessary

describe('FlashcardCollectionList', () => {
  const mockOnSeleccionarColeccion = jest.fn();

  const baseProps = {
    coleccionSeleccionada: null,
    onSeleccionarColeccion: mockOnSeleccionarColeccion,
    isLoading: false,
  };

  test('renders empty state correctly', () => {
    render(<FlashcardCollectionList {...baseProps} colecciones={[]} />);

    expect(screen.getByText('No hay colecciones de flashcards disponibles.')).toBeInTheDocument();
    expect(screen.getByText('Crea una nueva colección para empezar a estudiar.')).toBeInTheDocument();
    
    // Check for the FiInbox icon (usually renders as an SVG)
    const svgIcon = screen.getByRole('graphics-document'); // FiInbox is an SVG, often gets this role or check parent container
    expect(svgIcon).toBeInTheDocument();
    // More specific check if needed, e.g. by checking parent div's class for icon styling
    const iconContainer = svgIcon.parentElement;
    expect(iconContainer).toHaveClass('mx-auto text-6xl text-gray-400 mb-4');

  });

  test('renders list of collections with flashcard counts', () => {
    const mockColecciones: ColeccionFlashcards[] = [
      {
        id: '1',
        titulo: 'Collection One',
        descripcion: 'Desc 1',
        creado_en: new Date().toISOString(),
        actualizado_en: new Date().toISOString(),
        user_id: 'user1',
        numero_flashcards: 10,
        pendientes_hoy: 5,
      },
      {
        id: '2',
        titulo: 'Collection Two',
        descripcion: 'Desc 2',
        creado_en: new Date().toISOString(),
        actualizado_en: new Date().toISOString(),
        user_id: 'user1',
        numero_flashcards: 0,
        pendientes_hoy: 0,
      },
      {
        id: '3',
        titulo: 'Collection Three',
        // @ts-ignore testing undefined case for numero_flashcards
        numero_flashcards: undefined,
        creado_en: new Date().toISOString(),
        actualizado_en: new Date().toISOString(),
        user_id: 'user1',
      },
    ];

    render(<FlashcardCollectionList {...baseProps} colecciones={mockColecciones} />);

    expect(screen.getByText('Collection One')).toBeInTheDocument();
    expect(screen.getByText('Flashcards: 10')).toBeInTheDocument();

    expect(screen.getByText('Collection Two')).toBeInTheDocument();
    expect(screen.getByText('Flashcards: 0')).toBeInTheDocument();
    
    expect(screen.getByText('Collection Three')).toBeInTheDocument();
    expect(screen.getByText('Flashcards: N/A')).toBeInTheDocument();
  });

  test('renders loading state correctly', () => {
    render(<FlashcardCollectionList {...baseProps} colecciones={[]} isLoading={true} />);
    // Check for the spinner, typically by role or a specific class if available
    // For this example, assuming a div with animate-spin is the spinner:
    const spinner = screen.getByRole('status', { hidden: true }) || screen.getByRole('progressbar', { hidden: true }); // Or a more specific selector
    // If using a div with specific classes:
    // const spinner = document.querySelector('.animate-spin.rounded-full.h-8.w-8.border-b-2.border-gray-900');
    expect(spinner).toBeInTheDocument();
  });

  test('calls onSeleccionarColeccion when a collection is clicked', () => {
    const mockColecciones: ColeccionFlashcards[] = [
      {
        id: '1',
        titulo: 'Clickable Collection',
        creado_en: new Date().toISOString(),
        actualizado_en: new Date().toISOString(),
        user_id: 'user1',
        numero_flashcards: 5,
      },
    ];
    render(<FlashcardCollectionList {...baseProps} colecciones={mockColecciones} />);

    const collectionCard = screen.getByText('Clickable Collection');
    fireEvent.click(collectionCard); // Need to import fireEvent

    expect(mockOnSeleccionarColeccion).toHaveBeenCalledTimes(1);
    expect(mockOnSeleccionarColeccion).toHaveBeenCalledWith(mockColecciones[0]);
  });
});

// Helper to re-add fireEvent as it might be removed by automated tool
import { fireEvent } from '@testing-library/react';
