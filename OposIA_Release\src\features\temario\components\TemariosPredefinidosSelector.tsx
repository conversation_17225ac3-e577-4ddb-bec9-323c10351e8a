import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Info, FiArrowRight, FiLoader } from 'react-icons/fi';
import { 
  obtenerTemariosPredefinidos, 
  cargarTemarioPredefinido, 
  buscarTemariosPredefinidos,
  obtenerEstadisticasTemarioPredefinido,
  TemarioPredefinido 
} from '../services/temariosPredefinidosService';
import { toast } from 'react-hot-toast';

interface TemariosPredefinidosSelectorProps {
  onSeleccionar: (temario: TemarioPredefinido) => void;
  onVolver: () => void;
}

const TemariosPredefinidosSelector: React.FC<TemariosPredefinidosSelectorProps> = ({ 
  onSeleccionar, 
  onVolver 
}) => {
  const [temarios, setTemarios] = useState<Omit<TemarioPredefinido, 'temas'>[]>([]);
  const [busqueda, setBusqueda] = useState('');
  const [temarioSeleccionado, setTemarioSeleccionado] = useState<string | null>(null);
  const [cargandoTemario, setCargandoTemario] = useState<string | null>(null);
  const [estadisticas, setEstadisticas] = useState<Record<string, any>>({});

  useEffect(() => {
    cargarTemarios();
  }, []);

  useEffect(() => {
    const temasriosFiltrados = buscarTemariosPredefinidos(busqueda);
    setTemarios(temasriosFiltrados);
  }, [busqueda]);

  const cargarTemarios = () => {
    const temariosList = obtenerTemariosPredefinidos();
    setTemarios(temariosList);
    
    // Cargar estadísticas para cada temario
    temariosList.forEach(async (temario) => {
      const stats = await obtenerEstadisticasTemarioPredefinido(temario.id);
      if (stats) {
        setEstadisticas(prev => ({
          ...prev,
          [temario.id]: stats
        }));
      }
    });
  };

  const handleSeleccionarTemario = async (temarioId: string) => {
    setCargandoTemario(temarioId);
    try {
      const temarioCompleto = await cargarTemarioPredefinido(temarioId);
      if (temarioCompleto) {
        onSeleccionar(temarioCompleto);
        toast.success('Temario predefinido cargado exitosamente');
      } else {
        toast.error('Error al cargar el temario predefinido');
      }
    } catch (error) {
      console.error('Error al cargar temario:', error);
      toast.error('Error al cargar el temario predefinido');
    } finally {
      setCargandoTemario(null);
    }
  };

  const formatearDescripcion = (descripcion: string, maxLength: number = 150) => {
    if (descripcion.length <= maxLength) return descripcion;
    return descripcion.substring(0, maxLength) + '...';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={onVolver}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium mb-4 flex items-center"
          >
            ← Volver a la selección
          </button>
          
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Seleccionar Temario Predefinido
            </h1>
            <p className="text-lg text-gray-600 mb-2">
              Elige uno de nuestros temarios oficiales predefinidos
            </p>
            <p className="text-gray-500">
              Estos temarios están basados en convocatorias oficiales y contienen todos los temas necesarios
            </p>
          </div>
        </div>

        {/* Barra de búsqueda */}
        <div className="mb-6">
          <div className="relative max-w-md mx-auto">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar por cuerpo, nivel o descripción..."
              value={busqueda}
              onChange={(e) => setBusqueda(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Lista de temarios */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {temarios.map((temario) => {
            const stats = estadisticas[temario.id];
            const isLoading = cargandoTemario === temario.id;
            
            return (
              <div
                key={temario.id}
                className={`bg-white rounded-xl shadow-sm border-2 transition-all duration-200 hover:shadow-md h-full flex flex-col ${
                  temarioSeleccionado === temario.id
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                <div className="p-6 flex flex-col h-full">
                  {/* Contenido principal que crece */}
                  <div className="flex-grow">
                    {/* Header del temario */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <FiBook className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                            {temario.nombre}
                          </h3>
                          <p className="text-xs text-gray-500 mt-1">
                            {temario.cuerpo}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Descripción */}
                    <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                      {formatearDescripcion(temario.descripcion)}
                    </p>

                    {/* Estadísticas */}
                    {stats && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Total de temas:</span>
                          <span className="font-semibold text-gray-900">{stats.totalTemas}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm mt-1">
                          <span className="text-gray-600">Tipo:</span>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            Completo
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Botón de selección */}
                  <button
                    onClick={() => handleSeleccionarTemario(temario.id)}
                    disabled={isLoading}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center ${
                      isLoading
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800'
                    }`}
                  >
                    {isLoading ? (
                      <>
                        <FiLoader className="w-4 h-4 mr-2 animate-spin" />
                        Cargando...
                      </>
                    ) : (
                      <>
                        Seleccionar Temario
                        <FiArrowRight className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Mensaje si no hay resultados */}
        {temarios.length === 0 && (
          <div className="text-center py-12">
            <FiInfo className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No se encontraron temarios
            </h3>
            <p className="text-gray-600">
              Intenta con otros términos de búsqueda o revisa la ortografía
            </p>
          </div>
        )}

        {/* Información adicional */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <FiInfo className="w-5 h-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
            <div className="text-blue-800">
              <h4 className="font-semibold mb-2">Sobre los temarios predefinidos</h4>
              <ul className="text-sm space-y-1">
                <li>• Basados en convocatorias oficiales reales</li>
                <li>• Incluyen todos los temas necesarios para la oposición</li>
                <li>• Optimizados para usar con las funciones de IA de la plataforma</li>
                <li>• Se pueden personalizar después de la importación</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemariosPredefinidosSelector;
