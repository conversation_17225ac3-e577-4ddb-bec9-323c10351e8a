// src/features/profile/components/NotificationHistory.tsx
// Componente para mostrar el historial de notificaciones del usuario

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  FiBell, 
  FiMail, 
  FiAlertCircle, 
  FiCheck, 
  FiClock, 
  FiX,
  FiRefreshCw,
  FiFilter
} from 'react-icons/fi';

interface Notification {
  id: string;
  type: string;
  subject: string;
  sentAt: string;
  status: string;
  metadata?: {
    planName?: string;
    gracePeriodEnd?: string;
    hoursRemaining?: number;
    daysRemaining?: number;
  };
}

interface NotificationHistoryProps {
  userId: string;
}

export default function NotificationHistory({ userId }: NotificationHistoryProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [total, setTotal] = useState(0);

  const loadNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: '20'
      });

      if (filter !== 'all') {
        params.append('type', filter);
      }

      const response = await fetch(`/api/user/notifications?${params}`);

      if (!response.ok) {
        throw new Error('Error cargando notificaciones');
      }

      const data = await response.json();

      if (data.success) {
        setNotifications(data.data.notifications);
        setTotal(data.data.total);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error loading notifications:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, [filter]);

  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'subscription_cancelled':
        return <FiX className="w-5 h-5 text-red-500" />;
      case 'grace_period_ending':
        return <FiClock className="w-5 h-5 text-yellow-500" />;
      case 'plan_expired':
        return <FiAlertCircle className="w-5 h-5 text-red-500" />;
      case 'payment_failed':
        return <FiX className="w-5 h-5 text-red-500" />;
      case 'welcome':
        return <FiCheck className="w-5 h-5 text-green-500" />;
      default:
        return <FiBell className="w-5 h-5 text-blue-500" />;
    }
  };

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case 'subscription_cancelled':
        return 'Suscripción Cancelada';
      case 'grace_period_ending':
        return 'Período de Gracia';
      case 'plan_expired':
        return 'Plan Expirado';
      case 'payment_failed':
        return 'Pago Fallido';
      case 'welcome':
        return 'Bienvenida';
      default:
        return 'Notificación';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <FiCheck className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <FiX className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <FiClock className="w-4 h-4 text-yellow-500" />;
      default:
        return <FiMail className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Hace unos minutos';
    } else if (diffInHours < 24) {
      return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
    } else {
      return date.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const filterOptions = [
    { value: 'all', label: 'Todas las notificaciones' },
    { value: 'subscription_cancelled', label: 'Suscripciones canceladas' },
    { value: 'grace_period_ending', label: 'Períodos de gracia' },
    { value: 'plan_expired', label: 'Planes expirados' },
    { value: 'payment_failed', label: 'Pagos fallidos' },
    { value: 'welcome', label: 'Bienvenida' },
  ];

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Historial de Notificaciones</h2>
        <button
          onClick={loadNotifications}
          disabled={loading}
          className="flex items-center text-blue-600 hover:text-blue-700 transition-colors disabled:opacity-50"
        >
          <FiRefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
          Actualizar
        </button>
      </div>

      {/* Filtros */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <FiFilter className="w-4 h-4 text-gray-500" />
          <label className="text-sm font-medium text-gray-700">Filtrar por tipo:</label>
        </div>
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {filterOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Estadísticas */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{total}</div>
            <div className="text-sm text-gray-600">Total de notificaciones</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {notifications.filter(n => n.status === 'sent').length}
            </div>
            <div className="text-sm text-gray-600">Enviadas exitosamente</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {notifications.filter(n => n.status === 'failed').length}
            </div>
            <div className="text-sm text-gray-600">Fallos de envío</div>
          </div>
        </div>
      </div>

      {/* Lista de notificaciones */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error cargando notificaciones</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadNotifications}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Reintentar
          </button>
        </div>
      ) : notifications.length === 0 ? (
        <div className="text-center py-8">
          <FiBell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay notificaciones</h3>
          <p className="text-gray-600">
            {filter === 'all' 
              ? 'Aún no has recibido ninguna notificación por email.'
              : `No hay notificaciones del tipo "${filterOptions.find(f => f.value === filter)?.label}".`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-blue-600">
                        {getNotificationTypeLabel(notification.type)}
                      </span>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(notification.status)}
                        <span className="text-xs text-gray-500 capitalize">
                          {notification.status}
                        </span>
                      </div>
                    </div>
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      {notification.subject}
                    </h4>
                    <p className="text-xs text-gray-500">
                      {formatDate(notification.sentAt)}
                    </p>
                    
                    {/* Metadata adicional */}
                    {notification.metadata && (
                      <div className="mt-2 text-xs text-gray-600">
                        {notification.metadata.planName && (
                          <span className="inline-block bg-gray-100 px-2 py-1 rounded mr-2">
                            Plan: {notification.metadata.planName}
                          </span>
                        )}
                        {notification.metadata.daysRemaining !== undefined && (
                          <span className="inline-block bg-yellow-100 px-2 py-1 rounded mr-2">
                            {notification.metadata.daysRemaining} días restantes
                          </span>
                        )}
                        {notification.metadata.hoursRemaining !== undefined && (
                          <span className="inline-block bg-red-100 px-2 py-1 rounded mr-2">
                            {notification.metadata.hoursRemaining} horas restantes
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
