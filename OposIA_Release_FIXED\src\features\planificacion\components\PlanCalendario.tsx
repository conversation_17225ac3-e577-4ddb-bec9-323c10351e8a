/**
 * Componente de calendario para el plan de estudios
 * Muestra un calendario mensual con indicadores visuales para días con tareas
 */

import React from 'react';
import { FiChevronLeft, FiChevronRight, FiCalendar, FiHome } from 'react-icons/fi';
import { PlanCalendarioProps, DiaCalendario } from '../types/calendarTypes';
import { usePlanCalendario } from '@/hooks/usePlanCalendario';
import { DIAS_SEMANA_CORTOS } from '@/lib/utils/dateUtils';

const PlanCalendario: React.FC<PlanCalendarioProps> = ({
  plan,
  progresoPlan,
  fechaSeleccionada,
  onFechaSeleccionada,
  onMesChanged,
  className = ''
}) => {
  const {
    estadoCalendario,
    isLoading,
    error,
    navegarMes,
    irAHoy,
    tituloMes,
    esFechaSeleccionable
  } = usePlanCalendario(plan, progresoPlan, fechaSeleccionada);

  // Manejar clic en un día
  const handleDiaClick = (diaCalendario: DiaCalendario) => {
    if (!esFechaSeleccionable(diaCalendario.fecha)) {
      return;
    }
    onFechaSeleccionada(diaCalendario.fecha);
  };

  // Manejar navegación de mes
  const handleNavegacionMes = (direccion: 'anterior' | 'siguiente') => {
    navegarMes(direccion);
    if (onMesChanged) {
      const nuevoYear = estadoCalendario.yearActual;
      const nuevoMes = estadoCalendario.mesActual;
      onMesChanged(nuevoYear, nuevoMes);
    }
  };

  // Manejar navegación por teclado
  const handleKeyDown = (event: React.KeyboardEvent, diaCalendario: DiaCalendario) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleDiaClick(diaCalendario);
    }
  };

  // Manejar navegación por teclado en controles
  const handleControlKeyDown = (event: React.KeyboardEvent, action: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      action();
    }
  };

  // Obtener clases CSS para un día
  const obtenerClasesDia = (diaCalendario: DiaCalendario): string => {
    const clases = [
      'relative',
      'aspect-square',
      'flex',
      'items-center',
      'justify-center',
      'text-xs',
      'sm:text-sm',
      'font-medium',
      'cursor-pointer',
      'calendario-day-hover',
      'calendario-estado-transition',
      'rounded-none',
      'sm:rounded-lg',
      'border',
      'border-transparent',
      'min-h-[2.5rem]',
      'sm:min-h-[3rem]'
    ];

    // Estilos base según si está en el mes actual
    if (!diaCalendario.estaEnMesActual) {
      clases.push('text-gray-300', 'hover:text-gray-400');
    } else {
      clases.push('text-gray-700', 'hover:text-gray-900');
    }

    // Estilos según el estado del día
    switch (diaCalendario.estado) {
      case 'hoy':
        clases.push(
          'bg-blue-100',
          'text-blue-900',
          'border-blue-300',
          'font-bold',
          'ring-2',
          'ring-blue-400',
          'ring-opacity-50',
          'calendario-pulso'
        );
        break;
      
      case 'con-tareas':
        clases.push(
          'bg-orange-50',
          'text-orange-800',
          'border-orange-200',
          'hover:bg-orange-100',
          'hover:border-orange-300'
        );
        break;
      
      case 'completado':
        clases.push(
          'bg-green-50',
          'text-green-800',
          'border-green-200',
          'hover:bg-green-100',
          'hover:border-green-300'
        );
        break;
      
      case 'parcial':
        clases.push(
          'bg-yellow-50',
          'text-yellow-800',
          'border-yellow-200',
          'hover:bg-yellow-100',
          'hover:border-yellow-300'
        );
        break;
      
      case 'normal':
        if (diaCalendario.estaEnMesActual) {
          clases.push(
            'hover:bg-gray-50',
            'hover:border-gray-200'
          );
        }
        break;
      
      case 'fuera-mes':
        // Ya manejado arriba
        break;
    }

    // Resaltar día seleccionado
    if (fechaSeleccionada && 
        diaCalendario.fecha.getTime() === fechaSeleccionada.getTime()) {
      clases.push(
        'ring-2',
        'ring-blue-500',
        'ring-opacity-75',
        'bg-blue-50',
        'border-blue-300'
      );
    }

    // Deshabilitar días no seleccionables
    if (!esFechaSeleccionable(diaCalendario.fecha)) {
      clases.push('cursor-not-allowed', 'opacity-50');
    }

    return clases.join(' ');
  };

  // Obtener indicador visual para un día
  const obtenerIndicadorDia = (diaCalendario: DiaCalendario) => {
    if (diaCalendario.totalTareas === 0) return null;

    const porcentaje = diaCalendario.porcentajeCompletado;
    let colorIndicador = 'bg-orange-400'; // Por defecto: tareas pendientes

    if (porcentaje === 100) {
      colorIndicador = 'bg-green-400'; // Completado
    } else if (porcentaje > 0) {
      colorIndicador = 'bg-yellow-400'; // Parcial
    }

    return (
      <div className="absolute bottom-1 right-1">
        <div 
          className={`w-2 h-2 rounded-full ${colorIndicador}`}
          title={`${diaCalendario.tareasCompletadas}/${diaCalendario.totalTareas} tareas completadas`}
        />
      </div>
    );
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center text-red-800">
          <FiCalendar className="w-5 h-5 mr-2" />
          <span className="font-medium">Error en el calendario</span>
        </div>
        <p className="text-red-600 text-sm mt-1">{error}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-fade-in ${className}`}>
      {/* Header del calendario */}
      <div className="bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <button
            onClick={() => handleNavegacionMes('anterior')}
            onKeyDown={(e) => handleControlKeyDown(e, () => handleNavegacionMes('anterior'))}
            className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors"
            aria-label="Mes anterior"
            tabIndex={0}
          >
            <FiChevronLeft className="w-5 h-5 text-gray-600" />
          </button>
          
          <div className="flex items-center space-x-2">
            <FiCalendar className="w-4 h-4 text-gray-600 hidden sm:block" />
            <h3 className="font-semibold text-gray-900 text-sm sm:text-base">{tituloMes}</h3>
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={irAHoy}
              onKeyDown={(e) => handleControlKeyDown(e, irAHoy)}
              className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors"
              title="Ir a hoy"
              aria-label="Ir a hoy"
              tabIndex={0}
            >
              <FiHome className="w-4 h-4 text-gray-600" />
            </button>
            <button
              onClick={() => handleNavegacionMes('siguiente')}
              onKeyDown={(e) => handleControlKeyDown(e, () => handleNavegacionMes('siguiente'))}
              className="p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors"
              aria-label="Mes siguiente"
              tabIndex={0}
            >
              <FiChevronRight className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Días de la semana */}
      <div className="grid grid-cols-7 bg-gray-100 border-b border-gray-200">
        {DIAS_SEMANA_CORTOS.map((dia) => (
          <div
            key={dia}
            className="py-1 sm:py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wide"
          >
            {dia}
          </div>
        ))}
      </div>

      {/* Grid del calendario */}
      <div className="grid grid-cols-7 gap-0">
        {isLoading ? (
          // Estado de carga
          Array.from({ length: 42 }, (_, index) => (
            <div
              key={index}
              className="aspect-square flex items-center justify-center border-r border-b border-gray-100 last:border-r-0"
            >
              <div className="w-6 h-6 bg-gray-200 rounded animate-pulse" />
            </div>
          ))
        ) : (
          // Días del calendario
          estadoCalendario.diasCalendario.map((diaCalendario, index) => (
            <div
              key={index}
              className={`border-r border-b border-gray-100 last:border-r-0 ${
                Math.floor(index / 7) === 5 ? 'border-b-0' : ''
              }`}
            >
              <button
                onClick={() => handleDiaClick(diaCalendario)}
                onKeyDown={(e) => handleKeyDown(e, diaCalendario)}
                className={`${obtenerClasesDia(diaCalendario)} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
                disabled={!esFechaSeleccionable(diaCalendario.fecha)}
                tabIndex={esFechaSeleccionable(diaCalendario.fecha) ? 0 : -1}
                aria-label={`${diaCalendario.dia} de ${tituloMes}${
                  diaCalendario.totalTareas > 0
                    ? `, ${diaCalendario.totalTareas} tareas`
                    : ''
                }${diaCalendario.esHoy ? ', hoy' : ''}${
                  diaCalendario.estado === 'completado' ? ', completado' :
                  diaCalendario.estado === 'parcial' ? ', parcialmente completado' :
                  diaCalendario.estado === 'con-tareas' ? ', con tareas pendientes' : ''
                }`}
                aria-pressed={fechaSeleccionada &&
                  diaCalendario.fecha.getTime() === fechaSeleccionada.getTime() ? 'true' : 'false'}
              >
                {diaCalendario.dia}
                {obtenerIndicadorDia(diaCalendario)}
              </button>
            </div>
          ))
        )}
      </div>

      {/* Leyenda */}
      <div className="bg-gray-50 px-3 sm:px-4 py-2 border-t border-gray-200">
        <div className="flex items-center justify-center space-x-2 sm:space-x-4 text-xs text-gray-600">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-orange-400 rounded-full" />
            <span className="hidden sm:inline">Pendientes</span>
            <span className="sm:hidden">Pend.</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-yellow-400 rounded-full" />
            <span className="hidden sm:inline">Parcial</span>
            <span className="sm:hidden">Parc.</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-400 rounded-full" />
            <span className="hidden sm:inline">Completado</span>
            <span className="sm:hidden">Comp.</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlanCalendario;
