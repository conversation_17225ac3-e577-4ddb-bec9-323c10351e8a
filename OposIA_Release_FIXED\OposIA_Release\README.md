# OposIA - Sistema de Preparación de Oposiciones con IA

## Inicio Rápido

1. **Configurar variables de entorno**: El archivo `.env.local` ya está incluido con la configuración actual
2. **Iniciar aplicación**: Ejecutar `INICIAR_APLICACION.bat`
3. **Abrir navegador**: http://localhost:3000

## Requisitos

- Node.js 18+ (https://nodejs.org/)
- Conexión a internet
- Cuentas configuradas en:
  - Supabase (base de datos)
  - OpenAI (inteligencia artificial)
  - Stripe (pagos)

## Estructura

- `src/` - Código fuente completo
- `public/` - Archivos estáticos y recursos
- `.env.local` - Variables de entorno configuradas
- `INICIAR_APLICACION.bat` - Iniciador automático
- `COMO_INICIAR.txt` - Instrucciones detalladas

## Nuevas Funcionalidades Incluidas

### Sistema de Cookies y Privacidad
- ✅ Banner de consentimiento de cookies
- ✅ Gestión de preferencias de usuario
- ✅ Página de política de cookies
- ✅ Cumplimiento RGPD

### APIs Administrativas Avanzadas
- ✅ Limpieza automática de cuentas expiradas
- ✅ Recordatorios de período de gracia
- ✅ Procesamiento de períodos de gracia expirados
- ✅ Reactivación manual de usuarios
- ✅ Pre-registro de usuarios pagos

### Mejoras en Autenticación
- ✅ Registro mejorado con envío automático de emails
- ✅ Gestión avanzada de sesiones
- ✅ Validaciones de seguridad mejoradas

### Características Principales

- 📄 Gestión de documentos PDF
- 🤖 Generación de tests con IA
- 🃏 Sistema de flashcards inteligente
- 🧠 Creación de mapas mentales
- 💬 Chat tutor con IA
- 📅 Planificación de estudios
- 👤 Sistema completo de usuarios
- 💳 Gestión de suscripciones y pagos
- 🔐 Panel administrativo avanzado
- 📊 Estadísticas de estudio
- 🍪 Gestión de cookies y privacidad
- ⚙️ APIs administrativas automatizadas

## Tecnologías

- **Frontend**: Next.js 14 + React + TypeScript
- **Estilos**: Tailwind CSS
- **Base de datos**: Supabase (PostgreSQL)
- **IA**: OpenAI GPT-4
- **Pagos**: Stripe
- **Autenticación**: Supabase Auth + JWT
- **Administración**: APIs automatizadas

## APIs Administrativas

### Gestión de Usuarios
- `/api/admin/cleanup-expired-free` - Limpieza de cuentas expiradas
- `/api/admin/send-grace-period-reminders` - Envío de recordatorios
- `/api/admin/process-expired-grace-periods` - Procesamiento automático
- `/api/admin/reactivate-user` - Reactivación manual

### Autenticación
- `/api/auth/register-free` - Registro con email automático
- `/api/auth/pre-register-paid` - Pre-registro para planes pagos

## Seguridad

- ✅ Autenticación de administradores
- ✅ Validación de permisos
- ✅ Protección contra CSRF
- ✅ Gestión segura de sesiones
- ✅ Cumplimiento RGPD

Para más información, consultar `COMO_INICIAR.txt`
