// src/lib/services/email/emailTemplates.ts
// Templates para diferentes tipos de notificaciones por email

import { EmailTemplate, SecurityAlertData } from './types';

export class EmailTemplates {
  
  /**
   * Template para notificación de cancelación de suscripción
   */
  static generateSubscriptionCancelledEmail(
    userName: string,
    planName: string,
    gracePeriodEnd: string
  ): EmailTemplate {
    const gracePeriodDate = new Date(gracePeriodEnd);
    const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const daysRemaining = Math.ceil(
      (gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    );

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Suscripción Cancelada - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">Suscripción Cancelada</h1>
          
          <p>Hola ${userName},</p>
          
          <p>Hemos recibido tu solicitud de cancelación de la suscripción al <strong>Plan ${planName}</strong>.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #059669;">📅 Período de Gracia Activo</h3>
            <p><strong>Mantienes acceso completo hasta:</strong> ${formattedDate}</p>
            <p><strong>Días restantes:</strong> ${daysRemaining} días</p>
            <p>Durante este período, puedes seguir usando todas las funciones de tu plan actual.</p>
          </div>
          
          <h3>¿Qué sucede después?</h3>
          <ul>
            <li>Tu acceso a las funciones premium finalizará el ${formattedDate}</li>
            <li>Tu cuenta se convertirá automáticamente al Plan Gratuito</li>
            <li>Conservarás acceso a las funciones básicas de OposI</li>
            <li>Tus documentos y progreso se mantendrán guardados</li>
          </ul>
          
          <h3>¿Cambiaste de opinión?</h3>
          <p>Si deseas reactivar tu suscripción, puedes hacerlo en cualquier momento desde tu panel de control:</p>
          <p style="text-align: center; margin: 20px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Reactivar Suscripción
            </a>
          </p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          
          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>
            Equipo de OposI
          </p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
Suscripción Cancelada - OposI

Hola ${userName},

Hemos recibido tu solicitud de cancelación de la suscripción al Plan ${planName}.

PERÍODO DE GRACIA ACTIVO:
- Mantienes acceso completo hasta: ${formattedDate}
- Días restantes: ${daysRemaining} días

¿Qué sucede después?
- Tu acceso a las funciones premium finalizará el ${formattedDate}
- Tu cuenta se convertirá automáticamente al Plan Gratuito
- Conservarás acceso a las funciones básicas de OposI
- Tus documentos y progreso se mantendrán guardados

¿Cambiaste de opinión?
Puedes reactivar tu suscripción en cualquier momento desde: ${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
    `;

    const subject = `Suscripción cancelada - Acceso hasta el ${gracePeriodDate.toLocaleDateString('es-ES')}`;

    return {
      htmlContent,
      textContent,
      subject
    };
  }

  /**
   * Template para recordatorio de que el período de gracia está por terminar
   */
  static generateGracePeriodEndingEmail(
    userName: string,
    planName: string,
    gracePeriodEnd: string
  ): EmailTemplate {
    const gracePeriodDate = new Date(gracePeriodEnd);
    const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const hoursRemaining = Math.ceil(
      (gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60)
    );

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Tu acceso premium termina pronto - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #dc2626;">⏰ Tu acceso premium termina pronto</h1>
          
          <p>Hola ${userName},</p>
          
          <p>Te recordamos que tu acceso al <strong>Plan ${planName}</strong> terminará el <strong>${formattedDate}</strong> (en aproximadamente ${hoursRemaining} horas).</p>
          
          <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <h3 style="margin-top: 0; color: #92400e;">¿Quieres continuar con tu plan premium?</h3>
            <p>Reactivar tu suscripción es fácil y rápido. Mantén acceso a todas las funciones avanzadas de OposI.</p>
          </div>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan" 
               style="background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Reactivar Mi Suscripción
            </a>
          </p>
          
          <p style="font-size: 14px; color: #6b7280;">
            Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.
          </p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
Tu acceso premium termina pronto - OposI

Hola ${userName},

Te recordamos que tu acceso al Plan ${planName} terminará el ${formattedDate} (en aproximadamente ${hoursRemaining} horas).

¿Quieres continuar con tu plan premium?
Reactivar tu suscripción: ${process.env.NEXT_PUBLIC_APP_URL}/upgrade-plan

Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.

Equipo de OposI
    `;

    const subject = `⏰ Tu Plan ${planName} termina en ${hoursRemaining} horas`;

    return {
      htmlContent,
      textContent,
      subject
    };
  }

  /**
   * Template base para otros tipos de notificaciones
   */
  static generateGenericEmail(
    userName: string,
    title: string,
    message: string,
    ctaText?: string,
    ctaUrl?: string
  ): EmailTemplate {
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${title} - OposI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2563eb;">${title}</h1>
          
          <p>Hola ${userName},</p>
          
          <p>${message}</p>
          
          ${ctaText && ctaUrl ? `
          <p style="text-align: center; margin: 30px 0;">
            <a href="${ctaUrl}" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              ${ctaText}
            </a>
          </p>
          ` : ''}
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          
          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta, no dudes en contactarnos.<br>
            Equipo de OposI
          </p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
${title} - OposI

Hola ${userName},

${message}

${ctaText && ctaUrl ? `${ctaText}: ${ctaUrl}` : ''}

Si tienes alguna pregunta, no dudes en contactarnos.
Equipo de OposI
    `;

    return {
      htmlContent,
      textContent,
      subject: title
    };
  }

  /**
   * Template para confirmación de upgrade de plan (SEGURIDAD)
   */
  static generatePlanUpgradeConfirmation(
    userName: string,
    newPlanId: string,
    confirmationToken: string
  ): EmailTemplate {
    const confirmationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/auth/confirm-upgrade?token=${confirmationToken}`;
    const planDisplayName = newPlanId === 'usuario' ? 'Usuario' : newPlanId === 'pro' ? 'Pro' : newPlanId;

    const subject = `⚠️ Acción Requerida: Confirma la actualización de tu plan en OposiAI`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Confirma la Actualización de tu Plan - OposiAI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin-bottom: 10px;">🔒 Confirma la Actualización de tu Plan</h1>
          </div>

          <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 4px;">
            <h3 style="margin-top: 0; color: #92400e;">⚠️ Acción de Seguridad Requerida</h3>
            <p style="margin-bottom: 0;"><strong>Hemos detectado un intento de actualización de plan en tu cuenta.</strong></p>
          </div>

          <p>Hola <strong>${userName}</strong>,</p>

          <p>Hemos recibido una solicitud para actualizar tu cuenta al <strong>Plan ${planDisplayName}</strong>. Esta solicitud se ha realizado mediante un pago asociado a tu dirección de email.</p>

          <p><strong>Para proteger tu cuenta, necesitamos que confirmes que has sido tú quien ha realizado esta acción.</strong></p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}"
               style="background-color: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
              ✅ Sí, actualizar mi plan
            </a>
          </div>

          <div style="background-color: #fee2e2; border-left: 4px solid #dc2626; padding: 20px; margin: 20px 0; border-radius: 4px;">
            <h4 style="margin-top: 0; color: #991b1b;">🚨 Si NO has solicitado esta actualización:</h4>
            <ul style="margin-bottom: 0;">
              <li>Por favor, <strong>ignora este email</strong></li>
              <li>Contacta inmediatamente con nuestro soporte</li>
              <li>Considera cambiar tu contraseña por seguridad</li>
            </ul>
          </div>

          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0; font-size: 14px; color: #6b7280;">
              <strong>Información de seguridad:</strong><br>
              • Este enlace es válido durante 24 horas<br>
              • Solo tú puedes confirmar esta actualización<br>
              • El pago se procesará únicamente tras tu confirmación
            </p>
          </div>

          <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

          <p style="font-size: 14px; color: #6b7280;">
            Si tienes alguna pregunta sobre esta solicitud, contacta con nuestro equipo de soporte.<br>
            <strong>Equipo de OposiAI</strong>
          </p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
⚠️ ACCIÓN REQUERIDA: Confirma la actualización de tu plan en OposiAI

Hola ${userName},

🔒 ACCIÓN DE SEGURIDAD REQUERIDA

Hemos recibido una solicitud para actualizar tu cuenta al Plan ${planDisplayName}. Esta solicitud se ha realizado mediante un pago asociado a tu dirección de email.

Para proteger tu cuenta, necesitamos que confirmes que has sido tú quien ha realizado esta acción.

CONFIRMAR ACTUALIZACIÓN:
${confirmationUrl}

🚨 SI NO HAS SOLICITADO ESTA ACTUALIZACIÓN:
- Por favor, ignora este email
- Contacta inmediatamente con nuestro soporte
- Considera cambiar tu contraseña por seguridad

INFORMACIÓN DE SEGURIDAD:
• Este enlace es válido durante 24 horas
• Solo tú puedes confirmar esta actualización
• El pago se procesará únicamente tras tu confirmación

Si tienes alguna pregunta sobre esta solicitud, contacta con nuestro equipo de soporte.

Equipo de OposiAI
    `;

    return {
      htmlContent,
      textContent,
      subject
    };
  }

  /**
   * Template para alerta de seguridad al administrador
   */
  static generateSecurityAlertEmail(alertData: SecurityAlertData): EmailTemplate {
    const formattedAmount = (alertData.paymentAmount / 100).toFixed(2);
    const formattedTimestamp = new Date(alertData.timestamp).toLocaleString('es-ES');
    const planDisplayName = alertData.requestedPlan === 'usuario' ? 'Usuario' :
                           alertData.requestedPlan === 'pro' ? 'Pro' : alertData.requestedPlan;

    const subject = `🚨 ALERTA DE SEGURIDAD: Discrepancia en pago - ${alertData.payerEmail}`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Alerta de Seguridad - OposiAI</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold;">🚨 ALERTA DE SEGURIDAD</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Discrepancia detectada en pago</p>
        </div>

        <div style="background: #fef2f2; border: 2px solid #fecaca; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h2 style="color: #dc2626; margin-top: 0;">⚠️ Discrepancia Detectada</h2>
          <p style="margin-bottom: 15px;">Se ha detectado un pago realizado por un email diferente al propietario de la cuenta:</p>

          <div style="background: white; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <p style="margin: 5px 0;"><strong>👤 Email del pagador:</strong> ${alertData.payerEmail}</p>
            <p style="margin: 5px 0;"><strong>🏠 Propietario de la cuenta:</strong> ${alertData.accountOwnerEmail}</p>
          </div>
        </div>

        <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="margin-top: 0; color: #374151;">📊 Detalles del Pago</h3>
          <ul style="list-style: none; padding: 0;">
            <li style="margin: 8px 0;"><strong>💳 Plan solicitado:</strong> ${planDisplayName}</li>
            <li style="margin: 8px 0;"><strong>💰 Monto:</strong> ${formattedAmount}€</li>
            <li style="margin: 8px 0;"><strong>🏦 Moneda:</strong> ${alertData.currency.toUpperCase()}</li>
            <li style="margin: 8px 0;"><strong>🔗 Sesión Stripe:</strong> <code style="background: #e5e7eb; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${alertData.stripeSessionId}</code></li>
            <li style="margin: 8px 0;"><strong>⏰ Timestamp:</strong> ${formattedTimestamp}</li>
          </ul>
        </div>

        <div style="background: #ecfdf5; border: 1px solid #a7f3d0; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="margin-top: 0; color: #065f46;">✅ Acción Tomada</h3>
          <p style="margin-bottom: 10px;">${alertData.actionTaken}</p>
          <p style="font-size: 14px; color: #047857;">El sistema ha activado automáticamente el protocolo de seguridad para proteger la cuenta del usuario.</p>
        </div>

        <div style="background: #fffbeb; border: 1px solid #fde68a; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="margin-top: 0; color: #92400e;">🔍 Acciones Recomendadas</h3>
          <ol style="margin: 0; padding-left: 20px;">
            <li style="margin: 8px 0;">Verificar si el pago es legítimo contactando al usuario</li>
            <li style="margin: 8px 0;">Revisar el historial de la cuenta para patrones sospechosos</li>
            <li style="margin: 8px 0;">Monitorear si el usuario confirma o rechaza la actualización</li>
            <li style="margin: 8px 0;">Considerar contactar al pagador si es necesario</li>
          </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="font-size: 14px; color: #6b7280; margin: 0;">
            Esta alerta se generó automáticamente por el sistema de seguridad de OposiAI
          </p>
          <p style="font-size: 12px; color: #9ca3af; margin: 5px 0 0 0;">
            ${formattedTimestamp}
          </p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
🚨 ALERTA DE SEGURIDAD - OposiAI

DISCREPANCIA DETECTADA EN PAGO

Se ha detectado un pago realizado por un email diferente al propietario de la cuenta:

👤 Email del pagador: ${alertData.payerEmail}
🏠 Propietario de la cuenta: ${alertData.accountOwnerEmail}

DETALLES DEL PAGO:
💳 Plan solicitado: ${planDisplayName}
💰 Monto: ${formattedAmount}€
🏦 Moneda: ${alertData.currency.toUpperCase()}
🔗 Sesión Stripe: ${alertData.stripeSessionId}
⏰ Timestamp: ${formattedTimestamp}

ACCIÓN TOMADA:
${alertData.actionTaken}

El sistema ha activado automáticamente el protocolo de seguridad para proteger la cuenta del usuario.

ACCIONES RECOMENDADAS:
1. Verificar si el pago es legítimo contactando al usuario
2. Revisar el historial de la cuenta para patrones sospechosos
3. Monitorear si el usuario confirma o rechaza la actualización
4. Considerar contactar al pagador si es necesario

Esta alerta se generó automáticamente por el sistema de seguridad de OposiAI
${formattedTimestamp}
    `;

    return {
      htmlContent,
      textContent,
      subject
    };
  }
}
