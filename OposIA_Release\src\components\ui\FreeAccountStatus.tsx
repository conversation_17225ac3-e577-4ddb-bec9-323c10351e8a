// src/components/ui/FreeAccountStatus.tsx
// Componente para mostrar el estado de cuentas gratuitas

'use client';

import React from 'react';
import Link from 'next/link';
import { FiClock, FiAlertTriangle, FiArrowUp, FiX, FiInfo } from 'react-icons/fi';
import { useFreeAccountAlerts, useTimeRemaining } from '@/hooks/useFreeAccount';

interface FreeAccountStatusProps {
  className?: string;
  showUpgradeButton?: boolean;
  compact?: boolean;
}

export default function FreeAccountStatus({ 
  className = '', 
  showUpgradeButton = true,
  compact = false 
}: FreeAccountStatusProps) {
  const {
    isFreeAccount,
    status,
    visibleAlerts,
    visibleWarnings,
    shouldShowUpgradePrompt,
    dismissAlert,
    dismissWarning
  } = useFreeAccountAlerts();

  const timeRemaining = useTimeRemaining(status?.expiresAt || null);

  // No mostrar nada si no es cuenta gratuita
  if (!isFreeAccount || !status) {
    return null;
  }

  // Versión compacta para header
  if (compact) {
    return (
      <div className={`flex items-center space-x-2 text-sm ${className}`}>
        <FiClock className="w-4 h-4 text-orange-500" />
        <span className={`font-medium ${timeRemaining.isExpired ? 'text-red-600' : 'text-orange-600'}`}>
          {timeRemaining.isExpired ? 'Expirado' : `${timeRemaining.formatted} restantes`}
        </span>
        {showUpgradeButton && (
          <Link
            href="/upgrade-plan"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Actualizar
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Estado principal */}
      <div className={`p-4 rounded-lg border ${
        timeRemaining.isExpired 
          ? 'bg-red-50 border-red-200' 
          : timeRemaining.days <= 1 
            ? 'bg-orange-50 border-orange-200'
            : 'bg-blue-50 border-blue-200'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FiClock className={`w-5 h-5 ${
              timeRemaining.isExpired 
                ? 'text-red-500' 
                : timeRemaining.days <= 1 
                  ? 'text-orange-500'
                  : 'text-blue-500'
            }`} />
            <div>
              <h3 className={`font-semibold ${
                timeRemaining.isExpired 
                  ? 'text-red-800' 
                  : timeRemaining.days <= 1 
                    ? 'text-orange-800'
                    : 'text-blue-800'
              }`}>
                {timeRemaining.isExpired 
                  ? 'Cuenta Gratuita Expirada' 
                  : 'Cuenta Gratuita Activa'
                }
              </h3>
              <p className={`text-sm ${
                timeRemaining.isExpired 
                  ? 'text-red-600' 
                  : timeRemaining.days <= 1 
                    ? 'text-orange-600'
                    : 'text-blue-600'
              }`}>
                {timeRemaining.isExpired 
                  ? 'Tu período gratuito ha terminado'
                  : `Tiempo restante: ${timeRemaining.formatted}`
                }
              </p>
            </div>
          </div>
          
          {showUpgradeButton && (
            <Link
              href="/upgrade-plan"
              className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                timeRemaining.isExpired
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              <FiArrowUp className="w-4 h-4 mr-2" />
              {timeRemaining.isExpired ? 'Renovar Ahora' : 'Actualizar Plan'}
            </Link>
          )}
        </div>

        {/* Barra de progreso */}
        {!timeRemaining.isExpired && (
          <div className="mt-3">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>Día 1</span>
              <span>Día 5</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  status.progressPercentage >= 80 
                    ? 'bg-red-500' 
                    : status.progressPercentage >= 60 
                      ? 'bg-orange-500'
                      : 'bg-blue-500'
                }`}
                style={{ width: `${Math.min(100, status.progressPercentage)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Alertas de sistema */}
      {visibleAlerts.map((alert, index) => (
        <div
          key={index}
          className={`p-3 rounded-lg border flex items-start justify-between ${
            alert.type === 'error' 
              ? 'bg-red-50 border-red-200 text-red-800'
              : alert.type === 'warning'
                ? 'bg-orange-50 border-orange-200 text-orange-800'
                : 'bg-blue-50 border-blue-200 text-blue-800'
          }`}
        >
          <div className="flex items-start space-x-2">
            {alert.type === 'error' ? (
              <FiAlertTriangle className="w-4 h-4 mt-0.5 text-red-500" />
            ) : alert.type === 'warning' ? (
              <FiAlertTriangle className="w-4 h-4 mt-0.5 text-orange-500" />
            ) : (
              <FiInfo className="w-4 h-4 mt-0.5 text-blue-500" />
            )}
            <p className="text-sm font-medium">{alert.message}</p>
          </div>
          <button
            onClick={() => dismissAlert(alert)}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="w-4 h-4" />
          </button>
        </div>
      ))}

      {/* Advertencias de uso */}
      {visibleWarnings.map((warning, index) => (
        <div
          key={index}
          className={`p-3 rounded-lg border flex items-start justify-between ${
            warning.severity === 'error'
              ? 'bg-red-50 border-red-200 text-red-800'
              : 'bg-yellow-50 border-yellow-200 text-yellow-800'
          }`}
        >
          <div className="flex items-start space-x-2">
            <FiAlertTriangle className={`w-4 h-4 mt-0.5 ${
              warning.severity === 'error' ? 'text-red-500' : 'text-yellow-500'
            }`} />
            <p className="text-sm font-medium">{warning.message}</p>
          </div>
          <button
            onClick={() => dismissWarning(warning)}
            className="text-gray-400 hover:text-gray-600"
          >
            <FiX className="w-4 h-4" />
          </button>
        </div>
      ))}

      {/* Límites de uso */}
      {status && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">Uso de Recursos</h4>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(status.usage).map(([feature, used]) => {
              const limit = status.limits[feature as keyof typeof status.limits];
              const percentage = status.usagePercentages[feature] || 0;
              
              return (
                <div key={feature} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="capitalize text-gray-600">
                      {feature === 'mindMaps' ? 'Mapas Mentales' : 
                       feature === 'documents' ? 'Documentos' :
                       feature === 'tests' ? 'Tests' :
                       feature === 'flashcards' ? 'Flashcards' :
                       'Tokens'}
                    </span>
                    <span className={`font-medium ${
                      percentage >= 100 ? 'text-red-600' :
                      percentage >= 80 ? 'text-orange-600' :
                      'text-gray-900'
                    }`}>
                      {used}/{limit}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className={`h-1.5 rounded-full transition-all duration-300 ${
                        percentage >= 100 ? 'bg-red-500' :
                        percentage >= 80 ? 'bg-orange-500' :
                        'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(100, percentage)}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Prompt de actualización */}
      {shouldShowUpgradePrompt && showUpgradeButton && (
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold mb-1">¿Necesitas más recursos?</h4>
              <p className="text-sm text-blue-100">
                Actualiza tu plan para obtener acceso ilimitado y más funcionalidades
              </p>
            </div>
            <Link
              href="/upgrade-plan"
              className="bg-white text-blue-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              Ver Planes
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
