@echo off
chcp 65001 >nul
title OposiAI - Iniciador de Aplicación

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                   OposiAI                                    ║
echo ║                        Sistema de Preparación de Oposiciones                ║
echo ║                              con Inteligencia Artificial                     ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Iniciando OposiAI...
echo.

REM Verificar si Node.js está instalado
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Node.js no está instalado en este sistema.
    echo.
    echo 📥 Por favor, instale Node.js desde: https://nodejs.org/
    echo    Versión recomendada: 18.x o superior
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js detectado correctamente
node --version

REM Verificar si npm está disponible
CALL npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: npm no está disponible.
    echo.
    pause
    exit /b 1
)

echo ✅ npm detectado correctamente
CALL npm --version
echo.

REM Verificar si existe package.json
if not exist "package.json" (
    echo ❌ Error: No se encontró package.json en el directorio actual.
    echo    Asegúrese de ejecutar este archivo desde la carpeta raíz del proyecto.
    echo.
    pause
    exit /b 1
)

echo ✅ Proyecto OposiAI encontrado
echo.

REM Verificar si node_modules existe
if not exist "node_modules" (
    echo 📦 Instalando dependencias por primera vez...
    echo    Esto puede tomar varios minutos...
    echo.
    CALL npm install
    if %errorlevel% neq 0 (
        echo ❌ Error instalando dependencias.
        echo.
        pause
        exit /b 1
    )
    echo ✅ Dependencias instaladas correctamente
    echo.
) else (
    echo ✅ Dependencias ya instaladas
    echo.
)

REM Verificar si existe .env.local
if not exist ".env.local" (
    echo ⚠️  ADVERTENCIA: No se encontró archivo .env.local
    echo    La aplicación necesita variables de entorno configuradas.
    echo.
    echo 📝 Creando archivo .env.local de ejemplo...
    echo.
    (
        echo # Configuración de OposiAI
        echo # Copie este archivo como .env.local y configure las variables reales
        echo.
        echo # Supabase Configuration
        echo NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
        echo NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
        echo SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
        echo.
        echo # OpenAI Configuration
        echo OPENAI_API_KEY=your_openai_api_key_here
        echo.
        echo # Stripe Configuration
        echo STRIPE_SECRET_KEY=your_stripe_secret_key_here
        echo NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
        echo STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here
        echo.
        echo # Application URLs
        echo NEXT_PUBLIC_APP_URL=http://localhost:3000
        echo.
        echo # Admin Configuration
        echo ADMIN_EMAILS=<EMAIL>
        echo.
        echo # Email Configuration ^(opcional^)
        echo RESEND_API_KEY=your_resend_api_key_here
    ) > .env.example
    
    echo ✅ Archivo .env.example creado
    echo    Configure las variables reales en .env.local antes de usar la aplicación
    echo.
)

echo 🔧 Verificando configuración del proyecto...
echo.

REM Verificar scripts en package.json
findstr /C:"\"dev\"" package.json >nul
if %errorlevel% neq 0 (
    echo ❌ Error: Script 'dev' no encontrado en package.json
    pause
    exit /b 1
)

echo ✅ Configuración del proyecto verificada
echo.

echo 🌐 Iniciando servidor de desarrollo...
echo.
echo    📍 La aplicación estará disponible en: http://localhost:3000
echo    🔄 El servidor se recargará automáticamente al hacer cambios
echo    🛑 Para detener el servidor, presione Ctrl+C
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║  IMPORTANTE: Mantenga esta ventana abierta mientras usa la aplicación        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Iniciar el servidor de desarrollo
CALL npm run dev

REM Si el servidor se detiene, mostrar mensaje
echo.
echo 🛑 Servidor detenido.
echo.
pause