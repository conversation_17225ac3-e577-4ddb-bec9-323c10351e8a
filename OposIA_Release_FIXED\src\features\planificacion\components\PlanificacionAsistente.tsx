import React, { useState, useEffect, useCallback } from 'react';
import { FiClock, FiCalendar, FiTarget, FiBookOpen, FiStar, FiArrowRight, FiArrowLeft, FiCheck } from 'react-icons/fi';
import { Te<PERSON><PERSON>, Te<PERSON> } from '@/lib/supabase/supabaseClient';
import { obtenerTemas } from '@/features/temario/services/temarioService';
import {
  guardarPlanificacionUsuario,
  obtenerPlanificacionUsuario
} from '../services/planificacionService';
import { toast } from 'react-hot-toast';

interface PlanificacionAsistenteProps {
  temario: Temario;
  onComplete: () => void;
  onCancel: () => void;
  isEditing?: boolean; // Nueva prop para indicar si estamos editando
}

interface FormData {
  // Paso 1: Tiempo diario
  tiempoDiarioPromedio: number;
  tiempoPorDia: Record<string, number>;
  
  // Paso 2: Fecha examen
  fechaExamen: string;
  fechaExamenAproximada: string;
  
  // Paso 3: Familiaridad
  familiaridadGeneral: number;
  
  // Paso 4: Estimaciones por tema
  estimacionesTemas: Record<string, {
    horas: number;
    esDificil: boolean;
    esMuyImportante: boolean;
    yaDominado: boolean;
    notas: string;
  }>;
  
  // Paso 5: Preferencias
  preferenciasHorario: string[];
  frecuenciaRepasos: string;
}

const DIAS_SEMANA = [
  { key: 'lunes', label: 'Lunes' },
  { key: 'martes', label: 'Martes' },
  { key: 'miercoles', label: 'Miércoles' },
  { key: 'jueves', label: 'Jueves' },
  { key: 'viernes', label: 'Viernes' },
  { key: 'sabado', label: 'Sábado' },
  { key: 'domingo', label: 'Domingo' }
];

const PlanificacionAsistente: React.FC<PlanificacionAsistenteProps> = ({
  temario,
  onComplete,
  onCancel,
  isEditing = false
}) => {
  const [pasoActual, setPasoActual] = useState(1);
  const [temas, setTemas] = useState<Tema[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [guardando, setGuardando] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    tiempoDiarioPromedio: 2,
    tiempoPorDia: {},
    fechaExamen: '',
    fechaExamenAproximada: '',
    familiaridadGeneral: 3,
    estimacionesTemas: {}, // Mantenemos por compatibilidad pero ya no se usa
    preferenciasHorario: [],
    frecuenciaRepasos: 'semanal'
  });

  const cargarDatos = useCallback(async () => {
    setIsLoading(true);
    try {
      const temasData = await obtenerTemas(temario.id);
      setTemas(temasData);

      if (isEditing) {
        // Cargar datos existentes de planificación
        const planificacionExistente = await obtenerPlanificacionUsuario(temario.id);

        if (planificacionExistente) {
          // Actualizar formulario con datos existentes (sin estimaciones manuales)
          setFormData({
            tiempoDiarioPromedio: planificacionExistente.tiempo_diario_promedio || 2,
            tiempoPorDia: planificacionExistente.tiempo_por_dia || {},
            fechaExamen: planificacionExistente.fecha_examen || '',
            fechaExamenAproximada: planificacionExistente.fecha_examen_aproximada || '',
            familiaridadGeneral: planificacionExistente.familiaridad_general || 3,
            estimacionesTemas: {}, // Ya no se usan estimaciones manuales
            preferenciasHorario: planificacionExistente.preferencias_horario || [],
            frecuenciaRepasos: planificacionExistente.frecuencia_repasos || 'semanal'
          });
        }
      }
      // En modo creación no necesitamos inicializar estimaciones ya que la IA las determinará automáticamente
    } catch (error) {
      console.error('Error al cargar datos:', error);
      toast.error(isEditing ? 'Error al cargar la planificación existente' : 'Error al cargar los temas del temario');
    } finally {
      setIsLoading(false);
    }
  }, [temario.id, isEditing]);

  useEffect(() => {
    cargarDatos();
  }, [cargarDatos]);

  const actualizarFormData = (campo: string, valor: any) => {
    setFormData(prev => ({
      ...prev,
      [campo]: valor
    }));
  };

  const actualizarTiempoPorDia = (dia: string, horas: number) => {
    setFormData(prev => ({
      ...prev,
      tiempoPorDia: {
        ...prev.tiempoPorDia,
        [dia]: horas
      }
    }));
  };

  const togglePreferenciaHorario = (horario: string) => {
    setFormData(prev => ({
      ...prev,
      preferenciasHorario: prev.preferenciasHorario.includes(horario)
        ? prev.preferenciasHorario.filter(h => h !== horario)
        : [...prev.preferenciasHorario, horario]
    }));
  };

  const siguientePaso = () => {
    if (pasoActual < 4) {
      setPasoActual(pasoActual + 1);
    }
  };

  const pasoAnterior = () => {
    if (pasoActual > 1) {
      setPasoActual(pasoActual - 1);
    }
  };

  const finalizarAsistente = async () => {
    setGuardando(true);
    try {
      // Guardar planificación principal
      const planificacionId = await guardarPlanificacionUsuario(temario.id, {
        tiempo_diario_promedio: formData.tiempoDiarioPromedio,
        tiempo_por_dia: formData.tiempoPorDia,
        fecha_examen: formData.fechaExamen || undefined,
        fecha_examen_aproximada: formData.fechaExamenAproximada || undefined,
        familiaridad_general: formData.familiaridadGeneral,
        preferencias_horario: formData.preferenciasHorario,
        frecuencia_repasos: formData.frecuenciaRepasos
      });

      if (!planificacionId) {
        throw new Error('Error al guardar la planificación');
      }

      // Ya no guardamos estimaciones manuales - la IA determinará automáticamente las características de cada tema

      toast.success(isEditing ? '¡Planificación actualizada exitosamente!' : '¡Planificación configurada exitosamente!');
      onComplete();
    } catch (error) {
      console.error('Error al finalizar asistente:', error);
      toast.error(isEditing ? 'Error al actualizar la planificación' : 'Error al guardar la planificación');
    } finally {
      setGuardando(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const renderPaso = () => {
    switch (pasoActual) {
      case 1:
        return renderPasoTiempo();
      case 2:
        return renderPasoFechaExamen();
      case 3:
        return renderPasoFamiliaridad();
      case 4:
        return renderPasoPreferencias();
      default:
        return null;
    }
  };

  const renderPasoTiempo = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FiClock className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Disponibilidad de Tiempo</h2>
        <p className="text-gray-600">
          Para empezar, ¿cuánto tiempo REAL estimas que puedes dedicar al estudio cada día?
        </p>
        <p className="text-sm text-blue-600 mt-2">
          Sé realista. Considera tu trabajo, familia y otros compromisos.
        </p>
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Tiempo promedio diario (horas)
        </label>
        <input
          type="number"
          min="0.5"
          max="12"
          step="0.5"
          value={formData.tiempoDiarioPromedio}
          onChange={(e) => actualizarFormData('tiempoDiarioPromedio', parseFloat(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Tiempo específico por día (opcional)
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {DIAS_SEMANA.map(dia => (
            <div key={dia.key} className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                {dia.label}
              </label>
              <input
                type="number"
                min="0"
                max="12"
                step="0.5"
                value={formData.tiempoPorDia[dia.key] || ''}
                onChange={(e) => actualizarTiempoPorDia(dia.key, parseFloat(e.target.value) || 0)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPasoFechaExamen = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FiCalendar className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Fecha del Examen</h2>
        <p className="text-gray-600">
          ¿Cuál es la fecha (aproximada o exacta) de tu próxima convocatoria o examen principal?
        </p>
        <p className="text-sm text-blue-600 mt-2">
          Esto nos ayudará a distribuir el temario en el tiempo disponible.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 rounded-lg p-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Fecha exacta del examen
          </label>
          <input
            type="date"
            value={formData.fechaExamen}
            onChange={(e) => actualizarFormData('fechaExamen', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="bg-gray-50 rounded-lg p-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            O fecha aproximada
          </label>
          <select
            value={formData.fechaExamenAproximada}
            onChange={(e) => actualizarFormData('fechaExamenAproximada', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Selecciona una opción</option>
            <option value="1-3_meses">En 1-3 meses</option>
            <option value="3-6_meses">En 3-6 meses</option>
            <option value="6-12_meses">En 6-12 meses</option>
            <option value="mas_12_meses">Más de 12 meses</option>
            <option value="primavera_2025">Primavera 2025</option>
            <option value="verano_2025">Verano 2025</option>
            <option value="otono_2025">Otoño 2025</option>
            <option value="invierno_2025">Invierno 2025</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderPasoFamiliaridad = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FiTarget className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Familiaridad con el Temario</h2>
        <p className="text-gray-600">
          En una escala del 1 al 5, ¿cómo describirías tu familiaridad general actual con el conjunto del temario?
        </p>
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <div className="grid grid-cols-5 gap-4">
          {[1, 2, 3, 4, 5].map(nivel => (
            <button
              key={nivel}
              onClick={() => actualizarFormData('familiaridadGeneral', nivel)}
              className={`p-4 rounded-lg border-2 transition-all ${
                formData.familiaridadGeneral === nivel
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl font-bold mb-2">{nivel}</div>
              <div className="text-xs">
                {nivel === 1 && 'Muy poco'}
                {nivel === 2 && 'Poco'}
                {nivel === 3 && 'Moderado'}
                {nivel === 4 && 'Bastante'}
                {nivel === 5 && 'Muy familiarizado'}
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPasoPreferencias = () => (
    <div className="space-y-6">
      <div className="text-center">
        <FiStar className="w-12 h-12 text-blue-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Preferencias de Estudio</h2>
        <p className="text-gray-600">
          Configura tus preferencias de horario y frecuencia de repasos.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Preferencias de Horario
          </h3>
          <div className="space-y-2">
            {['mañana', 'tarde', 'noche'].map(horario => (
              <label key={horario} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.preferenciasHorario.includes(horario)}
                  onChange={() => togglePreferenciaHorario(horario)}
                  className="mr-3"
                />
                <span className="capitalize">{horario}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Frecuencia de Repasos
          </h3>
          <select
            value={formData.frecuenciaRepasos}
            onChange={(e) => actualizarFormData('frecuenciaRepasos', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="semanal">Semanal</option>
            <option value="quincenal">Quincenal</option>
            <option value="mensual">Mensual</option>
          </select>
          <p className="text-sm text-gray-600 mt-2">
            Los repasos son fundamentales para consolidar el aprendizaje a largo plazo.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header con progreso */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">
            {isEditing ? 'Modificar Planificación IA' : 'Asistente de Planificación IA'}
          </h1>
          <span className="text-sm text-gray-500">
            Paso {pasoActual} de 4
          </span>
        </div>

        {/* Barra de progreso */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(pasoActual / 4) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Contenido del paso actual */}
      <div className="bg-white rounded-xl shadow-sm border p-8 mb-6">
        {renderPaso()}
      </div>

      {/* Botones de navegación */}
      <div className="flex justify-between">
        <button
          onClick={onCancel}
          className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          Cancelar
        </button>
        
        <div className="flex space-x-3">
          {pasoActual > 1 && (
            <button
              onClick={pasoAnterior}
              className="flex items-center px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              Anterior
            </button>
          )}
          
          {pasoActual < 4 ? (
            <button
              onClick={siguientePaso}
              className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Siguiente
              <FiArrowRight className="w-4 h-4 ml-2" />
            </button>
          ) : (
            <button
              onClick={finalizarAsistente}
              disabled={guardando}
              className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {guardando ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <FiCheck className="w-4 h-4 mr-2" />
              )}
              {isEditing ? 'Actualizar' : 'Finalizar'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanificacionAsistente;
