// src/lib/stripe/config.ts
import Stripe from 'stripe';

// Inicializar Stripe solo en el servidor
export const stripe = typeof window === 'undefined'
  ? new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-05-28.basil',
      typescript: true,
    })
  : null;

// Importar configuración de planes
export { PLANS, getPlanById, isValidPlan, APP_URLS } from './plans';
export type { PlanId } from './plans';
