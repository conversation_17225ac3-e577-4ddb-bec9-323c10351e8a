// src/app/api/user/validate-access/route.ts
// API para validación de acceso a características

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/supabaseClient';
import { PlanValidationService } from '@/lib/services/planValidation';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { feature, tokensToUse = 0, action, quantity = 1 } = body;
    
    if (!feature) {
      return NextResponse.json(
        { error: 'Característica requerida' },
        { status: 400 }
      );
    }
    
    // Validar acceso a la característica
    const validation = await PlanValidationService.validateFeatureAccess(
      user.id,
      feature,
      tokensToUse
    );
    
    if (!validation.allowed) {
      return NextResponse.json({
        allowed: false,
        reason: validation.reason,
        needsUpgrade: true
      }, { status: 403 });
    }
    
    // Si se especifica una acción, validar también esa acción
    if (action) {
      const actionValidation = await PlanValidationService.canUserPerformAction(
        user.id,
        action,
        quantity
      );
      
      if (!actionValidation.allowed) {
        return NextResponse.json({
          allowed: false,
          reason: actionValidation.reason,
          needsUpgrade: true
        }, { status: 403 });
      }
    }
    
    return NextResponse.json({
      allowed: true,
      remainingUsage: validation.remainingUsage,
      planLimits: validation.planLimits
    });
    
  } catch (error) {
    console.error('Error validating access:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { tokensUsed, activity } = body;
    
    if (!tokensUsed || !activity) {
      return NextResponse.json(
        { error: 'Tokens y actividad requeridos' },
        { status: 400 }
      );
    }
    
    // Actualizar uso de tokens
    const success = await PlanValidationService.updateTokenUsage(
      user.id,
      tokensUsed,
      activity
    );
    
    if (!success) {
      return NextResponse.json(
        { error: 'Error actualizando uso de tokens' },
        { status: 500 }
      );
    }
    
    // Obtener información actualizada
    const accessInfo = await PlanValidationService.getUserAccessInfo(user.id);
    
    return NextResponse.json({
      success: true,
      tokensUsed,
      currentUsage: accessInfo?.currentUsage || null
    });
    
  } catch (error) {
    console.error('Error updating token usage:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
