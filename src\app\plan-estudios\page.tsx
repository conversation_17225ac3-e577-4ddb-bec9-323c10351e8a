'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FiCalendar, FiTarget, FiRefreshCw, FiDownload, Fi<PERSON>rinter, FiFileText, FiLock, FiArrowUp } from 'react-icons/fi';
import { obtenerTemarioUsuario } from '@/features/temario/services/temarioService';
import { tienePlanificacionConfigurada } from '@/features/planificacion/services/planificacionService';
import { obtenerPlanEstudiosActivoCliente } from '@/features/planificacion/services/planEstudiosClientService';
import { Temario, Documento } from '@/lib/supabase/supabaseClient';
import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';
import PlanEstudiosViewer from '@/features/planificacion/components/PlanEstudiosViewer';
import DocumentSelector, { DocumentSelectorRef } from '@/features/documents/components/DocumentSelector';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { checkUserFeatureAccess } from '@/config/plans';
import UpgradePlanMessage from '@/components/ui/UpgradePlanMessage';
import Link from 'next/link';

const PlanEstudiosPage: React.FC = () => {
  const [temario, setTemario] = useState<Temario | null>(null);
  const [planGenerado, setPlanGenerado] = useState<PlanEstudiosEstructurado | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [tienePlanificacion, setTienePlanificacion] = useState(false);
  const [documentosSeleccionados, setDocumentosSeleccionados] = useState<Documento[]>([]);
  const documentSelectorRef = useRef<DocumentSelectorRef>(null);
  const { user } = useAuth();
  const [userPlan, setUserPlan] = useState<string | null>(null);

  useEffect(() => {
    cargarDatos();
  }, []);

  // Efecto para verificar el plan del usuario
  useEffect(() => {
    const checkUserPlan = async () => {
      if (user) {
        const hasAccess = await checkUserFeatureAccess('study_planning');
        setUserPlan(hasAccess ? 'paid' : 'free');
      }
    };
    checkUserPlan();
  }, [user]);

  const cargarDatos = async () => {
    setIsLoading(true);
    try {
      // Obtener temario del usuario
      const temarioData = await obtenerTemarioUsuario();
      if (!temarioData) {
        toast.error('No se encontró un temario configurado');
        setTemario(null);
        setTienePlanificacion(false);
        setPlanGenerado(null);
        return;
      }
      setTemario(temarioData);

      // Verificar si tiene planificación configurada
      const tienePlan = await tienePlanificacionConfigurada(temarioData.id);
      setTienePlanificacion(tienePlan);

      if (!tienePlan) {
        toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');
        setPlanGenerado(null);
        return;
      }

      // Verificar si ya existe un plan de estudios guardado
      const planExistente = await obtenerPlanEstudiosActivoCliente(temarioData.id);
      if (planExistente && planExistente.plan_data) {
        console.log('✅ Plan de estudios existente encontrado');
        setPlanGenerado(planExistente.plan_data as PlanEstudiosEstructurado);
        toast.success('Plan de estudios cargado desde la base de datos');
      } else {
        setPlanGenerado(null); // Limpiar plan si no existe
      }

    } catch (error) {
      console.error('Error al cargar datos:', error);
      toast.error('Error al cargar los datos');
      // Limpiar estado en caso de error
      setTemario(null);
      setTienePlanificacion(false);
      setPlanGenerado(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerarPlan = async () => {
    if (!temario) return;

    // Verificar nuevamente que tiene planificación antes de generar
    if (!tienePlanificacion) {
      toast.error('Necesitas configurar tu planificación antes de generar el plan de estudios');
      return;
    }

    setIsGenerating(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('La generación del plan de estudios puede tardar unos minutos. Si encuentra algún fallo una vez finalizado, vuelve a generar. OposIA puede cometer errores de configuración.', {
        duration: 0 // No auto-dismiss para que el usuario pueda leer el mensaje completo
      });

      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'generarPlanEstudios',
          peticion: temario.id, // Usar peticion para consistencia con otros endpoints
          contextos: [] // Contextos vacíos para mantener la interfaz estándar
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || `Error en la API: ${response.status}`;
        throw new Error(errorMessage);
      }

      const { result } = await response.json();
      setPlanGenerado(result);

      toast.success('¡Plan de estudios generado exitosamente!', { id: loadingToastId });
    } catch (error) {
      console.error('Error al generar plan:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      if (errorMessage.includes('planificación configurada')) {
        toast.error('Necesitas completar la configuración de planificación en "Mi Temario"', { id: loadingToastId });
      } else {
        toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.', { id: loadingToastId });
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const handleRegenerarPlan = () => {
    setPlanGenerado(null);
    handleGenerarPlan();
  };

  const handleDescargarPlan = () => {
    if (!planGenerado) return;

    // Convertir el plan estructurado a texto
    const planTexto = convertirPlanATexto(planGenerado);
    const blob = new Blob([planTexto], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `plan-estudios-${temario?.titulo || 'temario'}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Plan descargado exitosamente');
  };

  const convertirPlanATexto = (plan: PlanEstudiosEstructurado): string => {
    let texto = `# Plan de Estudios - ${temario?.titulo}\n\n`;
    texto += `${plan.introduccion}\n\n`;
    texto += `## Resumen del Plan\n\n`;
    texto += `- **Tiempo total de estudio:** ${plan.resumen.tiempoTotalEstudio}\n`;
    texto += `- **Número de temas:** ${plan.resumen.numeroTemas}\n`;
    texto += `- **Duración estudio nuevo:** ${plan.resumen.duracionEstudioNuevo}\n`;
    texto += `- **Duración repaso final:** ${plan.resumen.duracionRepasoFinal}\n\n`;

    texto += `## Cronograma Semanal\n\n`;
    plan.semanas.forEach(semana => {
      texto += `### Semana ${semana.numero} (${semana.fechaInicio} - ${semana.fechaFin})\n\n`;
      texto += `**Objetivo:** ${semana.objetivoPrincipal}\n\n`;
      semana.dias.forEach(dia => {
        texto += `**${dia.dia} (${dia.horas}h):**\n`;
        dia.tareas.forEach(tarea => {
          texto += `- ${tarea.titulo} (${tarea.duracionEstimada})\n`;
          if (tarea.descripcion) {
            texto += `  ${tarea.descripcion}\n`;
          }
        });
        texto += '\n';
      });
    });

    texto += `## Estrategia de Repasos\n\n${plan.estrategiaRepasos}\n\n`;
    texto += `## Próximos Pasos\n\n${plan.proximosPasos}\n`;

    return texto;
  };

  const handleImprimirPlan = () => {
    if (!planGenerado) return;

    const planTexto = convertirPlanATexto(planGenerado);
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Plan de Estudios - ${temario?.titulo}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
              h1, h2, h3 { color: #333; }
              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }
              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }
              ul, ol { margin-left: 20px; }
              strong { color: #2563eb; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            <div id="content"></div>
            <script>
              // Convertir markdown a HTML básico para impresión
              const markdown = ${JSON.stringify(planTexto)};
              const content = markdown
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                .replace(/(<li>.*<\\/li>)/s, '<ul>$1</ul>')
                .replace(/\\n/g, '<br>');
              document.getElementById('content').innerHTML = content;
              window.print();
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando datos...</p>
        </div>
      </div>
    );
  }

  // Si el usuario tiene plan gratuito, mostrar mensaje de bloqueo
  if (userPlan === 'free') {
    return (
      <UpgradePlanMessage
        feature="study_planning"
        benefits={[
          "Planes de estudio personalizados con IA",
          "Cronogramas adaptativos a tu ritmo",
          "Seguimiento automático de progreso",
          "Recomendaciones inteligentes de repaso"
        ]}
      />
    );
  }

  if (!temario || !tienePlanificacion) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiTarget className="w-8 h-8 text-yellow-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Configuración Requerida
          </h2>
          <p className="text-gray-600 mb-4">
            Para generar tu plan de estudios personalizado, necesitas:
          </p>
          <ul className="text-left text-sm text-gray-600 mb-6 space-y-2">
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Tener un temario configurado
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
              Completar la planificación inteligente
            </li>
          </ul>
          <a
            href="/temario"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiTarget className="w-4 h-4 mr-2" />
            Ir a Mi Temario
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Header reducido */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-1">
                Mi Plan de Estudios
              </h1>
              <p className="text-sm text-gray-600">
                Plan personalizado generado con IA para: <strong>{temario.titulo}</strong>
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {planGenerado && (
                <>
                  <button
                    onClick={handleRegenerarPlan}
                    disabled={isGenerating}
                    className="flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 text-sm"
                  >
                    <FiRefreshCw className="w-4 h-4 mr-2" />
                    Regenerar
                  </button>
                  <button
                    onClick={handleDescargarPlan}
                    className="flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    <FiDownload className="w-4 h-4 mr-2" />
                    Descargar
                  </button>
                  <button
                    onClick={handleImprimirPlan}
                    className="flex items-center px-3 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    <FiPrinter className="w-4 h-4 mr-2" />
                    Imprimir
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Sección de Documentos Seleccionados */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center mb-3">
            <FiFileText className="w-5 h-5 text-blue-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">Documentos Seleccionados</h2>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Selecciona los documentos que quieres consultar durante el estudio de tu plan personalizado.
          </p>
          <DocumentSelector
            ref={documentSelectorRef}
            onSelectionChange={setDocumentosSeleccionados}
          />
          {documentosSeleccionados.length > 0 && (
            <div className="mt-3 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>{documentosSeleccionados.length}</strong> documento{documentosSeleccionados.length !== 1 ? 's' : ''} seleccionado{documentosSeleccionados.length !== 1 ? 's' : ''} para consulta.
              </p>
            </div>
          )}
        </div>

        {/* Contenido principal */}
        {!planGenerado ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiCalendar className="w-10 h-10 text-blue-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Genera tu Plan de Estudios Personalizado
            </h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Nuestro asistente de IA analizará tu planificación, disponibilidad de tiempo, 
              y las características de cada tema para crear un plan de estudios completamente 
              personalizado y realista.
            </p>
            <button
              onClick={handleGenerarPlan}
              disabled={isGenerating}
              className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 mx-auto"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Generando plan con IA...
                </>
              ) : (
                <>
                  <FiTarget className="w-5 h-5 mr-3" />
                  Generar Plan de Estudios
                </>
              )}
            </button>
          </div>
        ) : (
          <PlanEstudiosViewer plan={planGenerado} temarioId={temario.id} />
        )}
      </div>
    </div>
  );
};

export default PlanEstudiosPage;
