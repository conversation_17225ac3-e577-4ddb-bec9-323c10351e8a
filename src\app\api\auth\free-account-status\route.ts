// src/app/api/auth/free-account-status/route.ts
// Endpoint para verificar estado de cuentas gratuitas

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { FreeAccountService } from '@/lib/services/freeAccountService';

// Interfaces para tipado
interface UsageWarning {
  feature: string;
  message: string;
  severity: 'error' | 'warning';
}

interface Alert {
  type: 'error' | 'warning' | 'info';
  message: string;
  action: 'upgrade' | 'reminder';
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Verificando estado de cuenta gratuita');
    
    // 1. Crear cliente de Supabase para verificar autenticación
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No necesitamos setear cookies en este endpoint
          },
        },
      }
    );
    
    // 2. Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        error: 'Usuario no autenticado'
      }, { status: 401 });
    }
    
    console.log('👤 Usuario autenticado:', user.id);
    
    // 3. Obtener estado de la cuenta gratuita
    const status = await FreeAccountService.getFreeAccountStatus(user.id);

    if (!status) {
      // Usuario no tiene una cuenta gratuita activa o no se encontró perfil de cuenta gratuita
      console.log('📊 Usuario no tiene cuenta gratuita - devolviendo isFreeAccount: false');
      return NextResponse.json({
        success: true, // La petición se procesó correctamente, el resultado es que no es free
        isFreeAccount: false,
        status: null,
        alerts: [],
        usageWarnings: [],
        recommendations: [],
        upgradeUrl: '/upgrade-plan'
      });
    }
    
    console.log('📊 Estado de cuenta gratuita obtenido:', {
      isActive: status.isActive,
      daysRemaining: status.daysRemaining,
      expiresAt: status.expiresAt
    });

    // 4. Calcular información adicional
    const now = new Date();

    // Verificar que expiresAt no sea null
    if (!status.expiresAt) {
      console.error("❌ status.expiresAt es null, no se puede calcular la información adicional.");
      return NextResponse.json({
        error: 'Error interno: Faltan datos de expiración de la cuenta gratuita.'
      }, { status: 500 });
    }

    const expiresAtDate = new Date(status.expiresAt);
    const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms
    const timeElapsed = now.getTime() - (expiresAtDate.getTime() - totalDuration);
    const progressPercentage = Math.min(100, Math.max(0, (timeElapsed / totalDuration) * 100));
    
    // 5. Determinar alertas y recomendaciones
    const alerts: Alert[] = [];
    const recommendations: string[] = [];
    
    if (!status.isActive) {
      alerts.push({
        type: 'error',
        message: 'Tu cuenta gratuita ha expirado',
        action: 'upgrade'
      });
      recommendations.push('Actualiza a un plan premium para continuar usando OposiAI');
    } else if (status.daysRemaining <= 1) {
      alerts.push({
        type: 'warning',
        message: `Tu cuenta expira en ${status.hoursRemaining} horas`,
        action: 'upgrade'
      });
      recommendations.push('Considera actualizar tu plan antes de que expire');
    } else if (status.daysRemaining <= 2) {
      alerts.push({
        type: 'info',
        message: `Tu cuenta expira en ${status.daysRemaining} días`,
        action: 'reminder'
      });
    }
    
    // Verificar límites de uso
    const usageWarnings: UsageWarning[] = [];

    // Asegurar que usageCount y limits existen
    if (status.usageCount && status.limits) {
      Object.entries(status.usageCount).forEach(([feature, used]) => {
        // Asegurar que 'used' es un número
        const usedAmount = typeof used === 'number' ? used : 0;

        // Asegurar que limit no es undefined
        const limit = status.limits[feature as keyof typeof status.limits];
        const limitAmount = typeof limit === 'number' && limit > 0 ? limit : 1; // Evitar división por cero

        const percentage = (usedAmount / limitAmount) * 100;

        if (percentage >= 100) {
          usageWarnings.push({
            feature,
            message: `Has alcanzado el límite de ${feature} (${usedAmount}/${limitAmount})`,
            severity: 'error'
          });
        } else if (percentage >= 80) {
          usageWarnings.push({
            feature,
            message: `Cerca del límite de ${feature} (${usedAmount}/${limitAmount})`,
            severity: 'warning'
          });
        }
      });
    } else {
      console.warn("⚠️ No se pudo generar usageWarnings porque status.usageCount o status.limits no están definidos.");
    }
    
    if (usageWarnings.length > 0) {
      recommendations.push('Considera actualizar tu plan para obtener más recursos');
    }
    
    return NextResponse.json({
      success: true,
      isFreeAccount: true,
      status: {
        isActive: status.isActive,
        expiresAt: status.expiresAt,
        daysRemaining: status.daysRemaining,
        hoursRemaining: status.hoursRemaining,
        progressPercentage: Math.round(progressPercentage),
        usageCount: status.usageCount || {},
        limits: status.limits || {},
        usagePercentages: (status.usageCount && status.limits) ? Object.entries(status.usageCount).reduce((acc, [key, value]) => {
          const limit = status.limits[key as keyof typeof status.limits];
          // Asegurar que limit no es undefined y no es 0
          const limitAmount = typeof limit === 'number' && limit > 0 ? limit : 1; // Evitar división por cero
          const usedAmount = typeof value === 'number' ? value : 0;
          acc[key] = Math.round((usedAmount / limitAmount) * 100);
          return acc;
        }, {} as Record<string, number>) : {} // Enviar objeto vacío si no hay datos
      },
      alerts,
      usageWarnings,
      recommendations,
      upgradeUrl: '/upgrade-plan'
    });
    
  } catch (error) {
    console.error('❌ Error verificando estado de cuenta gratuita:', error);
    
    return NextResponse.json({
      error: 'Error interno del servidor',
      details: process.env.NODE_ENV === 'development' 
        ? (error instanceof Error ? error.message : 'Error desconocido')
        : undefined
    }, { status: 500 });
  }
}

// NOTA: El endpoint POST ha sido eliminado porque el incremento de uso
// ahora se hace automáticamente en el backend (api/ai/route.ts y api/document/upload)
// después de operaciones exitosas. Ya no es necesario que el frontend
// haga llamadas POST para incrementar contadores.
//
// El endpoint GET sigue siendo necesario para obtener el estado de la cuenta gratuita.
