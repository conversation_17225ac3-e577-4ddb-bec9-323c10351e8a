import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// Cliente para el servidor (componentes del servidor, API routes)
export async function createServerSupabaseClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: false, // No persistir sesiones en el servidor
        autoRefreshToken: false, // No refrescar tokens automáticamente
        detectSessionInUrl: false // No detectar sesión en URL
      },
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            // Filtrar cookies de autenticación para no persistirlas
            const filteredCookies = cookiesToSet.filter(cookie =>
              !cookie.name.includes('auth-token') &&
              !cookie.name.includes('refresh-token')
            );

            filteredCookies.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, {
                ...options,
                maxAge: undefined, // No establecer maxAge para evitar persistencia
                expires: undefined // No establecer expires para evitar persistencia
              })
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}
