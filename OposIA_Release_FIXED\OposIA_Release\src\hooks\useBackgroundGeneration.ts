'use client';

import { useCallback } from 'react';
import { useBackgroundTasks, BackgroundTask } from '@/contexts/BackgroundTasksContext';

interface GenerationOptions {
  peticion: string;
  contextos: string[];
  cantidad?: number;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface PlanEstudiosOptions {
  temarioId: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface ResumenOptions {
  documento: {
    id: string;
    titulo: string;
    contenido: string;
    categoria?: string;
    numero_tema?: number;
  };
  instrucciones: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface UseBackgroundGenerationReturn {
  generateMapaMental: (options: GenerationOptions) => Promise<string>;
  generateTest: (options: GenerationOptions) => Promise<string>;
  generateFlashcards: (options: GenerationOptions) => Promise<string>;
  generatePlanEstudios: (options: PlanEstudiosOptions) => Promise<string>;
  generateResumen: (options: ResumenOptions) => Promise<string>;
  isGenerating: (type: BackgroundTask['type']) => boolean;
  getActiveTask: (type: BackgroundTask['type']) => BackgroundTask | undefined;
}

export const useBackgroundGeneration = (): UseBackgroundGenerationReturn => {
  const { addTask, updateTask, getTasksByType } = useBackgroundTasks();

  const executeGeneration = useCallback(async (
    type: BackgroundTask['type'],
    action: string,
    options: GenerationOptions
  ): Promise<string> => {
    const { peticion, contextos, cantidad, onComplete, onError } = options;

    // Crear la tarea en segundo plano
    const taskId = addTask({
      type,
      title: peticion.length > 50 ? `${peticion.substring(0, 50)}...` : peticion,
    });

    try {
      // Marcar como procesando
      updateTask(taskId, { status: 'processing' });

      console.log(`🚀 [FRONTEND] Iniciando generación de ${type}:`, {
        action,
        peticion: peticion.substring(0, 100) + '...',
        contextosLength: contextos?.length,
        cantidad,
        timestamp: new Date().toISOString()
      });

      const requestBody = {
        action,
        peticion,
        contextos,
        cantidad
      };

      console.log(`📤 [FRONTEND] Enviando request a /api/ai:`, {
        bodySize: JSON.stringify(requestBody).length,
        timestamp: new Date().toISOString()
      });

      const startTime = Date.now();

      // Realizar la petición
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const responseTime = Date.now() - startTime;
      console.log(`📡 [FRONTEND] Respuesta recibida para ${type}:`, {
        status: response.status,
        ok: response.ok,
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString()
      });

      if (!response.ok) {
        console.error(`❌ [FRONTEND] Error HTTP para ${type}:`, {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          timestamp: new Date().toISOString()
        });

        const errorText = await response.text();
        console.error(`❌ [FRONTEND] Error body para ${type}:`, errorText);
        throw new Error(`Error en la API: ${response.status} - ${errorText}`);
      }

      console.log(`📥 [FRONTEND] Procesando respuesta JSON para ${type}...`, {
        timestamp: new Date().toISOString()
      });

      const responseData = await response.json();
      console.log(`✅ [FRONTEND] Resultado obtenido para ${type}:`, {
        hasResult: !!responseData.result,
        resultLength: responseData.result?.length || 0,
        timestamp: new Date().toISOString()
      });
      const { result } = responseData;

      // Marcar como completado
      updateTask(taskId, {
        status: 'completed',
        result,
        progress: 100
      });

      // Ejecutar callback de éxito de forma asíncrona para evitar problemas de renderizado
      if (onComplete) {
        setTimeout(() => onComplete(result), 0);
      }

      return taskId;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      console.error(`💥 [FRONTEND] Error en executeGeneration para ${type}:`, {
        errorMessage,
        errorType: error?.constructor?.name,
        errorStack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });

      // Marcar como error
      updateTask(taskId, {
        status: 'error',
        error: errorMessage
      });

      // Ejecutar callback de error de forma asíncrona para evitar problemas de renderizado
      if (onError) {
        setTimeout(() => onError(errorMessage), 0);
      }

      throw error;
    }
  }, [addTask, updateTask]);

  const generateMapaMental = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('mapa-mental', 'generarMapaMental', options);
  }, [executeGeneration]);

  const generateTest = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('test', 'generarTest', options);
  }, [executeGeneration]);

  const generateFlashcards = useCallback(async (options: GenerationOptions): Promise<string> => {
    return executeGeneration('flashcards', 'generarFlashcards', options);
  }, [executeGeneration]);

  const generateResumen = useCallback(async (options: ResumenOptions): Promise<string> => {
    const { documento, instrucciones, onComplete, onError } = options;

    // Preparar datos específicos para resúmenes
    const requestData = {
      action: 'generarResumen',
      peticion: `${documento.titulo}|${documento.categoria || ''}|${documento.numero_tema || ''}|${instrucciones}`,
      contextos: [documento.contenido]
    };

    return executeGeneration('resumen', 'generarResumen', {
      peticion: requestData.peticion,
      contextos: requestData.contextos,
      onComplete,
      onError
    });
  }, [executeGeneration]);

  const generatePlanEstudios = useCallback(async (options: PlanEstudiosOptions): Promise<string> => {
    const { temarioId, onComplete, onError } = options;

    // Crear la tarea en segundo plano
    const taskId = addTask({
      type: 'plan-estudios',
      title: 'Generando plan de estudios personalizado',
    });

    try {
      // Marcar como procesando
      updateTask(taskId, { status: 'processing' });

      // Realizar la petición usando la interfaz estándar
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generarPlanEstudios',
          peticion: temarioId, // Usar peticion en lugar de temarioId para consistencia
          contextos: [] // Contextos vacíos para mantener la interfaz estándar
        })
      });

      if (!response.ok) {
        throw new Error(`Error en la API: ${response.status}`);
      }

      const { result } = await response.json();

      // Marcar como completado
      updateTask(taskId, {
        status: 'completed',
        result,
        progress: 100
      });

      // Ejecutar callback de éxito de forma asíncrona para evitar problemas de renderizado
      if (onComplete) {
        setTimeout(() => onComplete(result), 0);
      }

      return taskId;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      // Marcar como error
      updateTask(taskId, {
        status: 'error',
        error: errorMessage
      });

      // Ejecutar callback de error de forma asíncrona para evitar problemas de renderizado
      if (onError) {
        setTimeout(() => onError(errorMessage), 0);
      }

      throw error;
    }
  }, [addTask, updateTask]);

  const isGenerating = useCallback((type: BackgroundTask['type']): boolean => {
    const tasks = getTasksByType(type);
    return tasks.some(task => task.status === 'pending' || task.status === 'processing');
  }, [getTasksByType]);

  const getActiveTask = useCallback((type: BackgroundTask['type']): BackgroundTask | undefined => {
    const tasks = getTasksByType(type);
    return tasks.find(task => task.status === 'pending' || task.status === 'processing');
  }, [getTasksByType]);

  return {
    generateMapaMental,
    generateTest,
    generateFlashcards,
    generatePlanEstudios,
    generateResumen,
    isGenerating,
    getActiveTask,
  };
};
