# Guía de Contribución - OposiAI

¡Gracias por tu interés en contribuir a OposiAI! Esta guía te ayudará a entender cómo participar en el desarrollo del proyecto.

## 📋 Tabla de Contenidos

- [Código de Conducta](#código-de-conducta)
- [Cómo Contribuir](#cómo-contribuir)
- [Configuración del Entorno](#configuración-del-entorno)
- [Convenciones de Código](#convenciones-de-código)
- [Proceso de Pull Request](#proceso-de-pull-request)
- [Testing](#testing)
- [Documentación](#documentación)

## 🤝 Código de Conducta

Este proyecto se adhiere a un código de conducta profesional. Al participar, te comprometes a mantener un ambiente respetuoso y colaborativo.

### Comportamientos Esperados
- Usar lenguaje inclusivo y respetuoso
- Ser receptivo a críticas constructivas
- Enfocarse en lo que es mejor para la comunidad
- Mostrar empatía hacia otros miembros

### Comportamientos Inaceptables
- Uso de lenguaje o imágenes sexualizadas
- Comentarios despectivos o ataques personales
- Acoso público o privado
- Publicar información privada sin permiso

## 🚀 Cómo Contribuir

### Tipos de Contribuciones

1. **Reportar Bugs**
   - Usar el template de issue para bugs
   - Incluir pasos para reproducir
   - Especificar entorno (OS, navegador, versión)

2. **Solicitar Funcionalidades**
   - Usar el template de feature request
   - Explicar el caso de uso
   - Proporcionar mockups si es posible

3. **Contribuir Código**
   - Corregir bugs existentes
   - Implementar nuevas funcionalidades
   - Mejorar performance
   - Refactorizar código

4. **Mejorar Documentación**
   - Corregir errores tipográficos
   - Añadir ejemplos
   - Traducir contenido
   - Mejorar claridad

## ⚙️ Configuración del Entorno

### Prerrequisitos
- Node.js 18+
- npm o yarn
- Git
- Editor con soporte TypeScript (recomendado: VS Code)

### Setup Inicial

1. **Fork del repositorio**
   ```bash
   # Hacer fork en GitHub, luego clonar
   git clone https://github.com/tu-usuario/OposicionesIA.git
   cd OposicionesIA/v16
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar variables de entorno**
   ```bash
   cp .env.example .env.local
   # Completar las variables necesarias
   ```

4. **Ejecutar en desarrollo**
   ```bash
   npm run dev
   ```

5. **Ejecutar tests**
   ```bash
   npm test
   ```

### Configuración de VS Code

Extensiones recomendadas:
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- ESLint
- Prettier
- Jest Runner

Configuración en `.vscode/settings.json`:
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

## 📝 Convenciones de Código

### Estructura de Archivos

```
src/
├── features/[feature-name]/
│   ├── components/
│   │   ├── FeatureComponent.tsx
│   │   └── index.ts
│   ├── hooks/
│   │   ├── useFeature.ts
│   │   └── index.ts
│   ├── services/
│   │   ├── featureService.ts
│   │   └── index.ts
│   ├── types/
│   │   ├── feature.types.ts
│   │   └── index.ts
│   └── __tests__/
│       ├── components/
│       ├── hooks/
│       └── services/
```

### Nomenclatura

#### Archivos y Directorios
- **Componentes**: PascalCase (`UserProfile.tsx`)
- **Hooks**: camelCase con prefijo `use` (`useAuth.ts`)
- **Servicios**: camelCase con sufijo `Service` (`authService.ts`)
- **Tipos**: camelCase con sufijo `.types` (`user.types.ts`)
- **Tests**: mismo nombre + `.test` (`UserProfile.test.tsx`)

#### Variables y Funciones
```typescript
// Variables: camelCase
const userName = 'john_doe';
const isAuthenticated = true;

// Funciones: camelCase
function getUserProfile() {}
const handleSubmit = () => {};

// Constantes: UPPER_SNAKE_CASE
const API_ENDPOINTS = {
  USERS: '/api/users',
  DOCUMENTS: '/api/documents'
};

// Tipos e Interfaces: PascalCase
interface UserProfile {
  id: string;
  email: string;
}

type AuthState = 'loading' | 'authenticated' | 'unauthenticated';
```

### Estructura de Componentes

```typescript
// Imports
import React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import type { UserProfile } from '@/types/user.types';

// Interfaces
interface ComponentProps {
  user: UserProfile;
  onUpdate?: (user: UserProfile) => void;
}

// Componente principal
export const UserProfileCard: React.FC<ComponentProps> = ({ 
  user, 
  onUpdate 
}) => {
  // Hooks
  const { updateUser } = useAuth();
  
  // Estado local
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(user);
  
  // Efectos
  useEffect(() => {
    setFormData(user);
  }, [user]);
  
  // Handlers
  const handleSave = async () => {
    try {
      await updateUser(formData);
      onUpdate?.(formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating user:', error);
    }
  };
  
  // Render
  return (
    <div className="p-4 border rounded-lg">
      {/* JSX content */}
    </div>
  );
};

// Export por defecto si es necesario
export default UserProfileCard;
```

### Hooks Personalizados

```typescript
// useFeature.ts
import { useState, useEffect } from 'react';
import { featureService } from '../services/featureService';
import type { FeatureData } from '../types/feature.types';

interface UseFeatureReturn {
  data: FeatureData | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export const useFeature = (id: string): UseFeatureReturn => {
  const [data, setData] = useState<FeatureData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await featureService.getById(id);
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchData();
  }, [id]);
  
  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
};
```

### Servicios

```typescript
// featureService.ts
import { createClient } from '@/lib/supabase/supabaseClient';
import type { FeatureData, CreateFeatureDto } from '../types/feature.types';

class FeatureService {
  private supabase = createClient();
  
  async getById(id: string): Promise<FeatureData> {
    const { data, error } = await this.supabase
      .from('features')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) throw error;
    return data;
  }
  
  async create(dto: CreateFeatureDto): Promise<FeatureData> {
    const { data, error } = await this.supabase
      .from('features')
      .insert(dto)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  }
  
  async update(id: string, dto: Partial<CreateFeatureDto>): Promise<FeatureData> {
    const { data, error } = await this.supabase
      .from('features')
      .update(dto)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  }
  
  async delete(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('features')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
  }
}

export const featureService = new FeatureService();
```

## 🔄 Proceso de Pull Request

### Antes de Crear un PR

1. **Crear una rama**
   ```bash
   git checkout -b feature/nueva-funcionalidad
   # o
   git checkout -b fix/corregir-bug
   ```

2. **Hacer commits descriptivos**
   ```bash
   git commit -m "feat: añadir validación de formulario de registro"
   git commit -m "fix: corregir error de autenticación en Safari"
   git commit -m "docs: actualizar guía de instalación"
   ```

3. **Ejecutar tests**
   ```bash
   npm test
   npm run type-check
   npm run lint
   ```

4. **Actualizar documentación** si es necesario

### Formato de Commits

Usar [Conventional Commits](https://www.conventionalcommits.org/):

```
<tipo>[ámbito opcional]: <descripción>

[cuerpo opcional]

[pie opcional]
```

**Tipos:**
- `feat`: Nueva funcionalidad
- `fix`: Corrección de bug
- `docs`: Cambios en documentación
- `style`: Cambios de formato (no afectan lógica)
- `refactor`: Refactorización de código
- `test`: Añadir o modificar tests
- `chore`: Tareas de mantenimiento

**Ejemplos:**
```
feat(auth): añadir autenticación con Google
fix(payments): corregir cálculo de impuestos
docs(readme): actualizar instrucciones de instalación
refactor(components): simplificar lógica de UserCard
test(auth): añadir tests para hook useAuth
```

### Template de Pull Request

```markdown
## Descripción
Breve descripción de los cambios realizados.

## Tipo de Cambio
- [ ] Bug fix (cambio que corrige un issue)
- [ ] Nueva funcionalidad (cambio que añade funcionalidad)
- [ ] Breaking change (cambio que rompe compatibilidad)
- [ ] Documentación

## Testing
- [ ] Tests unitarios pasan
- [ ] Tests de integración pasan
- [ ] Probado manualmente

## Checklist
- [ ] Código sigue las convenciones del proyecto
- [ ] Self-review realizado
- [ ] Documentación actualizada
- [ ] Tests añadidos/actualizados
```

## 🧪 Testing

### Estructura de Tests

```typescript
// UserProfile.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserProfile } from './UserProfile';
import { mockUser } from '@/__tests__/setup/testUtils';

describe('UserProfile', () => {
  const defaultProps = {
    user: mockUser,
    onUpdate: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render user information', () => {
    render(<UserProfile {...defaultProps} />);
    
    expect(screen.getByText(mockUser.name)).toBeInTheDocument();
    expect(screen.getByText(mockUser.email)).toBeInTheDocument();
  });

  it('should handle edit mode', async () => {
    const user = userEvent.setup();
    render(<UserProfile {...defaultProps} />);
    
    await user.click(screen.getByRole('button', { name: /edit/i }));
    
    expect(screen.getByRole('textbox', { name: /name/i })).toBeInTheDocument();
  });

  it('should call onUpdate when saving changes', async () => {
    const user = userEvent.setup();
    const onUpdate = jest.fn();
    
    render(<UserProfile {...defaultProps} onUpdate={onUpdate} />);
    
    await user.click(screen.getByRole('button', { name: /edit/i }));
    await user.clear(screen.getByRole('textbox', { name: /name/i }));
    await user.type(screen.getByRole('textbox', { name: /name/i }), 'New Name');
    await user.click(screen.getByRole('button', { name: /save/i }));
    
    await waitFor(() => {
      expect(onUpdate).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'New Name' })
      );
    });
  });
});
```

### Comandos de Testing

```bash
# Ejecutar todos los tests
npm test

# Tests en modo watch
npm run test:watch

# Tests con coverage
npm run test:coverage

# Tests específicos
npm test UserProfile

# Tests de integración
npm run test:integration
```

## 📚 Documentación

### JSDoc para Funciones

```typescript
/**
 * Valida el acceso de un usuario a una funcionalidad específica
 * @param userId - ID del usuario
 * @param feature - Nombre de la funcionalidad
 * @param tokenCost - Costo en tokens de la operación
 * @returns Promise que resuelve con el resultado de la validación
 * @throws {Error} Si el usuario no tiene permisos o excede límites
 * @example
 * ```typescript
 * const result = await validateFeatureAccess('user-123', 'ai_chat', 1000);
 * if (result.allowed) {
 *   // Proceder con la operación
 * }
 * ```
 */
async function validateFeatureAccess(
  userId: string, 
  feature: string, 
  tokenCost: number
): Promise<ValidationResult> {
  // Implementación
}
```

### README para Features

Cada feature debe incluir un README.md:

```markdown
# Feature: Authentication

## Descripción
Sistema de autenticación basado en Supabase Auth.

## Componentes
- `LoginForm`: Formulario de inicio de sesión
- `RegisterForm`: Formulario de registro
- `PasswordResetForm`: Formulario de recuperación de contraseña

## Hooks
- `useAuth`: Hook principal de autenticación
- `useAuthRedirect`: Redirección basada en estado de auth

## Servicios
- `authService`: Operaciones de autenticación

## Uso
```typescript
import { useAuth } from '@/features/auth';

const { user, loading, signIn, signOut } = useAuth();
```
```

---

¡Gracias por contribuir a OposiAI! 🚀

Para preguntas específicas, abre un issue o contacta al equipo de desarrollo.
