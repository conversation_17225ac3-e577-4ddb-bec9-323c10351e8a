// src/app/api/admin/reactivate-user/route.ts
// API administrativa para reactivar usuarios cuando el webhook falla

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { stripe } from '@/lib/stripe/config';

// Lista de emails de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Agregar más emails de administradores aquí
];

export async function POST(request: NextRequest) {
  try {
    // --- INICIO: CONTROL DE ACCESO DE ADMINISTRADOR ---

    // 1. Verificar si el usuario está autenticado y es un administrador
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No necesitamos setear cookies en este endpoint
          },
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.warn('Acceso denegado a reactivate-user: Usuario no autenticado.');
      return NextResponse.json({
        error: 'No autorizado. Debes iniciar sesión como administrador.'
      }, { status: 401 });
    }

    if (!user.email || !ADMIN_EMAILS.includes(user.email)) {
      console.error(`Acceso denegado a reactivate-user para usuario: ${user.email}`);
      return NextResponse.json({
        error: 'Acceso denegado. Esta acción es solo para administradores.'
      }, { status: 403 });
    }

    console.log(`👤 Administrador autorizado (${user.email}) iniciando reactivación.`);

    // --- FIN: CONTROL DE ACCESO DE ADMINISTRADOR ---

    const body = await request.json();
    const { sessionId, email, userId } = body;

    // Validar que se proporcione al menos uno de los identificadores
    if (!sessionId && !email && !userId) {
      return NextResponse.json({
        error: 'Se requiere sessionId, email o userId'
      }, { status: 400 });
    }

    console.log('🔄 Iniciando reactivación de usuario:', { sessionId, email, userId });

    let targetUserId = userId;
    let stripeSession = null;

    // 1. Si tenemos sessionId, obtener información de Stripe
    if (sessionId) {
      try {
        if (stripe) {
          stripeSession = await stripe.checkout.sessions.retrieve(sessionId);
          console.log('📊 Sesión de Stripe obtenida:', {
            id: stripeSession.id,
            paymentStatus: stripeSession.payment_status,
            customerEmail: stripeSession.customer_details?.email
          });
        }
      } catch (stripeError) {
        console.error('Error obteniendo sesión de Stripe:', stripeError);
        return NextResponse.json({
          error: 'Error verificando sesión en Stripe'
        }, { status: 500 });
      }

      // Verificar que el pago esté completado
      if (stripeSession?.payment_status !== 'paid') {
        return NextResponse.json({
          error: 'El pago no está completado en Stripe'
        }, { status: 400 });
      }
    }

    // 2. Encontrar el usuario
    if (!targetUserId) {
      const userEmail = email || stripeSession?.customer_details?.email || stripeSession?.metadata?.customerEmail;
      
      if (!userEmail) {
        return NextResponse.json({
          error: 'No se pudo determinar el email del usuario'
        }, { status: 400 });
      }

      const existingUser = await SupabaseAdminService.getUserByEmail(userEmail);
      if (!existingUser) {
        return NextResponse.json({
          error: 'Usuario no encontrado'
        }, { status: 404 });
      }

      targetUserId = existingUser.id;
    }

    console.log('👤 Usuario objetivo identificado:', targetUserId);

    // 3. Verificar si ya existe una transacción para esta sesión
    let transaction = null;
    if (sessionId) {
      transaction = await SupabaseAdminService.getTransactionBySessionId(sessionId);
    }

    // 4. Crear transacción si no existe
    if (sessionId && !transaction && stripeSession) {
      const planId = stripeSession.metadata?.planId || stripeSession.client_reference_id;
      
      if (!planId) {
        return NextResponse.json({
          error: 'No se pudo determinar el plan del usuario'
        }, { status: 400 });
      }

      console.log('💳 Creando transacción faltante...');
      
      transaction = await SupabaseAdminService.createStripeTransaction({
        stripe_session_id: sessionId,
        stripe_customer_id: stripeSession.customer as string,
        user_email: stripeSession.customer_details?.email || stripeSession.metadata?.customerEmail || email!,
        user_name: stripeSession.customer_details?.name || stripeSession.metadata?.customerName,
        plan_id: planId,
        amount: stripeSession.amount_total || 0,
        currency: stripeSession.currency || 'eur',
        payment_status: 'paid',
        subscription_id: stripeSession.subscription as string || undefined,
        user_id: targetUserId,
        metadata: {
          created_by: 'reactivation_api',
          reactivated_at: new Date().toISOString()
        }
      });

      console.log('✅ Transacción creada:', transaction.id);
    }

    // 5. Actualizar perfil de usuario
    const userProfile = await SupabaseAdminService.getUserProfile(targetUserId);
    
    if (userProfile) {
      console.log('🔄 Actualizando perfil de usuario...');
      
      const updateData: any = {
        payment_verified: true,
        updated_at: new Date().toISOString(),
        security_flags: {
          ...userProfile.security_flags,
          reactivated: true,
          reactivated_at: new Date().toISOString(),
          payment_completed: true
        }
      };

      if (stripeSession) {
        updateData.stripe_customer_id = stripeSession.customer as string;
        updateData.last_payment_date = new Date().toISOString();
        
        if (stripeSession.subscription) {
          updateData.stripe_subscription_id = stripeSession.subscription as string;
          updateData.auto_renew = true;
        }
      }

      await SupabaseAdminService.upsertUserProfile({
        ...userProfile,
        ...updateData
      });

      console.log('✅ Perfil de usuario actualizado');
    }

    // 6. Activar transacción si existe
    if (transaction) {
      await SupabaseAdminService.activateTransaction(transaction.id);
      console.log('✅ Transacción activada');
    }

    // 7. Enviar email de confirmación
    console.log('📧 Enviando email de confirmación...');
    const emailResult = await SupabaseAdminService.sendConfirmationEmailForUser(targetUserId);
    
    if (!emailResult.success) {
      console.error('⚠️ Error enviando email:', emailResult.error);
      // No fallar completamente
    }

    return NextResponse.json({
      success: true,
      message: 'Usuario reactivado exitosamente',
      data: {
        userId: targetUserId,
        transactionId: transaction?.id,
        emailSent: emailResult.success,
        profileUpdated: !!userProfile
      }
    });

  } catch (error) {
    console.error('Error reactivando usuario:', error);
    return NextResponse.json({
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
