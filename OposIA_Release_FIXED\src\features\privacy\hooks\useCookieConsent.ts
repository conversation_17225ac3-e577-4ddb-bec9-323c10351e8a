// src/features/privacy/hooks/useCookieConsent.ts
// Hook personalizado para gestión de consentimiento de cookies

'use client';

import { useState, useEffect, useCallback } from 'react';
import { CookieConsentState, CookiePreferences } from '../types/cookie.types';
import { CookieConsentService } from '../services/CookieConsentService';
import { LocalStorageCookieRepository } from '../services/CookieStorageRepository';

// Instancia singleton del servicio
const cookieRepository = new LocalStorageCookieRepository();
const cookieConsentService = new CookieConsentService(cookieRepository);

export function useCookieConsent() {
  const [consentState, setConsentState] = useState<CookieConsentState>({
    hasConsent: false,
    preferences: { functional: true },
    lastUpdated: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [showBanner, setShowBanner] = useState(false);

  // Cargar estado inicial
  useEffect(() => {
    const loadConsentState = async () => {
      try {
        const state = await cookieConsentService.getConsentState();
        const shouldShow = await cookieConsentService.shouldShowBanner();
        
        setConsentState(state);
        setShowBanner(shouldShow);
      } catch (error) {
        console.error('Error loading cookie consent state:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadConsentState();
  }, []);

  const grantConsent = useCallback(async (preferences?: CookiePreferences) => {
    try {
      await cookieConsentService.grantConsent(preferences);
      
      const newState = await cookieConsentService.getConsentState();
      setConsentState(newState);
      setShowBanner(false);
    } catch (error) {
      console.error('Error granting cookie consent:', error);
    }
  }, []);

  const revokeConsent = useCallback(async () => {
    try {
      await cookieConsentService.revokeConsent();
      
      const newState = await cookieConsentService.getConsentState();
      setConsentState(newState);
      setShowBanner(true);
    } catch (error) {
      console.error('Error revoking cookie consent:', error);
    }
  }, []);

  const updatePreferences = useCallback(async (preferences: CookiePreferences) => {
    try {
      await cookieConsentService.updatePreferences(preferences);
      
      const newState = await cookieConsentService.getConsentState();
      setConsentState(newState);
    } catch (error) {
      console.error('Error updating cookie preferences:', error);
    }
  }, []);

  const hideBanner = useCallback(() => {
    setShowBanner(false);
  }, []);

  return {
    consentState,
    isLoading,
    showBanner,
    grantConsent,
    revokeConsent,
    updatePreferences,
    hideBanner,
  };
}
