#!/usr/bin/env node

/**
 * Script de configuración automática para desarrollo
 * Automatiza tareas comunes de desarrollo en OposIA
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`)
};

// Funciones de utilidad
const fileExists = (filePath) => fs.existsSync(filePath);
const createDir = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log.success(`Created directory: ${dirPath}`);
  }
};

const createFile = (filePath, content) => {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content);
    log.success(`Created file: ${filePath}`);
  } else {
    log.warning(`File already exists: ${filePath}`);
  }
};

const runCommand = (command, description) => {
  try {
    log.info(`${description}...`);
    execSync(command, { stdio: 'inherit' });
    log.success(`${description} completed`);
  } catch (error) {
    log.error(`${description} failed: ${error.message}`);
    process.exit(1);
  }
};

// Templates para archivos
const templates = {
  component: (name) => `import React from 'react';

interface ${name}Props {
  // Define props here
}

export const ${name}: React.FC<${name}Props> = (props) => {
  return (
    <div>
      <h1>${name} Component</h1>
      {/* Component content */}
    </div>
  );
};

export default ${name};
`,

  hook: (name) => `import { useState, useEffect } from 'react';

interface Use${name}Return {
  // Define return type here
}

export const use${name} = (): Use${name}Return => {
  // Hook implementation
  
  return {
    // Return values
  };
};
`,

  service: (name) => `class ${name}Service {
  // Service implementation
  
  async get(id: string) {
    // Implementation
  }
  
  async create(data: any) {
    // Implementation
  }
  
  async update(id: string, data: any) {
    // Implementation
  }
  
  async delete(id: string) {
    // Implementation
  }
}

export const ${name.toLowerCase()}Service = new ${name}Service();
`,

  test: (name, type) => `import { ${type === 'component' ? 'render, screen' : 'renderHook'} } from '@testing-library/react';
${type === 'component' ? `import userEvent from '@testing-library/user-event';` : ''}
import { ${name} } from '../${name}';

describe('${name}', () => {
  ${type === 'component' ? `
  it('should render correctly', () => {
    render(<${name} />);
    expect(screen.getByText('${name} Component')).toBeInTheDocument();
  });
  
  it('should handle user interactions', async () => {
    const user = userEvent.setup();
    render(<${name} />);
    
    // Test interactions
  });
  ` : `
  it('should work correctly', () => {
    const { result } = renderHook(() => ${name}());
    
    // Test hook behavior
    expect(result.current).toBeDefined();
  });
  `}
});
`,

  types: (name) => `export interface ${name} {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Create${name}Dto {
  // Define creation properties
}

export interface Update${name}Dto {
  // Define update properties
}
`
};

// Comandos disponibles
const commands = {
  async setup() {
    log.title('🚀 Setting up development environment');
    
    // Verificar Node.js version
    const nodeVersion = process.version;
    log.info(`Node.js version: ${nodeVersion}`);
    
    if (parseInt(nodeVersion.slice(1)) < 18) {
      log.error('Node.js 18+ is required');
      process.exit(1);
    }
    
    // Verificar .env.local
    if (!fileExists('.env.local')) {
      if (fileExists('.env.example')) {
        fs.copyFileSync('.env.example', '.env.local');
        log.success('Created .env.local from .env.example');
        log.warning('Please update .env.local with your actual values');
      } else {
        log.warning('.env.example not found, please create .env.local manually');
      }
    }
    
    // Instalar dependencias
    runCommand('npm install', 'Installing dependencies');
    
    // Verificar TypeScript
    runCommand('npm run type-check', 'Checking TypeScript');
    
    log.success('Development environment setup completed!');
  },

  async feature(featureName) {
    if (!featureName) {
      log.error('Feature name is required. Usage: npm run dev-script feature <name>');
      return;
    }
    
    log.title(`📁 Creating feature: ${featureName}`);
    
    const featurePath = `src/features/${featureName}`;
    const dirs = ['components', 'hooks', 'services', 'types', '__tests__'];
    
    // Crear directorios
    dirs.forEach(dir => {
      createDir(path.join(featurePath, dir));
      if (dir === '__tests__') {
        createDir(path.join(featurePath, dir, 'components'));
        createDir(path.join(featurePath, dir, 'hooks'));
        createDir(path.join(featurePath, dir, 'services'));
      }
    });
    
    // Crear archivos index
    dirs.filter(dir => dir !== '__tests__').forEach(dir => {
      createFile(
        path.join(featurePath, dir, 'index.ts'),
        '// Export all from this directory\n'
      );
    });
    
    // Crear archivo principal de feature
    createFile(
      path.join(featurePath, 'index.ts'),
      `// ${featureName} feature exports
export * from './components';
export * from './hooks';
export * from './services';
export * from './types';
`
    );
    
    log.success(`Feature ${featureName} created successfully!`);
  },

  async component(componentName, featureName) {
    if (!componentName) {
      log.error('Component name is required. Usage: npm run dev-script component <name> [feature]');
      return;
    }
    
    log.title(`⚛️ Creating component: ${componentName}`);
    
    const basePath = featureName 
      ? `src/features/${featureName}/components`
      : 'src/components';
    
    // Crear componente
    createFile(
      path.join(basePath, `${componentName}.tsx`),
      templates.component(componentName)
    );
    
    // Crear test
    createFile(
      path.join(basePath.replace('/components', '/__tests__/components'), `${componentName}.test.tsx`),
      templates.test(componentName, 'component')
    );
    
    log.success(`Component ${componentName} created successfully!`);
  },

  async hook(hookName, featureName) {
    if (!hookName) {
      log.error('Hook name is required. Usage: npm run dev-script hook <name> [feature]');
      return;
    }
    
    log.title(`🪝 Creating hook: ${hookName}`);
    
    const basePath = featureName 
      ? `src/features/${featureName}/hooks`
      : 'src/hooks';
    
    // Crear hook
    createFile(
      path.join(basePath, `${hookName}.ts`),
      templates.hook(hookName.replace('use', ''))
    );
    
    // Crear test
    createFile(
      path.join(basePath.replace('/hooks', '/__tests__/hooks'), `${hookName}.test.ts`),
      templates.test(hookName, 'hook')
    );
    
    log.success(`Hook ${hookName} created successfully!`);
  },

  async service(serviceName, featureName) {
    if (!serviceName) {
      log.error('Service name is required. Usage: npm run dev-script service <name> [feature]');
      return;
    }
    
    log.title(`🔧 Creating service: ${serviceName}`);
    
    const basePath = featureName 
      ? `src/features/${featureName}/services`
      : 'src/lib/services';
    
    // Crear service
    createFile(
      path.join(basePath, `${serviceName}Service.ts`),
      templates.service(serviceName)
    );
    
    // Crear test
    const testPath = featureName
      ? path.join(`src/features/${featureName}/__tests__/services`, `${serviceName}Service.test.ts`)
      : path.join('src/lib/__tests__', `${serviceName}Service.test.ts`);
    
    createFile(testPath, templates.test(`${serviceName}Service`, 'service'));
    
    log.success(`Service ${serviceName} created successfully!`);
  },

  async check() {
    log.title('🔍 Running all checks');
    
    runCommand('npm run type-check', 'TypeScript check');
    runCommand('npm run lint', 'ESLint check');
    runCommand('npm test -- --passWithNoTests', 'Running tests');
    
    log.success('All checks passed!');
  },

  async clean() {
    log.title('🧹 Cleaning project');
    
    const dirsToClean = ['.next', 'node_modules/.cache', 'coverage'];
    
    dirsToClean.forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
        log.success(`Cleaned ${dir}`);
      }
    });
    
    log.success('Project cleaned!');
  },

  help() {
    log.title('🛠️ OposIA Development Script');
    
    console.log('Available commands:');
    console.log('');
    console.log('  setup                           - Setup development environment');
    console.log('  feature <name>                  - Create new feature structure');
    console.log('  component <name> [feature]      - Create new component');
    console.log('  hook <name> [feature]           - Create new hook');
    console.log('  service <name> [feature]        - Create new service');
    console.log('  check                           - Run all checks (types, lint, tests)');
    console.log('  clean                           - Clean build artifacts');
    console.log('  help                            - Show this help');
    console.log('');
    console.log('Examples:');
    console.log('  npm run dev-script setup');
    console.log('  npm run dev-script feature auth');
    console.log('  npm run dev-script component LoginForm auth');
    console.log('  npm run dev-script hook useAuth auth');
    console.log('  npm run dev-script service Auth auth');
  }
};

// Ejecutar comando
const [,, command, ...args] = process.argv;

if (!command || !commands[command]) {
  commands.help();
  process.exit(1);
}

commands[command](...args).catch(error => {
  log.error(`Command failed: ${error.message}`);
  process.exit(1);
});
