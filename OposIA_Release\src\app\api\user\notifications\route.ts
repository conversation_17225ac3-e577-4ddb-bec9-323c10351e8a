// src/app/api/user/notifications/route.ts
// Endpoint para que usuarios consulten su historial de notificaciones

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { EmailNotificationService } from '@/lib/services/email/emailNotificationService';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación del usuario
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        error: 'No autorizado'
      }, { status: 401 });
    }

    // Obtener parámetros de consulta
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type') || undefined;

    // Obtener notificaciones del usuario
    const result = await EmailNotificationService.getUserNotifications(
      user.id,
      limit,
      type
    );

    // Formatear respuesta para el frontend
    const formattedNotifications = result.notifications.map(notification => ({
      id: notification.id,
      type: notification.type,
      subject: notification.subject,
      sentAt: notification.sent_at,
      status: notification.status,
      metadata: notification.metadata,
      // No incluir contenido completo del email por privacidad/tamaño
    }));

    return NextResponse.json({
      success: true,
      data: {
        notifications: formattedNotifications,
        total: result.total,
        hasMore: result.total > limit
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error obteniendo notificaciones del usuario:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
