TITLE: Create Public Profiles Table with <PERSON><PERSON> and Auth Users Reference
DESCRIPTION: This SQL snippet demonstrates how to create a `public.profiles` table that references the `auth.users` table with `on delete cascade` and enables Row Level Security (RLS). This allows secure access to user data via the API while maintaining data integrity with Supabase's authentication system.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/auth/managing-user-data.mdx#_snippet_0

LANGUAGE: SQL
CODE:
```
create table public.profiles (
  id uuid not null references auth.users on delete cascade,
  first_name text,
  last_name text,

  primary key (id)
);

alter table public.profiles enable row level security;
```

----------------------------------------

TITLE: Implementing User Authentication Component with Supabase in React Native (TSX)
DESCRIPTION: This React Native component (`Auth.tsx`) provides a user interface for email and password-based sign-in and sign-up using Supabase. It manages input states, handles loading indicators, and integrates Supabase authentication methods. Additionally, it sets up an `AppState` listener to ensure continuous session refreshing when the app is active.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/tutorials/with-expo-react-native.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import React, { useState } from 'react'
import { Alert, StyleSheet, View, AppState } from 'react-native'
import { supabase } from '../lib/supabase'
import { Button, Input } from '@rneui/themed'

// Tells Supabase Auth to continuously refresh the session automatically if
// the app is in the foreground. When this is added, you will continue to receive
// `onAuthStateChange` events with the `TOKEN_REFRESHED` or `SIGNED_OUT` event
// if the user's session is terminated. This should only be registered once.
AppState.addEventListener('change', (state) => {
  if (state === 'active') {
    supabase.auth.startAutoRefresh()
  } else {
    supabase.auth.stopAutoRefresh()
  }
})

export default function Auth() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)

  async function signInWithEmail() {
    setLoading(true)
    const { error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    })

    if (error) Alert.alert(error.message)
    setLoading(false)
  }

  async function signUpWithEmail() {
    setLoading(true)
    const {
      data: { session },
      error,
    } = await supabase.auth.signUp({
      email: email,
      password: password,
    })

    if (error) Alert.alert(error.message)
    if (!session) Alert.alert('Please check your inbox for email verification!')
    setLoading(false)
  }

  return (
    <View style={styles.container}>
      <View style={[styles.verticallySpaced, styles.mt20]}>
        <Input
          label="Email"
          leftIcon={{ type: 'font-awesome', name: 'envelope' }}
          onChangeText={(text) => setEmail(text)}
          value={email}
          placeholder="<EMAIL>"
          autoCapitalize={'none'}
        />
      </View>
      <View style={styles.verticallySpaced}>
        <Input
          label="Password"
          leftIcon={{ type: 'font-awesome', name: 'lock' }}
          onChangeText={(text) => setPassword(text)}
          value={password}
          secureTextEntry={true}
          placeholder="Password"
          autoCapitalize={'none'}
        />
      </View>
      <View style={[styles.verticallySpaced, styles.mt20]}>
        <Button title="Sign in" disabled={loading} onPress={() => signInWithEmail()} />
      </View>
      <View style={styles.verticallySpaced}>
        <Button title="Sign up" disabled={loading} onPress={() => signUpWithEmail()} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  mt20: {
    marginTop: 20,
  },
})
```

----------------------------------------

TITLE: Initializing Supabase Client in TypeScript
DESCRIPTION: This TypeScript code initializes the Supabase client using the createClient function from @supabase/supabase-js. It retrieves the Supabase URL and anonymous key from environment variables, establishing the connection to the Supabase project.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2024-09-23-local-first-expo-legend-state.mdx#_snippet_3

LANGUAGE: typescript
CODE:
```
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
)
```

----------------------------------------

TITLE: Creating User Roles and Permissions Tables in SQL
DESCRIPTION: This SQL script defines custom types for application roles (`app_role`) and permissions (`app_permission`), then creates two tables: `user_roles` to assign roles to users, and `role_permissions` to map specific permissions to each role. This setup forms the foundation for implementing role-based access control within the application.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/postgres/custom-claims-and-role-based-access-control-rbac.mdx#_snippet_1

LANGUAGE: sql
CODE:
```
-- Custom types
create type public.app_permission as enum ('channels.delete', 'messages.delete');
create type public.app_role as enum ('admin', 'moderator');

-- USER ROLES
create table public.user_roles (
  id        bigint generated by default as identity primary key,
  user_id   uuid references auth.users on delete cascade not null,
  role      app_role not null,
  unique (user_id, role)
);
comment on table public.user_roles is 'Application roles for each user.';

-- ROLE PERMISSIONS
create table public.role_permissions (
  id           bigint generated by default as identity primary key,
  role         app_role not null,
  permission   app_permission not null,
  unique (role, permission)
);
comment on table public.role_permissions is 'Application permissions for each role.';
```

----------------------------------------

TITLE: Creating Documents Table with Vector Column in Postgres
DESCRIPTION: This SQL statement creates a new table named `documents` to store textual content and its corresponding embedding vectors. The `embedding` column uses the `vector(512)` data type, where 512 represents the dimensionality of the embedding, which should match the output of your chosen embedding model.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/ai/semantic-search.mdx#_snippet_1

LANGUAGE: SQL
CODE:
```
create table documents (
  id bigint primary key generated always as identity,
  content text,
  embedding vector(512)
);
```

----------------------------------------

TITLE: Fetching Data with Supabase in Next.js Server Components
DESCRIPTION: This example illustrates how to fetch data directly from Supabase within a Next.js Server Component. By using await supabase.from('...').select(), data can be retrieved on the server before the component is rendered, simplifying data fetching logic and improving initial page load performance.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2023-11-01-supabase-is-now-compatible-with-nextjs-14.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
export default async function Page() {
  const { data } = await supabase.from('...').select()
  return ...
}
```

----------------------------------------

TITLE: Fetching Single Post with Static Revalidation (Next.js, Supabase)
DESCRIPTION: This Next.js Server Component handles fetching a single post by ID from Supabase. It uses `generateStaticParams` to pre-render pages for known IDs and `revalidate = 60` to ensure the data is refreshed periodically, similar to `getStaticPaths` and `getStaticProps` with revalidation.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2022-11-17-fetching-and-caching-supabase-data-in-next-js-server-components.mdx#_snippet_13

LANGUAGE: tsx
CODE:
```
import supabase from '../../../utils/supabase'
import { notFound } from 'next/navigation'

export const revalidate = 60

export async function generateStaticParams() {
  const { data: posts } = await supabase.from('posts').select('id')

  return posts?.map(({ id }) => ({
    id,
  }))
}

export default async function Post({ params: { id } }: { params: { id: string } }) {
  const { data: post } = await supabase.from('posts').select().match({ id }).single()

  if (!post) {
    notFound()
  }

  return <pre>{JSON.stringify(post, null, 2)}</pre>
}
```

----------------------------------------

TITLE: Protecting Next.js API Routes - JavaScript
DESCRIPTION: This snippet shows how to protect a Next.js API route by checking for an authenticated Supabase user. It creates a server-side Supabase client, verifies the user's session, and if no user is found, returns a 401 unauthorized error. Otherwise, it proceeds to query the database with RLS.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/auth/auth-helpers/nextjs-pages.mdx#_snippet_16

LANGUAGE: jsx
CODE:
```
import { createPagesServerClient } from '@supabase/auth-helpers-nextjs'

const ProtectedRoute = async (req, res) => {
  // Create authenticated Supabase Client
  const supabase = createPagesServerClient({ req, res })
  // Check if we have a user
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user)
    return res.status(401).json({
      error: 'not_authenticated',
      description: 'The user does not have an active session or is not authenticated',
    })

  // Run queries with RLS on the server
  const { data } = await supabase.from('test').select('*')
  res.json(data)
}

export default ProtectedRoute
```

----------------------------------------

TITLE: Implementing User Authentication Form with Supabase in React Native
DESCRIPTION: This snippet defines the main login and registration screen for a React Native application. It includes input fields for email and password, and buttons for signing in and signing up. It utilizes Supabase's `signInWithPassword` and `signUp` methods for authentication, displaying alerts for errors and managing a loading spinner during API calls.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2023-08-01-react-native-storage.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import { Alert, View, Button, TextInput, StyleSheet, Text, TouchableOpacity } from 'react-native'
import { useState } from 'react'
import React from 'react'
import Spinner from 'react-native-loading-spinner-overlay'
import { supabase } from '../config/initSupabase'

const Login = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)

  // Sign in with email and password
  const onSignInPress = async () => {
    setLoading(true)

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) Alert.alert(error.message)
    setLoading(false)
  }

  // Create a new user
  const onSignUpPress = async () => {
    setLoading(true)
    const { error } = await supabase.auth.signUp({
      email: email,
      password: password,
    })

    if (error) Alert.alert(error.message)
    setLoading(false)
  }

  return (
    <View style={styles.container}>
      <Spinner visible={loading} />

      <Text style={styles.header}>My Cloud</Text>

      <TextInput
        autoCapitalize="none"
        placeholder="<EMAIL>"
        value={email}
        onChangeText={setEmail}
        style={styles.inputField}
      />
      <TextInput
        placeholder="password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
        style={styles.inputField}
      />

      <TouchableOpacity onPress={onSignInPress} style={styles.button}>
        <Text style={{ color: '#fff' }}>Sign in</Text>
      </TouchableOpacity>
      <Button onPress={onSignUpPress} title="Create Account" color={'#fff'}></Button>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 200,
    padding: 20,
    backgroundColor: '#151515',
  },
  header: {
    fontSize: 30,
    textAlign: 'center',
    margin: 50,
    color: '#fff',
  },
  inputField: {
    marginVertical: 4,
    height: 50,
    borderWidth: 1,
    borderColor: '#2b825b',
    borderRadius: 4,
    padding: 10,
    color: '#fff',
    backgroundColor: '#363636',
  },
  button: {
    marginVertical: 15,
    alignItems: 'center',
    backgroundColor: '#2b825b',
    padding: 12,
    borderRadius: 4,
  },
})

export default Login
```

----------------------------------------

TITLE: Inserting Multiple Rows into a Table (SQL)
DESCRIPTION: This SQL `INSERT` statement adds two new movie records, 'The Empire Strikes Back' and 'Return of the Jedi', into the `movies` table. It specifies `name` and `description` columns for the data insertion.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/tables.mdx#_snippet_3

LANGUAGE: SQL
CODE:
```
insert into movies
  (name, description)
values
  (
    'The Empire Strikes Back',
    'After the Rebels are brutally overpowered by the Empire on the ice planet Hoth, Luke Skywalker begins Jedi training with Yoda.'
  ),
  (
    'Return of the Jedi',
    'After a daring mission to rescue Han Solo from Jabba the Hutt, the Rebels dispatch to Endor to destroy the second Death Star.'
  );
```

----------------------------------------

TITLE: Creating Users via Admin API with Edge Functions (SQL/JavaScript)
DESCRIPTION: This example illustrates how to use `edge.exec` to execute JavaScript code that interacts with the Supabase Auth Admin API. The embedded JavaScript calls `supabase.auth.admin.createUser` to create a new user with a specified email, password, and user metadata, enabling administrative user management directly from SQL.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2024-11-13-supabase-dynamic-functions.mdx#_snippet_14

LANGUAGE: SQL
CODE:
```
select edge.exec(
$js$

const { data, error } = await supabase.auth.admin.createUser({
  email: '<EMAIL>',
  password: 'password',
  user_metadata: { name: 'Yoda' }
});

$js$));
```

----------------------------------------

TITLE: Initializing Supabase Client in TypeScript
DESCRIPTION: This TypeScript snippet demonstrates how to create and export a Supabase client instance using the `createClient` function from `@supabase/supabase-js`. It retrieves the Supabase URL and `anon` key from environment variables, enabling the application to interact with Supabase services like authentication and database operations.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/tutorials/with-ionic-vue.mdx#_snippet_3

LANGUAGE: typescript
CODE:
```
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

----------------------------------------

TITLE: Creating a SELECT Policy for User's Own Profile
DESCRIPTION: This policy restricts `SELECT` access on the `profiles` table, allowing users to view only their own profile. It achieves this by comparing the authenticated user's ID (`auth.uid()`) with the `user_id` column of the profile.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/postgres/row-level-security.mdx#_snippet_6

LANGUAGE: SQL
CODE:
```
create policy "User can see their own profile only."
on profiles
for select using ( (select auth.uid()) = user_id );
```

----------------------------------------

TITLE: Configuring Supabase Environment Variables
DESCRIPTION: This snippet shows the required environment variables for connecting a Next.js application to Supabase. `NEXT_PUBLIC_SUPABASE_URL` is the URL of your Supabase project, and `NEXT_PUBLIC_SUPABASE_ANON_KEY` is your client-side API key for anonymous access.
SOURCE: https://github.com/supabase/supabase/blob/master/examples/realtime/nextjs-auth-presence/README.md#_snippet_0

LANGUAGE: Text
CODE:
```
NEXT_PUBLIC_SUPABASE_URL=<<insert-your-db-url-here>>

NEXT_PUBLIC_SUPABASE_ANON_KEY=<<insert-your-anon-key-here>>
```

----------------------------------------

TITLE: Configuring Supabase Environment Variables
DESCRIPTION: This snippet defines environment variables for the Supabase API URL and the `anon` key in a `.env` file. These variables are crucial for the application to connect and authenticate with the Supabase project.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/tutorials/with-solidjs.mdx#_snippet_2

LANGUAGE: bash
CODE:
```
VITE_SUPABASE_URL=YOUR_SUPABASE_URL
VITE_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
```

----------------------------------------

TITLE: Performing Hybrid Search with LangChain and Supabase
DESCRIPTION: This JavaScript code demonstrates how to use the `SupabaseHybridSearch` retriever from LangChain to perform a hybrid search (combining similarity and keyword search) against a Supabase backend. It initializes a Supabase client and OpenAI embeddings, then configures the retriever with table and query names, and finally executes a search query.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2023-04-14-dbdev.mdx#_snippet_3

LANGUAGE: JavaScript
CODE:
```
import { OpenAIEmbeddings } from 'langchain/embeddings/openai'
import { createClient } from '@supabase/supabase-js'
import { SupabaseHybridSearch } from 'langchain/retrievers/supabase'

const privateKey = process.env.SUPABASE_PRIVATE_KEY
if (!privateKey) throw new Error(`Expected env var SUPABASE_PRIVATE_KEY`)

const url = process.env.SUPABASE_URL
if (!url) throw new Error(`Expected env var SUPABASE_URL`)

export const run = async () => {
  const client = createClient(url, privateKey)

  const embeddings = new OpenAIEmbeddings()

  const retriever = new SupabaseHybridSearch(embeddings, {
    client,
    //  Below are the defaults, expecting that you set up your supabase table and functions according to the guide above. Please change if necessary.
    similarityK: 2,
    keywordK: 2,
    tableName: 'documents',
    similarityQueryName: 'match_documents',
    keywordQueryName: 'kw_match_documents',
  })

  const results = await retriever.getRelevantDocuments('hello bye')

  console.log(results)
}
```

----------------------------------------

TITLE: Initializing Supabase Client
DESCRIPTION: This section demonstrates how to initialize the Supabase client using your project's URL and anonymous public API key. These credentials can be found in your Supabase project's API Settings.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/realtime/presence.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = 'https://<project>.supabase.co'
const SUPABASE_KEY = '<your-anon-key>'

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY)
```

LANGUAGE: Dart
CODE:
```
void main() {
  Supabase.initialize(
    url: 'https://<project>.supabase.co',
    anonKey: '<your-anon-key>',
  );

  runApp(MyApp());
}

final supabase = Supabase.instance.client;
```

LANGUAGE: Swift
CODE:
```
let supabaseURL = "https://<project>.supabase.co"
let supabaseKey = "<your-anon-key>"
let supabase = SupabaseClient(supabaseURL: URL(string: supabaseURL)!, supabaseKey: supabaseKey)

let realtime = supabase.realtime
```

LANGUAGE: Kotlin
CODE:
```
val supabaseUrl = "https://<project>.supabase.co"
val supabaseKey = "<your-anon-key>"
val supabase = createSupabaseClient(supabaseUrl, supabaseKey) {
    install(Realtime)
}
```

LANGUAGE: Python
CODE:
```
from supabase import create_client

SUPABASE_URL = 'https://<project>.supabase.co'
SUPABASE_KEY = '<your-anon-key>'

supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
```

----------------------------------------

TITLE: Implement Supabase Authentication Middleware in Next.js
DESCRIPTION: This code snippet provides the implementation for a Next.js middleware designed to handle Supabase authentication. Its primary functions include refreshing expired authentication tokens by calling `supabase.auth.getUser()`, and then propagating these refreshed tokens to both Server Components (via `request.cookies.set`) and the user's browser (via `response.cookies.set`). The `config.matcher` is used to specify paths where the middleware should run, optimizing performance. A critical security warning is highlighted: always use `supabase.auth.getUser()` for protecting pages and user data, and never rely on `supabase.auth.getSession()` in server-side code like middleware, as it does not guarantee token revalidation. The snippet also includes important guidelines on the correct handling of `supabaseResponse` and the placement of `auth.getUser()` calls to prevent session issues.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/auth/server-side/nextjs.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
import { type NextRequest } from 'next/server'
import { updateSession } from '@/utils/supabase/middleware'

export async function middleware(request: NextRequest) {
  return await updateSession(request)
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'
  ]
}
```

LANGUAGE: TypeScript
CODE:
```
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        }
      }
    }
  )

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()

  const {
    data: { user }
  } = await supabase.auth.getUser()

  if (
    !user &&
    !request.nextUrl.pathname.startsWith('/login') &&
    !request.nextUrl.pathname.startsWith('/auth')
  ) {
    // no user, potentially respond by redirecting the user to the login page
    const url = request.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}
```

----------------------------------------

TITLE: Enabling pgvector Extension in PostgreSQL (SQL)
DESCRIPTION: Enables the `pgvector` extension in your PostgreSQL database schema, which is required for storing and querying vector embeddings for similarity search.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/ai/examples/nextjs-vector-search.mdx#_snippet_2

LANGUAGE: sql
CODE:
```
-- Enable pgvector extension
create extension if not exists vector with schema public;
```

----------------------------------------

TITLE: Implement Supabase Login and Signup Server Actions
DESCRIPTION: This server-side code defines `login` and `signup` functions using Supabase for user authentication. It handles form data, calls Supabase authentication methods, and redirects users based on success or error, revalidating the path on success.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/auth/server-side/nextjs.mdx#_snippet_5

LANGUAGE: ts
CODE:
```
'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

import { createClient } from '@/utils/supabase/server'

export async function login(formData: FormData) {
  const supabase = await createClient()

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  const { error } = await supabase.auth.signInWithPassword(data)

  if (error) {
    redirect('/error')
  }

  revalidatePath('/', 'layout')
  redirect('/')
}

export async function signup(formData: FormData) {
  const supabase = await createClient()

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  const { error } = await supabase.auth.signUp(data)

  if (error) {
    redirect('/error')
  }

  revalidatePath('/', 'layout')
  redirect('/')
}
```

----------------------------------------

TITLE: Initializing Supabase Client in SvelteKit (JavaScript)
DESCRIPTION: This JavaScript snippet creates `src/lib/supabaseClient.js` to initialize the Supabase client. It imports `createClient` from `@supabase/supabase-js` and exports a `supabase` instance configured with the project's URL and public (anon) key, enabling database interactions.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/quickstarts/sveltekit.mdx#_snippet_2

LANGUAGE: javascript
CODE:
```
import { createClient } from '@supabase/supabase-js'

export const supabase = createClient('https://<project>.supabase.co', '<your-anon-key>')
```

----------------------------------------

TITLE: Creating an Avatar Upload Widget in Flutter
DESCRIPTION: This snippet defines a `StatefulWidget` called `Avatar` that enables users to select an image from their gallery, upload it to Supabase Storage, and display the current avatar. It handles image picking, byte conversion, file naming, and Supabase storage interactions, including generating a signed URL. Error handling for storage operations is also included.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/getting-started/tutorials/with-flutter.mdx#_snippet_12

LANGUAGE: Dart
CODE:
```
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:supabase_quickstart/main.dart';

class Avatar extends StatefulWidget {
  const Avatar({
    super.key,
    required this.imageUrl,
    required this.onUpload,
  });

  final String? imageUrl;
  final void Function(String) onUpload;

  @override
  State<Avatar> createState() => _AvatarState();
}

class _AvatarState extends State<Avatar> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.imageUrl == null || widget.imageUrl!.isEmpty)
          Container(
            width: 150,
            height: 150,
            color: Colors.grey,
            child: const Center(
              child: Text('No Image'),
            ),
          )
        else
          Image.network(
            widget.imageUrl!,
            width: 150,
            height: 150,
            fit: BoxFit.cover,
          ),
        ElevatedButton(
          onPressed: _isLoading ? null : _upload,
          child: const Text('Upload'),
        ),
      ],
    );
  }

  Future<void> _upload() async {
    final picker = ImagePicker();
    final imageFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 300,
      maxHeight: 300,
    );
    if (imageFile == null) {
      return;
    }
    setState(() => _isLoading = true);

    try {
      final bytes = await imageFile.readAsBytes();
      final fileExt = imageFile.path.split('.').last;
      final fileName = '${DateTime.now().toIso8601String()}.$fileExt';
      final filePath = fileName;
      await supabase.storage.from('avatars').uploadBinary(
            filePath,
            bytes,
            fileOptions: FileOptions(contentType: imageFile.mimeType),
          );
      final imageUrlResponse = await supabase.storage
          .from('avatars')
          .createSignedUrl(filePath, 60 * 60 * 24 * 365 * 10);
      widget.onUpload(imageUrlResponse);
    } on StorageException catch (error) {
      if (mounted) {
        context.showSnackBar(error.message, isError: true);
      }
    } catch (error) {
      if (mounted) {
        context.showSnackBar('Unexpected error occurred', isError: true);
      }
    }

    setState(() => _isLoading = false);
  }
}
```

----------------------------------------

TITLE: Declaring Supabase Environment Variables (.env.local)
DESCRIPTION: This snippet shows how to declare public Supabase URL and anonymous key as environment variables in a `.env.local` file. These variables are crucial for the Supabase client to connect to your project's API and are retrieved from your Supabase project settings.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/auth/auth-helpers/sveltekit.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
# Find these in your Supabase project settings https://supabase.com/dashboard/project/_/settings/api
PUBLIC_SUPABASE_URL=https://your-project.supabase.co
PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

----------------------------------------

TITLE: Optimizing Filtered Sequential Scans in PostgreSQL
DESCRIPTION: This EXPLAIN ANALYZE output demonstrates a long-running Sequential Scan on the products table with a filter condition. The high number of 'Rows Removed by Filter' (2997 out of 3000) indicates that an index on the 'price' column would significantly improve performance by avoiding a full table scan.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/troubleshooting/understanding-postgresql-explain-output-Un9dqX.mdx#_snippet_5

LANGUAGE: PostgreSQL Explain Output
CODE:
```
Seq Scan on products  (cost=0.00..100.00 rows=300 width=50) (actual time=50.000..100.000 rows=3 loops=1)
   Filter: (price > 1000)
   Rows Removed by Filter: 2997
```

----------------------------------------

TITLE: Querying Document Sections with RLS Applied (JWT/REST API) - SQL
DESCRIPTION: This SQL query selects document sections based on an embedding similarity match. When executed via the REST API with a valid JWT, Row Level Security (RLS) automatically filters the results to include only document sections owned by the user identified by the JWT's `sub` claim, leveraging the `auth.uid()` function.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/ai/rag-with-permissions.mdx#_snippet_13

LANGUAGE: SQL
CODE:
```
-- Only document sections owned by the user are returned
select *
from document_sections
where document_sections.embedding <#> embedding < -match_threshold
order by document_sections.embedding <#> embedding;
```

----------------------------------------

TITLE: Calling Supabase RPC Function to Test Authorization Header in JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to call the `test_authorization_header` SQL function using the Supabase client's `rpc` method. It retrieves the JWT payload, logs the user's role and UUID, and helps verify if a user session is correctly established when making database calls from the client-side application.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/troubleshooting/why-is-my-select-returning-an-empty-data-array-and-i-have-data-in-the-table-xvOPgx.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const { data: testData, error: testError } = await supabase.rpc('test_authorization_header')
console.log(`The user role is ${testData.role} and the user UUID is ${testData.sub}. `, testError)
```

----------------------------------------

TITLE: Creating Typed Supabase Server Client in API Route (TypeScript)
DESCRIPTION: This TypeScript example illustrates how to create a typed Supabase server client within a Next.js API route using `createPagesServerClient`. It fetches user data from authentication, providing type safety for both the client and the user object.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/auth/auth-helpers/nextjs-pages.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
// Creating a new supabase server client object (e.g. in API route):
import { createPagesServerClient } from '@supabase/auth-helpers-nextjs'
import type { NextApiRequest, NextApiResponse } from 'next'
import type { Database } from 'types_db'

export default async (req: NextApiRequest, res: NextApiResponse) => {
  const supabaseServerClient = createPagesServerClient<Database>({
    req,
    res,
  })
  const {
    data: { user },
  } = await supabaseServerClient.auth.getUser()

  res.status(200).json({ name: user?.name ?? '' })
}
```

----------------------------------------

TITLE: Creating a SELECT Policy for User-Owned Data in Postgres
DESCRIPTION: This policy allows individuals to view only their own 'todos' by comparing the authenticated user's ID (obtained via `auth.uid()`) with the `user_id` column in the `todos` table. It acts as an implicit `WHERE` clause for `SELECT` operations.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/postgres/row-level-security.mdx#_snippet_1

LANGUAGE: SQL
CODE:
```
create policy "Individuals can view their own todos."
on todos for select
using ( (select auth.uid()) = user_id );
```

----------------------------------------

TITLE: Defining User Profiles Table and Row Level Security Policies (SQL)
DESCRIPTION: This SQL script defines the 'profiles' table, sets up Row Level Security (RLS) policies for public viewing, user-specific insertion and updates, and creates a trigger to automatically create a profile entry upon new user sign-up. It also configures a 'storage.buckets' entry for avatars and sets up RLS policies for avatar image access.
SOURCE: https://github.com/supabase/supabase/blob/master/examples/user-management/nextjs-user-management/README.md#_snippet_1

LANGUAGE: sql
CODE:
```
-- Create a table for public profiles
create table profiles (
  id uuid references auth.users not null primary key,
  updated_at timestamp with time zone,
  username text unique,
  full_name text,
  avatar_url text,
  website text,

  constraint username_length check (char_length(username) >= 3)
);
-- Set up Row Level Security (RLS)
-- See https://supabase.com/docs/guides/auth/row-level-security for more details.
alter table profiles
  enable row level security;

create policy "Public profiles are viewable by everyone." on profiles
  for select using (true);

create policy "Users can insert their own profile." on profiles
  for insert with check ((select auth.uid()) = id);

create policy "Users can update own profile." on profiles
  for update using ((select auth.uid()) = id);

-- This trigger automatically creates a profile entry when a new user signs up via Supabase Auth.
-- See https://supabase.com/docs/guides/auth/managing-user-data#using-triggers for more details.
create function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, full_name, avatar_url)
  values (new.id, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  return new;
end;
$$ language plpgsql security definer;
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Set up Storage!
insert into storage.buckets (id, name)
  values ('avatars', 'avatars');

-- Set up access controls for storage.
-- See https://supabase.com/docs/guides/storage#policy-examples for more details.
create policy "Avatar images are publicly accessible." on storage.objects
  for select using (bucket_id = 'avatars');

create policy "Anyone can upload an avatar." on storage.objects
  for insert with check (bucket_id = 'avatars');

create policy "Anyone can update their own avatar." on storage.objects
  for update using ( auth.uid() = owner ) with check (bucket_id = 'avatars');
```

----------------------------------------

TITLE: Unnesting Response Status Code in Supabase Edge Logs (SQL)
DESCRIPTION: This query specifically targets the `status_code` within the `metadata.response` field of `edge_logs`. It provides a direct way to filter and analyze API responses based on their HTTP status, enabling quick detection of successful requests (e.g., 200) or various error types (e.g., 404, 500).
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/troubleshooting/discovering-and-interpreting-api-errors-in-the-logs-7xREI9.mdx#_snippet_4

LANGUAGE: SQL
CODE:
```
select
  status_code
from
  edge_logs
  -- Unpack 'metadata' field
  cross join unnest(metadata) as metadata
  -- unpack 'response' from 'metadata'
  cross join unnest(response) as response;
```

----------------------------------------

TITLE: Querying Supabase Data with .then() and async/await in JavaScript
DESCRIPTION: This snippet demonstrates two ways to query data from a Supabase table named 'countries': using promise-based `.then()` syntax and modern `async/await` syntax. Both methods select all columns, limit the results to 5 records, and log the data or any errors to the console.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/www/_blog/2021-03-11-using-supabase-replit.mdx#_snippet_2

LANGUAGE: JavaScript
CODE:
```
// .then() syntax
supabase.
  .from('countries')
  .select('*')
  .limit(5)
  .then(console.log)
  .catch(console.error)

// or...
// async/await syntax
const main = async() => {
  const { data, error } = supabase
    .from('countries')
    .select('*')
    .limit(5)

  if (error) {
    console.log(error)
    return
  }

  console.log(data)
}
main()
```

----------------------------------------

TITLE: Migrating Supabase Storage Objects using JavaScript
DESCRIPTION: This script facilitates the migration of Storage objects between two Supabase projects. It connects to both old and new projects using their respective URLs and service keys, fetches all objects from the old project's storage, downloads each object, and then uploads it to the corresponding bucket in the new project. It handles potential errors during object retrieval, download, and upload, and includes options for upserting and preserving metadata like content type and cache control.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/platform/migrating-within-supabase/backup-restore.mdx#_snippet_9

LANGUAGE: JavaScript
CODE:
```
// npm install @supabase/supabase-js@1
const { createClient } = require('@supabase/supabase-js')

const OLD_PROJECT_URL = 'https://xxx.supabase.co'
const OLD_PROJECT_SERVICE_KEY = 'old-project-service-key-xxx'

const NEW_PROJECT_URL = 'https://yyy.supabase.co'
const NEW_PROJECT_SERVICE_KEY = 'new-project-service-key-yyy'

;(async () => {
  const oldSupabaseRestClient = createClient(OLD_PROJECT_URL, OLD_PROJECT_SERVICE_KEY, {
    db: {
      schema: 'storage',
    },
  })
  const oldSupabaseClient = createClient(OLD_PROJECT_URL, OLD_PROJECT_SERVICE_KEY)
  const newSupabaseClient = createClient(NEW_PROJECT_URL, NEW_PROJECT_SERVICE_KEY)

  // make sure you update max_rows in postgrest settings if you have a lot of objects
  // or paginate here
  const { data: oldObjects, error } = await oldSupabaseRestClient.from('objects').select()
  if (error) {
    console.log('error getting objects from old bucket')
    throw error
  }

  for (const objectData of oldObjects) {
    console.log(`moving ${objectData.id}`)
    try {
      const { data, error: downloadObjectError } = await oldSupabaseClient.storage
        .from(objectData.bucket_id)
        .download(objectData.name)
      if (downloadObjectError) {
        throw downloadObjectError
      }

      const { _, error: uploadObjectError } = await newSupabaseClient.storage
        .from(objectData.bucket_id)
        .upload(objectData.name, data, {
          upsert: true,
          contentType: objectData.metadata.mimetype,
          cacheControl: objectData.metadata.cacheControl,
        })
      if (uploadObjectError) {
        throw uploadObjectError
      }
    } catch (err) {
      console.log('error moving ', objectData)
      console.log(err)
    }
  }
})()
```

----------------------------------------

TITLE: Defining RLS Policy with `exists` and `auth.uid()` in SQL
DESCRIPTION: This SQL snippet defines a Row Level Security policy named 'rls_test_select' on 'test_table'. It restricts access to authenticated users, allowing selection only if their `auth.uid()` matches a 'user_id' in 'roles_table' where the 'role' is 'good_role'. This policy demonstrates a common RLS setup that might incur performance penalties due to the subquery.
SOURCE: https://github.com/supabase/supabase/blob/master/apps/docs/content/guides/database/postgres/row-level-security.mdx#_snippet_19

LANGUAGE: SQL
CODE:
```
create policy "rls_test_select" on test_table
to authenticated
using (
  exists (
    select 1 from roles_table
    where (select auth.uid()) = user_id and role = 'good_role'
  )
);
```

----------------------------------------

TITLE: Configuring Supabase Postgres Profiles Table, RLS, Realtime, and Storage
DESCRIPTION: This SQL script defines the `profiles` table with user-related information, establishes Row Level Security (RLS) policies to control data access based on user authentication, sets up Realtime capabilities for the `profiles` table, and initializes a storage bucket for user avatars. RLS policies ensure users can only view public profiles, insert their own, and update their own profile data.
SOURCE: https://github.com/supabase/supabase/blob/master/examples/user-management/solid-user-management/README.md#_snippet_3

LANGUAGE: sql
CODE:
```
-- Create a table for Public Profiles
create table
	profiles (
		id uuid references auth.users not null,
		updated_at timestamp
		with
			time zone,
			username text unique,
			avatar_url text,
			website text,
			primary key (id),
			unique (username),
			constraint username_length check (char_length(username) >= 3)
	);

alter table
	profiles enable row level security;

create policy "Public profiles are viewable by everyone." on profiles for
select
	using (true);

create policy "Users can insert their own profile." on profiles for insert
with
	check ((select auth.uid()) = id);

create policy "Users can update own profile." on profiles for
update
	using ((select auth.uid()) = id);

-- Set up Realtime!
begin;

drop
	publication if exists supabase_realtime;

create publication supabase_realtime;

commit;

alter
	publication supabase_realtime add table profiles;

-- Set up Storage!
insert into
	storage.buckets (id, name)
values
	('avatars', 'avatars');

create policy "Avatar images are publicly accessible." on storage.objects for
select
	using (bucket_id = 'avatars');

create policy "Anyone can upload an avatar." on storage.objects for insert
with
	check (bucket_id = 'avatars');
```