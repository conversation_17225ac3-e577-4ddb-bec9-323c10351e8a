'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';

export default function DebugAuthPage() {
  const [authState, setAuthState] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();
    
    const checkAuth = async () => {
      try {
        // Verificar sesión actual
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        // Verificar usuario actual
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        setAuthState({
          session: {
            exists: !!session,
            userId: session?.user?.id,
            email: session?.user?.email,
            expiresAt: session?.expires_at,
            error: sessionError?.message
          },
          user: {
            exists: !!user,
            userId: user?.id,
            email: user?.email,
            emailConfirmed: user?.email_confirmed_at,
            error: userError?.message
          },
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        setAuthState({
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Escuchar cambios de autenticación
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state change:', event, session);
      checkAuth();
    });

    return () => subscription.unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Verificando estado de autenticación...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Debug - Estado de Autenticación</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(authState, null, 2)}
          </pre>
        </div>

        <div className="mt-6 space-y-4">
          <button
            onClick={() => window.location.href = '/app'}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Ir a /app
          </button>
          
          <button
            onClick={() => window.location.href = '/auth/callback'}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-4"
          >
            Ir a /auth/callback
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 ml-4"
          >
            Recargar página
          </button>
        </div>
      </div>
    </div>
  );
}
