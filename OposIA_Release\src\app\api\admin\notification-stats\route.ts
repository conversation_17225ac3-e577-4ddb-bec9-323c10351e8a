// src/app/api/admin/notification-stats/route.ts
// Endpoint para administradores para ver estadísticas de notificaciones

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { EmailNotificationService } from '@/lib/services/email/emailNotificationService';

// Lista de emails de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Agregar más emails de administradores aquí
];

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación de administrador
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
      return NextResponse.json({
        error: 'Acceso denegado. Solo administradores pueden ver estas estadísticas.'
      }, { status: 403 });
    }

    // Obtener parámetros de consulta
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Si no se especifican fechas, usar últimos 30 días
    const defaultStartDate = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const defaultEndDate = endDate || new Date().toISOString();

    console.log(`📊 Obteniendo estadísticas de notificaciones desde ${defaultStartDate} hasta ${defaultEndDate}`);

    // Obtener estadísticas
    const stats = await EmailNotificationService.getNotificationStats(
      defaultStartDate,
      defaultEndDate
    );

    // Calcular métricas adicionales
    const totalSent = stats.byStatus.sent || 0;
    const totalFailed = stats.byStatus.failed || 0;
    const successRate = stats.total > 0 ? ((totalSent / stats.total) * 100).toFixed(2) : '0';

    // Agrupar notificaciones recientes por día
    const notificationsByDay = stats.recentNotifications.reduce((acc, notification) => {
      const day = notification.sent_at.split('T')[0];
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        period: {
          startDate: defaultStartDate,
          endDate: defaultEndDate
        },
        summary: {
          total: stats.total,
          sent: totalSent,
          failed: totalFailed,
          successRate: `${successRate}%`
        },
        byType: stats.byType,
        byStatus: stats.byStatus,
        byDay: notificationsByDay,
        recentNotifications: stats.recentNotifications.map(notification => ({
          id: notification.id,
          type: notification.type,
          recipient: notification.recipient_email,
          subject: notification.subject,
          status: notification.status,
          sentAt: notification.sent_at,
          userId: notification.user_id,
          metadata: notification.metadata
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error obteniendo estadísticas de notificaciones:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
