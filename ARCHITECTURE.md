# Arquitectura de OposIA

Este documento describe la arquitectura técnica de la plataforma OposIA, incluyendo decisiones de diseño, patrones utilizados y estructura del sistema.

## 🏗️ Visión General de la Arquitectura

OposIA sigue una arquitectura de **aplicación web full-stack** basada en Next.js con el patrón **App Router**, utilizando **Supabase** como backend-as-a-service y **Stripe** para procesamiento de pagos.

### Principios Arquitectónicos

1. **Separación de Responsabilidades**: Cada capa tiene responsabilidades bien definidas
2. **Modularidad**: Funcionalidades organizadas en features independientes
3. **Escalabilidad**: Diseño que permite crecimiento horizontal y vertical
4. **Mantenibilidad**: Código limpio, tipado y bien documentado
5. **Seguridad**: Autenticación robusta y validación de permisos
6. **Performance**: Optimización de carga y renderizado

## 🎯 Arquitectura de Alto Nivel

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Servicios     │
│   (Next.js)     │◄──►│   (API Routes)  │◄──►│   Externos      │
│                 │    │                 │    │                 │
│ • React 18      │    │ • Next.js API   │    │ • Supabase      │
│ • TypeScript    │    │ • Middleware    │    │ • Stripe        │
│ • Tailwind CSS  │    │ • Validación    │    │ • OpenAI        │
│ • SWC           │    │ • Rate Limiting │    │ • Vercel        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Estructura de Directorios

### Frontend (`src/`)

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Grupo de rutas de autenticación
│   ├── dashboard/         # Panel principal de usuario
│   ├── api/               # API Routes de Next.js
│   ├── layout.tsx         # Layout raíz
│   ├── page.tsx           # Página de inicio
│   └── globals.css        # Estilos globales
├── components/            # Componentes reutilizables
│   ├── ui/               # Componentes base del sistema de diseño
│   └── layout/           # Componentes de layout (header, sidebar, etc.)
├── features/             # Funcionalidades específicas (feature-based)
├── lib/                  # Servicios y utilidades centralizadas
├── types/                # Definiciones de TypeScript
├── contexts/             # Contextos de React
├── hooks/                # Hooks personalizados
└── __tests__/            # Tests y configuración de testing
```

### Features (`src/features/`)

Cada feature sigue la estructura:

```
features/
├── auth/
│   ├── components/       # Componentes específicos de autenticación
│   ├── hooks/           # Hooks de autenticación
│   ├── services/        # Servicios de autenticación
│   ├── types/           # Tipos específicos
│   └── __tests__/       # Tests de la feature
├── documents/           # Gestión de documentos
├── tests/              # Generación de tests
├── flashcards/         # Sistema de flashcards
├── chat/               # Chat con IA
├── study-plan/         # Planificación de estudio
├── mind-maps/          # Mapas mentales
└── payments/           # Sistema de pagos
```

## 🔧 Capas de la Aplicación

### 1. Capa de Presentación (UI Layer)

**Responsabilidades:**
- Renderizado de componentes React
- Gestión de estado local de UI
- Interacción con el usuario
- Navegación entre páginas

**Tecnologías:**
- React 18 con Server Components
- Next.js App Router
- Tailwind CSS para estilos
- TypeScript para tipado

**Patrones:**
- **Compound Components**: Para componentes complejos
- **Render Props**: Para lógica reutilizable
- **Custom Hooks**: Para estado y efectos
- **Context API**: Para estado global

### 2. Capa de Lógica de Negocio (Business Logic Layer)

**Responsabilidades:**
- Validación de reglas de negocio
- Gestión de permisos por plan
- Cálculo de límites y uso de tokens
- Orquestación de servicios

**Ubicación:** `src/lib/services/`

**Servicios Principales:**
- `PlanValidationService`: Validación de acceso por plan
- `LimitHandler`: Gestión de límites de uso
- `PermissionService`: Control de permisos
- `TokenCalculator`: Cálculo de consumo de tokens

### 3. Capa de Datos (Data Layer)

**Responsabilidades:**
- Comunicación con Supabase
- Gestión de autenticación
- Operaciones CRUD
- Manejo de transacciones

**Ubicación:** `src/lib/supabase/`

**Servicios:**
- `SupabaseAdminService`: Operaciones administrativas
- `UserManagementService`: Gestión de usuarios
- `DocumentService`: Gestión de documentos
- `TestService`: Gestión de tests y resultados

### 4. Capa de Integración (Integration Layer)

**Responsabilidades:**
- Integración con servicios externos
- Webhooks de Stripe
- APIs de IA (OpenAI)
- Procesamiento de archivos

**Ubicación:** `src/lib/integrations/`

## 🔐 Arquitectura de Seguridad

### Autenticación y Autorización

```
Usuario → Supabase Auth → JWT Token → RLS Policies → Base de Datos
```

1. **Autenticación**: Supabase Auth con JWT
2. **Autorización**: Row Level Security (RLS) en PostgreSQL
3. **Validación de Permisos**: Middleware personalizado
4. **Rate Limiting**: Por usuario y endpoint

### Flujo de Seguridad

```typescript
// Ejemplo de validación de permisos
async function validateAccess(userId: string, feature: string, tokenCost: number) {
  // 1. Verificar autenticación
  const user = await getUser(userId);
  if (!user) throw new Error('Unauthorized');
  
  // 2. Verificar plan y permisos
  const hasPermission = await checkFeaturePermission(user.plan, feature);
  if (!hasPermission) throw new Error('Feature not available in your plan');
  
  // 3. Verificar límites
  const withinLimits = await checkTokenLimits(userId, tokenCost);
  if (!withinLimits) throw new Error('Token limit exceeded');
  
  return true;
}
```

## 💾 Arquitectura de Datos

### Modelo de Datos Principal

```sql
-- Usuarios y perfiles
users (Supabase Auth)
user_profiles (
  user_id UUID PRIMARY KEY,
  subscription_plan TEXT,
  payment_verified BOOLEAN,
  monthly_token_limit INTEGER,
  current_month_tokens INTEGER,
  current_month DATE
)

-- Documentos
documents (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(user_id),
  filename TEXT,
  content_type TEXT,
  file_size INTEGER,
  processed_content TEXT,
  created_at TIMESTAMP
)

-- Tests y preguntas
tests (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(user_id),
  document_id UUID REFERENCES documents(id),
  title TEXT,
  difficulty TEXT,
  created_at TIMESTAMP
)

test_questions (
  id UUID PRIMARY KEY,
  test_id UUID REFERENCES tests(id),
  question TEXT,
  options JSONB,
  correct_answer TEXT,
  explanation TEXT
)

-- Flashcards
flashcards (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(user_id),
  document_id UUID REFERENCES documents(id),
  front TEXT,
  back TEXT,
  difficulty TEXT,
  created_at TIMESTAMP
)

-- Planes de estudio
study_plans (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(user_id),
  title TEXT,
  description TEXT,
  start_date DATE,
  end_date DATE,
  created_at TIMESTAMP
)

study_plan_tasks (
  id UUID PRIMARY KEY,
  study_plan_id UUID REFERENCES study_plans(id),
  title TEXT,
  description TEXT,
  due_date DATE,
  completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP
)
```

### Políticas RLS

```sql
-- Ejemplo de política RLS para documentos
CREATE POLICY "Users can only access their own documents" 
ON documents FOR ALL 
USING (auth.uid() = user_id);

-- Política para perfiles de usuario
CREATE POLICY "Users can only access their own profile" 
ON user_profiles FOR ALL 
USING (auth.uid() = user_id);
```

## 🔄 Flujos de Datos Principales

### 1. Flujo de Generación de Tests

```
Usuario sube documento → Validación de permisos → Procesamiento de texto → 
Llamada a IA → Generación de preguntas → Almacenamiento → Actualización de tokens
```

### 2. Flujo de Pago

```
Usuario selecciona plan → Stripe Checkout → Webhook de confirmación → 
Actualización de perfil → Verificación de pago → Acceso a funciones premium
```

### 3. Flujo de Autenticación

```
Login/Registro → Supabase Auth → JWT Token → Carga de perfil → 
Validación de plan → Redirección a dashboard
```

## 🚀 Patrones de Diseño Utilizados

### 1. Repository Pattern
```typescript
interface DocumentRepository {
  create(document: CreateDocumentDto): Promise<Document>;
  findById(id: string): Promise<Document | null>;
  findByUserId(userId: string): Promise<Document[]>;
  update(id: string, data: UpdateDocumentDto): Promise<Document>;
  delete(id: string): Promise<void>;
}
```

### 2. Service Layer Pattern
```typescript
class DocumentService {
  constructor(
    private documentRepo: DocumentRepository,
    private permissionService: PermissionService,
    private limitHandler: LimitHandler
  ) {}

  async uploadDocument(userId: string, file: File): Promise<Document> {
    await this.permissionService.validateAccess(userId, 'document_upload');
    await this.limitHandler.checkLimits(userId, 'document_upload');
    
    return this.documentRepo.create({
      userId,
      filename: file.name,
      content: await this.processFile(file)
    });
  }
}
```

### 3. Factory Pattern
```typescript
class ServiceFactory {
  static createDocumentService(): DocumentService {
    return new DocumentService(
      new SupabaseDocumentRepository(),
      new PermissionService(),
      new LimitHandler()
    );
  }
}
```

### 4. Strategy Pattern
```typescript
interface PaymentStrategy {
  processPayment(amount: number, metadata: any): Promise<PaymentResult>;
}

class StripePaymentStrategy implements PaymentStrategy {
  async processPayment(amount: number, metadata: any): Promise<PaymentResult> {
    // Implementación específica de Stripe
  }
}
```

## 📊 Monitoreo y Observabilidad

### Métricas Clave
- Tiempo de respuesta de APIs
- Uso de tokens por usuario
- Tasa de conversión de pagos
- Errores de aplicación
- Uso de funcionalidades

### Logging
```typescript
// Ejemplo de logging estructurado
logger.info('Document uploaded', {
  userId,
  documentId,
  fileSize,
  processingTime,
  tokensUsed
});
```

## 🔧 Configuración y Deployment

### Variables de Entorno
```env
# Base de datos
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Pagos
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=

# IA
OPENAI_API_KEY=

# App
NEXT_PUBLIC_APP_URL=
```

### Pipeline de Deployment
1. **Desarrollo**: `npm run dev`
2. **Testing**: `npm test`
3. **Build**: `npm run build`
4. **Deploy**: Vercel automático en push a main

## 🔮 Consideraciones Futuras

### Escalabilidad
- Implementar cache con Redis
- Microservicios para funciones específicas
- CDN para archivos estáticos
- Database sharding por usuario

### Nuevas Funcionalidades
- Colaboración en tiempo real
- Integración con LMS externos
- App móvil nativa
- Analytics avanzados

---

**Versión**: 1.0  
**Última actualización**: Enero 2025
