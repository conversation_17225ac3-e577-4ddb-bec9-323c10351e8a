═══════════════════════════════════════════════════════════════════════════════
                                   OposiAI
                        Sistema de Preparación de Oposiciones
                              con Inteligencia Artificial
═══════════════════════════════════════════════════════════════════════════════

🚀 INSTRUCCIONES DE INICIO RÁPIDO

Para iniciar la aplicación OposiAI, siga estos pasos:

┌─────────────────────────────────────────────────────────────────────────────┐
│                              OPCIÓN 1: AUTOMÁTICO                          │
│                                (RECOMENDADO)                               │
└─────────────────────────────────────────────────────────────────────────────┘

1. Hacer DOBLE CLIC en el archivo:
   
   📁 INICIAR_APLICACION.bat  (Windows)
   📁 iniciar_aplicacion.sh   (Mac/Linux)

2. Seguir las instrucciones que aparecen en pantalla
   La primera vez que se inicie tardará un tiempo ya que tiene que instalar todas las dependencias.

3. La aplicación se abrirá automáticamente en:
   🌐 http://localhost:3000

4. No cerrar la terminal o no podrá acceder a la aplicación


┌─────────────────────────────────────────────────────────────────────────────┐
│                              OPCIÓN 2: MANUAL                              │
│                          (Para usuarios técnicos)                          │
└─────────────────────────────────────────────────────────────────────────────┘

1. Abrir terminal/símbolo del sistema en esta carpeta

2. Ejecutar los siguientes comandos:
   
   npm install
   npm run dev

3. Abrir navegador en: http://localhost:3000

┌─────────────────────────────────────────────────────────────────────────────┐
│                                REQUISITOS                                  │
└─────────────────────────────────────────────────────────────────────────────┘

✅ Node.js versión 18 o superior
   📥 Descargar desde: https://nodejs.org/

✅ Navegador web moderno (Chrome, Firefox, Safari, Edge)

✅ Conexión a internet (para funciones de IA)


┌─────────────────────────────────────────────────────────────────────────────┐
│                              INFORMACIÓN                                   │
└─────────────────────────────────────────────────────────────────────────────┘

📄 Documentación completa: README.md
📋 Instrucciones de registro PI: INSTRUCCIONES_REGISTRO_PI.md
🏗️ Arquitectura: Next.js + React + TypeScript + Supabase
🤖 IA: OpenAI GPT-4
💰 Pagos: Stripe
🔒 Seguridad: Autenticación JWT + RLS


