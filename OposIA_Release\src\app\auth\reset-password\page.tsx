// ===== Archivo: src\app\auth\reset-password\page.tsx =====
'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'react-hot-toast';
import { FiLock, FiCheck, FiClock, FiLoader } from 'react-icons/fi';

// Funciones auxiliares estables (fuera del componente)
const isRecoveryUrl = (addDebugInfo?: (message: string) => void) => {
  const urlHash = window.location.hash;
  const urlSearch = window.location.search;

  // Múltiples métodos de detección
  const hasRecoveryType = urlHash.includes('type=recovery') || urlSearch.includes('type=recovery');
  const hasAccessToken = urlHash.includes('access_token=') || urlSearch.includes('access_token=');
  const hasRefreshToken = urlHash.includes('refresh_token=') || urlSearch.includes('refresh_token=');
  const hasCode = urlSearch.includes('code=');
  const comesFromEmail = document.referrer === '' || document.referrer.includes('mail') || document.referrer.includes('gmail');

  if (addDebugInfo) {
    addDebugInfo(`URL Analysis: hash=${urlHash.substring(0, 100)}, search=${urlSearch.substring(0, 100)}`);
    addDebugInfo(`Recovery indicators: type=${hasRecoveryType}, access_token=${hasAccessToken}, refresh_token=${hasRefreshToken}, code=${hasCode}, fromEmail=${comesFromEmail}`);
  }

  return hasRecoveryType || (hasAccessToken && hasRefreshToken) || hasCode;
};

const checkUserMetadata = async (session: { user?: { user_metadata?: any; app_metadata?: any } } | null, addDebugInfo?: (message: string) => void) => {
  try {
    if (!session?.user) return false;

    const userMetadata = session.user.user_metadata || {};
    const appMetadata = session.user.app_metadata || {};

    if (addDebugInfo) {
      addDebugInfo(`User metadata: ${JSON.stringify(userMetadata)}`);
      addDebugInfo(`App metadata: ${JSON.stringify(appMetadata)}`);
    }

    // Verificar si hay indicadores de recovery en los metadatos
    return userMetadata.recovery_flow === true || appMetadata.recovery_flow === true;
  } catch (error) {
    if (addDebugInfo) {
      addDebugInfo(`Error checking metadata: ${(error as Error).message}`);
    } else {
      console.error(`Error checking metadata: ${(error as Error).message}`);
    }
    return false;
  }
};

function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams(); // Necesario para leer 'error' y 'error_description'
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Estados mejorados para mejor control del flujo
  const [isVerifyingToken, setIsVerifyingToken] = useState(true);
  const [hasRecoverySession, setHasRecoverySession] = useState(false);
  const [linkError, setLinkError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  // Función helper para logging (estable)
  const addDebugInfo = useCallback((message: string) => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    setDebugInfo(prev => [...prev, logMessage]);
  }, []);

  // useEffect principal para la lógica de autenticación
  useEffect(() => {
    addDebugInfo("ResetPasswordForm: useEffect AUTH iniciado. URL: " + window.location.href);
    setIsVerifyingToken(true);
    setHasRecoverySession(false);
    setLinkError(null);

    // Verificar errores explícitos en los query parameters (enviados por Supabase si el token/code es inválido)
    const urlErrorParam = searchParams.get('error');
    const errorCodeParam = searchParams.get('error_code');
    const errorDescriptionParam = searchParams.get('error_description');

    if (urlErrorParam || errorCodeParam || errorDescriptionParam) {
      let userFriendlyMessage = decodeURIComponent(errorDescriptionParam || urlErrorParam || 'Error desconocido en el enlace.');
      if (userFriendlyMessage.toLowerCase().includes('link is invalid or has expired') ||
          userFriendlyMessage.toLowerCase().includes('token has expired') ||
          errorCodeParam === 'token_expired_or_invalid') {
        userFriendlyMessage = 'El enlace de recuperación ha expirado o ya fue utilizado. Por favor, solicita un nuevo enlace.';
      }
      addDebugInfo("Error explícito en URL: " + JSON.stringify({ urlErrorParam, errorCodeParam, errorDescriptionParam }));
      setLinkError(userFriendlyMessage);
      setHasRecoverySession(false);
      setIsVerifyingToken(false);
      return;
    }

    const supabase = createClient();

    // Verificación inicial de sesión existente mejorada
    const checkInitialSession = async () => {
      try {
        addDebugInfo("Verificando sesión inicial...");

        // Primero verificar si la URL indica recovery
        const urlIndicatesRecovery = isRecoveryUrl(addDebugInfo);
        addDebugInfo(`URL indica recovery: ${urlIndicatesRecovery}`);

        const { data: { session: currentSession }, error } = await supabase.auth.getSession();

        if (error) {
          addDebugInfo("Error obteniendo sesión inicial: " + error.message);
          // Si hay error pero la URL indica recovery, esperar a los eventos
          if (urlIndicatesRecovery) {
            addDebugInfo("URL indica recovery, esperando eventos de auth...");
            return false; // No establecer error aún
          }
          return false;
        }

        if (currentSession) {
          addDebugInfo("Sesión inicial encontrada. User ID: " + currentSession.user?.id);

          // Múltiples verificaciones para detectar recovery
          const urlBasedRecovery = urlIndicatesRecovery;
          const metadataBasedRecovery = await checkUserMetadata(currentSession, addDebugInfo);

          if (urlBasedRecovery || metadataBasedRecovery) {
            addDebugInfo(`Recovery detectado - URL: ${urlBasedRecovery}, Metadata: ${metadataBasedRecovery}`);
            setHasRecoverySession(true);
            setIsVerifyingToken(false);
            return true;
          } else {
            addDebugInfo("Sesión encontrada pero no es de recovery");
          }
        } else if (urlIndicatesRecovery) {
          addDebugInfo("No hay sesión pero URL indica recovery - esperando procesamiento...");
          // No establecer error, esperar a que Supabase procese la URL
          return false;
        }

        return false;
      } catch (error) {
        addDebugInfo("Error en verificación inicial: " + (error as Error).message);
        return false;
      }
    };

    // Configurar listener de eventos de autenticación
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      addDebugInfo(`Evento onAuthStateChange: ${event}, Session: ${!!session}, User: ${session?.user?.id}`);

      if (event === 'PASSWORD_RECOVERY') {
        if (session) {
          addDebugInfo("Sesión establecida por evento PASSWORD_RECOVERY");
          setHasRecoverySession(true);
          setLinkError(null);
          setIsVerifyingToken(false);
        } else {
          addDebugInfo("Evento PASSWORD_RECOVERY sin sesión - token inválido o expirado");
          setLinkError('El enlace de recuperación parece ser inválido o ha expirado.');
          setHasRecoverySession(false);
          setIsVerifyingToken(false);
        }
      } else if (event === 'INITIAL_SESSION') {
        if (session) {
          addDebugInfo("Evento INITIAL_SESSION con sesión - verificando si es recovery");

          // Usar métodos mejorados de detección
          const urlBasedRecovery = isRecoveryUrl(addDebugInfo);
          const metadataBasedRecovery = await checkUserMetadata(session, addDebugInfo);

          if (urlBasedRecovery || metadataBasedRecovery) {
            addDebugInfo(`Recovery confirmado en INITIAL_SESSION - URL: ${urlBasedRecovery}, Metadata: ${metadataBasedRecovery}`);
            setHasRecoverySession(true);
            setIsVerifyingToken(false);
          } else {
            addDebugInfo("Sesión normal en INITIAL_SESSION - no es recovery");
            setIsVerifyingToken(false);
          }
        } else {
          addDebugInfo("Evento INITIAL_SESSION sin sesión");
          // Si no hay sesión pero la URL indica recovery, seguir esperando
          if (isRecoveryUrl(addDebugInfo)) {
            addDebugInfo("Sin sesión pero URL indica recovery - continuando verificación...");
          } else {
            setIsVerifyingToken(false);
          }
        }
      } else if (event === 'SIGNED_IN') {
        addDebugInfo("Evento SIGNED_IN");
        // Verificar si es una sesión de recovery
        if (session) {
          const urlBasedRecovery = isRecoveryUrl(addDebugInfo);
          const metadataBasedRecovery = await checkUserMetadata(session, addDebugInfo);

          if (urlBasedRecovery || metadataBasedRecovery) {
            addDebugInfo(`Recovery detectado en SIGNED_IN - URL: ${urlBasedRecovery}, Metadata: ${metadataBasedRecovery}`);
            setHasRecoverySession(true);
          }
        }
        setIsVerifyingToken(false);
      } else if (event === 'SIGNED_OUT') {
        addDebugInfo("Evento SIGNED_OUT");
        setHasRecoverySession(false);
        setIsVerifyingToken(false);
      }
    });

    // Ejecutar verificación inicial
    checkInitialSession();

    return () => {
      authListener?.subscription.unsubscribe();
      addDebugInfo("Listener de autenticación limpiado");
    };
  }, [searchParams, addDebugInfo]); // Dependencias corregidas

  // useEffect separado para el timeout de seguridad
  useEffect(() => {
    // Solo ejecutar si estamos verificando y no hay un error de enlace ya detectado
    if (!isVerifyingToken || linkError) {
      return;
    }

    addDebugInfo("ResetPasswordForm: useEffect TIMEOUT iniciado.");
    const timeoutId = setTimeout(async () => {
      // Volver a verificar el estado actual en el momento del timeout
      if (isVerifyingToken && !hasRecoverySession) {
        const supabase = createClient();
        const { data: { session: finalCheckSession } } = await supabase.auth.getSession();

        if (!finalCheckSession?.user || !(await checkUserMetadata(finalCheckSession, addDebugInfo)) && !isRecoveryUrl(addDebugInfo)) {
          addDebugInfo("❌ TIMEOUT (5 min): No se estableció sesión de recuperación válida.");
          setLinkError("No se pudo establecer una sesión para cambiar la contraseña. El enlace podría ser inválido o haber expirado.");
          setHasRecoverySession(false);
        }
        // En cualquier caso, después del timeout, ya no estamos "verificando" de esta manera.
        setIsVerifyingToken(false);
      }
    }, 300000); // 5 minutos

    return () => {
      clearTimeout(timeoutId);
      addDebugInfo("Timeout de seguridad limpiado");
    };
  }, [isVerifyingToken, hasRecoverySession, linkError, addDebugInfo]);

  // ... (handleSubmit y JSX igual que en la versión anterior)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!hasRecoverySession) {
      setError('No se ha establecido una sesión válida para cambiar la contraseña. Por favor, asegúrate de usar el enlace de tu email.');
      toast.error('Error de sesión. Intenta usar el enlace de tu email de nuevo.');
      return;
    }

    if (password.length < 6) {
      setError('La nueva contraseña debe tener al menos 6 caracteres.');
      return;
    }
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return;
    }

    setLoading(true); 
    const supabase = createClient();
    
    const { data: userObjectBeforeUpdate, error: getUserError } = await supabase.auth.getUser();
    if (getUserError || !userObjectBeforeUpdate.user) {
        setError('No se pudo verificar la sesión actual antes de actualizar. Intenta de nuevo.');
        setLoading(false);
        return;
    }
    
    const isInitialSetup = userObjectBeforeUpdate.user.user_metadata?.requires_terms_acceptance_and_final_password_setup === true ||
                           userObjectBeforeUpdate.user.user_metadata?.requires_initial_password_change === true;

    const metadataToUpdate: any = {};
    if (isInitialSetup) {
        metadataToUpdate.requires_terms_acceptance_and_final_password_setup = false;
        metadataToUpdate.temporary_password_set = false;
        metadataToUpdate.requires_initial_password_change = false; 
    }

    const { error: updateError } = await supabase.auth.updateUser({
      password,
      data: metadataToUpdate 
    });
    setLoading(false); 

    if (updateError) {
      console.error("ResetPasswordForm: Error al actualizar contraseña:", updateError);
      setError(updateError.message === "Auth session missing!" ? "Error de sesión: Tu sesión ha expirado o es inválida. Por favor, usa el enlace de tu email de nuevo." : updateError.message);
      toast.error(updateError.message === "Auth session missing!" ? "Error de sesión. Usa el enlace de tu email." : 'Error al actualizar la contraseña.');
    } else {
      toast.success('¡Contraseña actualizada exitosamente!');
      addDebugInfo("Contraseña actualizada exitosamente. Redirigiendo a login...");

      // Pequeña pausa para que el usuario vea el mensaje de éxito
      setTimeout(() => {
        router.push('/login');
      }, 1500);
    }
  };

  // Renderizado condicional mejorado
  if (isVerifyingToken) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
        <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />
        <h2 className="text-xl font-semibold text-gray-800">Verificando enlace...</h2>
        <p className="text-gray-600 mt-2">Esto puede tardar unos segundos.</p>
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 text-xs text-gray-500">
            <summary>Debug Info</summary>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-left max-w-md overflow-auto">
              {debugInfo.join('\n')}
            </pre>
          </details>
        )}
      </div>
    );
  }

  if (linkError) {
    return (
      <div className="min-h-screen bg-red-50 flex flex-col justify-center items-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <FiClock className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Enlace Inválido o Expirado</h2>
          <p className="text-gray-600 mb-6">{linkError}</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Volver a Inicio de Sesión
          </button>
        </div>
      </div>
    );
  }
  
  if (!hasRecoverySession && !isVerifyingToken) {
    return (
       <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center p-4">
        <FiLock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-700">Acceso no Autorizado</h2>
        <p className="text-gray-500 mt-2 mb-6 max-w-md text-center">
            Esta página es para establecer o restablecer tu contraseña usando un enlace seguro enviado a tu email.
            Si necesitas restablecer tu contraseña, solicítalo desde la página de inicio de sesión.
        </p>
         <button
            onClick={() => router.push('/login')}
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Ir a Inicio de Sesión
          </button>
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4 text-xs text-gray-500">
              <summary>Debug Info</summary>
              <pre className="mt-2 p-2 bg-gray-100 rounded text-left max-w-md overflow-auto">
                {debugInfo.join('\n')}
              </pre>
            </details>
          )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* ... JSX del formulario ... */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <FiLock className="w-12 h-12 text-blue-600" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Crea tu Nueva Contraseña
        </h2>
        {/* ... */}
      </div>
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* ... campos ... */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">Nueva Contraseña</label>
              <div className="mt-1">
                <input id="password" name="password" type="password" required value={password} onChange={(e) => setPassword(e.target.value)} className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Mínimo 6 caracteres"/>
              </div>
            </div>
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">Confirmar Nueva Contraseña</label>
              <div className="mt-1">
                <input id="confirmPassword" name="confirmPassword" type="password" required value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Repite la contraseña"/>
              </div>
            </div>
            {error && (
              <div className="text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200">
                {error}
              </div>
            )}
            <div>
              <button type="submit" disabled={loading || !hasRecoverySession} className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                {loading ? ( <><FiLoader className="animate-spin h-4 w-4 mr-2"/> Actualizando...</> ) : ( <><FiCheck className="h-4 w-4 mr-2"/> Establecer Contraseña</> )}
              </button>
            </div>
          </form>
          {/* ... consejos ... */}
        </div>
      </div>
    </div>
  );
} // Cierre de ResetPasswordForm

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={ // ... JSX del Suspense fallback ...
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
            <FiLoader className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />
            <h2 className="text-xl font-semibold text-gray-800">Cargando...</h2>
        </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  );
}