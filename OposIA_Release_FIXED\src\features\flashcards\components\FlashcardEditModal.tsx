import React, { useState, useEffect } from 'react';
import { FiX, FiSave, FiLoader } from 'react-icons/fi';
import { FlashcardConProgreso, actualizarFlashcard } from '@/lib/supabase';
import toast from 'react-hot-toast';

interface FlashcardEditModalProps {
  flashcard: FlashcardConProgreso;
  isOpen: boolean;
  onClose: () => void;
  onSave: (flashcard: FlashcardConProgreso) => void;
}

export default function FlashcardEditModal({ 
  flashcard, 
  isOpen, 
  onClose, 
  onSave 
}: FlashcardEditModalProps) {
  const [pregunta, setPregunta] = useState('');
  const [respuesta, setRespuesta] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Actualizar los campos cuando cambie la flashcard
  useEffect(() => {
    if (flashcard) {
      setPregunta(flashcard.pregunta);
      setRespuesta(flashcard.respuesta);
    }
  }, [flashcard]);

  const handleSave = async () => {
    if (!pregunta.trim() || !respuesta.trim()) {
      toast.error('La pregunta y respuesta no pueden estar vacías');
      return;
    }

    setIsLoading(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Guardando cambios...');
      
      const success = await actualizarFlashcard(flashcard.id, pregunta.trim(), respuesta.trim());
      
      if (success) {
        toast.success('Flashcard actualizada exitosamente', { id: loadingToastId });
        
        // Crear la flashcard actualizada para pasar al componente padre
        const flashcardActualizada: FlashcardConProgreso = {
          ...flashcard,
          pregunta: pregunta.trim(),
          respuesta: respuesta.trim()
        };
        
        onSave(flashcardActualizada);
        onClose();
      } else {
        toast.error('Error al actualizar la flashcard', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al actualizar flashcard:', error);
      toast.error('Error al actualizar la flashcard', { id: loadingToastId });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Restaurar valores originales
    setPregunta(flashcard.pregunta);
    setRespuesta(flashcard.respuesta);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Editar Flashcard
          </h3>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Pregunta */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pregunta
            </label>
            <textarea
              value={pregunta}
              onChange={(e) => setPregunta(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={4}
              placeholder="Escribe la pregunta aquí..."
              disabled={isLoading}
            />
          </div>

          {/* Respuesta */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Respuesta
            </label>
            <textarea
              value={respuesta}
              onChange={(e) => setRespuesta(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={6}
              placeholder="Escribe la respuesta aquí..."
              disabled={isLoading}
            />
          </div>

          {/* Estado de la flashcard */}
          {flashcard.progreso?.estado && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Estado actual
              </h4>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    flashcard.progreso.estado === 'nuevo'
                      ? 'bg-blue-100 text-blue-800'
                      : flashcard.progreso.estado === 'aprendiendo'
                      ? 'bg-yellow-100 text-yellow-800'
                      : flashcard.progreso.estado === 'repasando'
                      ? 'bg-orange-100 text-orange-800'
                      : 'bg-green-100 text-green-800'
                  }`}
                >
                  {flashcard.progreso.estado}
                </span>
                <span>Repeticiones: {flashcard.progreso.repeticiones}</span>
                <span>Intervalo: {flashcard.progreso.intervalo} días</span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={handleCancel}
            className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading || !pregunta.trim() || !respuesta.trim()}
            className="px-4 py-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading ? (
              <>
                <FiLoader className="animate-spin mr-2" />
                Guardando...
              </>
            ) : (
              <>
                <FiSave className="mr-2" />
                Guardar cambios
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
