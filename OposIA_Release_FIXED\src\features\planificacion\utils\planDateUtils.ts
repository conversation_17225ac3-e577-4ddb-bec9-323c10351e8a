/**
 * Utilidades para procesar fechas del plan de estudios y convertirlas
 * en datos utilizables por el calendario
 */

import { 
  PlanEstudiosEstructurado, 
  SemanaPlan, 
  DiaPlan, 
  TareaPlan 
} from '../services/planGeneratorService';
import { ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';
import {
  DiaCalendario,
  TareaDelDia,
  DatosPlanCalendario,
  EstadoDiaCalendario,
  PlanProcesado,
  ValidacionFechasPlan,
  OpcionesProcesamiento
} from '../types/calendarTypes';
import {
  parseDate,
  formatDate,
  calcularFechaDia,
  esMismoDia,
  esHoy
} from '@/lib/utils/dateUtils';

/**
 * Procesa un plan de estudios completo y genera los datos para el calendario
 */
export function procesarPlanParaCalendario(
  plan: PlanEstudiosEstructurado,
  progresoPlan: ProgresoPlanEstudios[],
  opciones: OpcionesProcesamiento = {}
): PlanProcesado {
  const errores: string[] = [];
  const mapaDias = new Map<string, DiaCalendario>();
  
  // Configuración por defecto
  const config = {
    incluirDiasSinTareas: true,
    calcularEstadisticas: true,
    validarFechas: true,
    ordenarTareasPorTipo: false,
    ...opciones
  };

  // Validar el plan
  if (!plan || !plan.semanas || !Array.isArray(plan.semanas)) {
    errores.push('Plan de estudios inválido o sin semanas');
    return crearResultadoVacio(errores);
  }

  // Validar fechas si está habilitado
  if (config.validarFechas) {
    const validacion = validarFechasPlan(plan);
    if (!validacion.esValido) {
      errores.push(...validacion.errores);
    }
  }

  let fechaInicio: Date | null = null;
  let fechaFin: Date | null = null;
  let totalTareas = 0;

  // Procesar cada semana del plan
  for (const semana of plan.semanas) {
    if (!semana || typeof semana.numero !== 'number') {
      errores.push(`Semana inválida encontrada`);
      continue;
    }

    const fechaInicioSemana = parseDate(semana.fechaInicio);
    const fechaFinSemana = parseDate(semana.fechaFin);

    if (!fechaInicioSemana || !fechaFinSemana) {
      errores.push(`Fechas inválidas en semana ${semana.numero}`);
      continue;
    }

    // Actualizar rango de fechas del plan
    if (!fechaInicio || fechaInicioSemana < fechaInicio) {
      fechaInicio = fechaInicioSemana;
    }
    if (!fechaFin || fechaFinSemana > fechaFin) {
      fechaFin = fechaFinSemana;
    }

    // Procesar cada día de la semana
    if (semana.dias && Array.isArray(semana.dias)) {
      for (const dia of semana.dias) {
        const resultadoDia = procesarDiaPlan(
          dia, 
          semana, 
          progresoPlan, 
          config
        );
        
        if (resultadoDia.error) {
          errores.push(resultadoDia.error);
          continue;
        }

        if (resultadoDia.diaCalendario) {
          const fechaKey = formatDate(resultadoDia.diaCalendario.fecha);
          mapaDias.set(fechaKey, resultadoDia.diaCalendario);
          totalTareas += resultadoDia.diaCalendario.totalTareas;
        }
      }
    }
  }

  // Crear datos del plan
  const datosPlan: DatosPlanCalendario = {
    fechaInicio: fechaInicio || new Date(),
    fechaFin: fechaFin || new Date(),
    totalSemanas: plan.semanas.length,
    mapaDias,
    rangoFechas: calcularRangoFechas(fechaInicio, fechaFin)
  };

  // Calcular estadísticas
  const estadisticas = config.calcularEstadisticas 
    ? calcularEstadisticasPlan(mapaDias, progresoPlan)
    : {
        totalTareas: 0,
        tareasCompletadas: 0,
        porcentajeGeneral: 0,
        diasConTareas: 0,
        diasCompletados: 0
      };

  return {
    datosPlan,
    estadisticas,
    errores
  };
}

/**
 * Procesa un día específico del plan
 */
function procesarDiaPlan(
  dia: DiaPlan,
  semana: SemanaPlan,
  progresoPlan: ProgresoPlanEstudios[],
  config: OpcionesProcesamiento
): { diaCalendario?: DiaCalendario; error?: string } {
  
  if (!dia || !dia.dia) {
    return { error: `Día inválido en semana ${semana.numero}` };
  }

  // Calcular la fecha específica del día
  const fechaDia = calcularFechaDia(semana.fechaInicio, dia.dia);
  if (!fechaDia) {
    return { error: `No se pudo calcular la fecha para ${dia.dia} en semana ${semana.numero}` };
  }

  // Procesar tareas del día
  const tareas: TareaDelDia[] = [];
  if (dia.tareas && Array.isArray(dia.tareas)) {
    for (const tarea of dia.tareas) {
      if (!tarea || !tarea.titulo) continue;

      const progresoTarea = progresoPlan.find(p =>
        p.semana_numero === semana.numero &&
        p.dia_nombre === dia.dia &&
        p.tarea_titulo === tarea.titulo
      );

      tareas.push({
        tarea,
        semanaNumero: semana.numero,
        diaNombre: dia.dia,
        completada: progresoTarea?.completado || false,
        fechaCompletado: progresoTarea?.fecha_completado
      });
    }
  }

  // Ordenar tareas si está configurado
  if (config.ordenarTareasPorTipo) {
    tareas.sort((a, b) => {
      const orden = { 'estudio': 0, 'repaso': 1, 'practica': 2, 'evaluacion': 3 };
      return (orden[a.tarea.tipo] || 99) - (orden[b.tarea.tipo] || 99);
    });
  }

  // Calcular estadísticas del día
  const totalTareas = tareas.length;
  const tareasCompletadas = tareas.filter(t => t.completada).length;
  const porcentajeCompletado = totalTareas > 0 ? (tareasCompletadas / totalTareas) * 100 : 0;

  // Determinar estado del día
  const estado = determinarEstadoDia(fechaDia, totalTareas, tareasCompletadas);

  const diaCalendario: DiaCalendario = {
    fecha: fechaDia,
    dia: fechaDia.getDate(),
    estaEnMesActual: true, // Se ajustará en el calendario
    esHoy: esHoy(fechaDia),
    estado,
    tareas,
    totalTareas,
    tareasCompletadas,
    porcentajeCompletado
  };

  return { diaCalendario };
}

/**
 * Determina el estado de un día basado en sus tareas
 */
function determinarEstadoDia(
  fecha: Date, 
  totalTareas: number, 
  tareasCompletadas: number
): EstadoDiaCalendario {
  
  if (esHoy(fecha)) {
    return 'hoy';
  }

  if (totalTareas === 0) {
    return 'normal';
  }

  if (tareasCompletadas === 0) {
    return 'con-tareas';
  }

  if (tareasCompletadas === totalTareas) {
    return 'completado';
  }

  return 'parcial';
}

/**
 * Calcula el rango de fechas del plan
 */
function calcularRangoFechas(fechaInicio: Date | null, fechaFin: Date | null) {
  if (!fechaInicio || !fechaFin) {
    const hoy = new Date();
    return {
      minYear: hoy.getFullYear(),
      maxYear: hoy.getFullYear(),
      minMonth: hoy.getMonth(),
      maxMonth: hoy.getMonth()
    };
  }

  return {
    minYear: fechaInicio.getFullYear(),
    maxYear: fechaFin.getFullYear(),
    minMonth: fechaInicio.getMonth(),
    maxMonth: fechaFin.getMonth()
  };
}

/**
 * Calcula estadísticas generales del plan
 */
function calcularEstadisticasPlan(
  mapaDias: Map<string, DiaCalendario>,
  progresoPlan: ProgresoPlanEstudios[]
) {
  let totalTareas = 0;
  let tareasCompletadas = 0;
  let diasConTareas = 0;
  let diasCompletados = 0;

  // Convertir a array para compatibilidad con TypeScript target
  const diasArray = Array.from(mapaDias.values());

  for (const dia of diasArray) {
    if (dia.totalTareas > 0) {
      diasConTareas++;
      totalTareas += dia.totalTareas;
      tareasCompletadas += dia.tareasCompletadas;

      if (dia.tareasCompletadas === dia.totalTareas) {
        diasCompletados++;
      }
    }
  }

  return {
    totalTareas,
    tareasCompletadas,
    porcentajeGeneral: totalTareas > 0 ? (tareasCompletadas / totalTareas) * 100 : 0,
    diasConTareas,
    diasCompletados
  };
}

/**
 * Valida las fechas del plan de estudios
 */
export function validarFechasPlan(plan: PlanEstudiosEstructurado): ValidacionFechasPlan {
  const errores: string[] = [];
  const advertencias: string[] = [];
  const fechasProblematicas: ValidacionFechasPlan['fechasProblematicas'] = [];

  if (!plan.semanas || !Array.isArray(plan.semanas)) {
    errores.push('El plan no contiene semanas válidas');
    return { esValido: false, errores, advertencias, fechasProblematicas };
  }

  let fechaAnterior: Date | null = null;

  for (const semana of plan.semanas) {
    if (!semana || typeof semana.numero !== 'number') {
      errores.push(`Semana inválida encontrada`);
      continue;
    }

    const fechaInicio = parseDate(semana.fechaInicio);
    const fechaFin = parseDate(semana.fechaFin);

    if (!fechaInicio) {
      errores.push(`Fecha de inicio inválida en semana ${semana.numero}: ${semana.fechaInicio}`);
      fechasProblematicas.push({
        semana: semana.numero,
        fecha: semana.fechaInicio,
        problema: 'Fecha de inicio inválida'
      });
      continue;
    }

    if (!fechaFin) {
      errores.push(`Fecha de fin inválida en semana ${semana.numero}: ${semana.fechaFin}`);
      fechasProblematicas.push({
        semana: semana.numero,
        fecha: semana.fechaFin,
        problema: 'Fecha de fin inválida'
      });
      continue;
    }

    // Validar que fecha fin sea posterior a fecha inicio
    if (fechaFin <= fechaInicio) {
      errores.push(`La fecha de fin debe ser posterior a la de inicio en semana ${semana.numero}`);
    }

    // Validar continuidad con la semana anterior
    if (fechaAnterior && fechaInicio < fechaAnterior) {
      advertencias.push(`La semana ${semana.numero} comienza antes de que termine la anterior`);
    }

    fechaAnterior = fechaFin;
  }

  return {
    esValido: errores.length === 0,
    errores,
    advertencias,
    fechasProblematicas
  };
}

/**
 * Obtiene las tareas de un día específico
 */
export function obtenerTareasDelDia(
  fecha: Date,
  datosPlan: DatosPlanCalendario
): TareaDelDia[] {
  const fechaKey = formatDate(fecha);
  const diaCalendario = datosPlan.mapaDias.get(fechaKey);
  return diaCalendario?.tareas || [];
}

/**
 * Obtiene el estado de un día específico
 */
export function obtenerEstadoDia(
  fecha: Date,
  datosPlan: DatosPlanCalendario
): EstadoDiaCalendario {
  const fechaKey = formatDate(fecha);
  const diaCalendario = datosPlan.mapaDias.get(fechaKey);
  return diaCalendario?.estado || 'normal';
}

/**
 * Crea un resultado vacío para casos de error
 */
function crearResultadoVacio(errores: string[]): PlanProcesado {
  return {
    datosPlan: {
      fechaInicio: new Date(),
      fechaFin: new Date(),
      totalSemanas: 0,
      mapaDias: new Map(),
      rangoFechas: {
        minYear: new Date().getFullYear(),
        maxYear: new Date().getFullYear(),
        minMonth: new Date().getMonth(),
        maxMonth: new Date().getMonth()
      }
    },
    estadisticas: {
      totalTareas: 0,
      tareasCompletadas: 0,
      porcentajeGeneral: 0,
      diasConTareas: 0,
      diasCompletados: 0
    },
    errores
  };
}
