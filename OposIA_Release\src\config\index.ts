/**
 * Configuración centralizada - Re-exports
 * 
 * Este archivo centraliza todas las configuraciones del sistema,
 * proporcionando un punto único de importación para cualquier configuración.
 * 
 * Uso:
 * import { PLAN_CONFIGURATIONS, FEATURES_CONFIG, ERROR_MESSAGES } from '@/config';
 */

// ============================================================================
// CONFIGURACIONES DE PLANES
// ============================================================================

export {
  // Interfaces y tipos
  type PlanLimits,
  type PlanConfiguration,

  // Configuraciones de planes
  PLAN_CONFIGURATIONS,

  // Funciones utilitarias
  getPlanConfiguration,
  getTokenLimitForPlan,
  hasFeatureAccess,
  getWeeklyLimit,
  getTrialLimit,
  isUnlimited,
  canPerformAction,
  checkUserFeatureAccess
} from './plans';

// ============================================================================
// CONFIGURACIONES DE FEATURES
// ============================================================================

export {
  // Interfaces y tipos
  type FeatureId,
  type FeatureConfig,
  type ActionType,

  // Constantes de IDs
  FEATURE_IDS,
  ACTION_TYPES,

  // Configuraciones de features
  FEATURES_CONFIG,
  ACTIVITY_TO_FEATURE_MAP,
  ACTION_TO_FEATURE_MAP,
  PLAN_RESTRICTED_ROUTES,

  // Funciones utilitarias
  getFeatureConfig,
  getFeatureDisplayName,
  getFeaturesByCategory,
  getFeaturesForPlan,
  featureRequiresPayment,
  getFeatureTokensRequired,
  actionToFeature,
  activityToFeature,
  getAllFeatures,
  getAllFeatureIds,
  isValidFeatureId
} from './features';

// ============================================================================
// CONSTANTES DEL SISTEMA
// ============================================================================

export {
  // URLs y rutas
  APP_URLS,
  PUBLIC_ROUTES,
  AUTHENTICATED_ROUTES,
  
  // Límites de archivos y validación
  FILE_LIMITS,
  TEXT_LIMITS,
  TOKEN_LIMITS,
  RATE_LIMITS,
  
  // Timeouts y configuración
  TIMEOUTS,
  RETRY_CONFIG,
  SECURITY_CONFIG,
  
  // Mensajes del sistema
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_MESSAGES,
  
  // Estados y tipos del sistema
  PROCESSING_STATES,
  PAYMENT_STATES,
  NOTIFICATION_TYPES,
  
  // Configuración de entorno y negocio
  REQUIRED_ENV_VARS,
  AUTOMATION_CONFIG,
  PRICING,
  FREE_PLAN_LIMITS,
  SECURITY_RISK_SCORES
} from './constants';

// ============================================================================
// RE-EXPORTS COMBINADOS PARA CONVENIENCIA
// ============================================================================

// Importar constantes para uso en re-exports combinados
import {
  FILE_LIMITS,
  TEXT_LIMITS,
  TOKEN_LIMITS,
  RATE_LIMITS,
  TIMEOUTS,
  RETRY_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  VALIDATION_MESSAGES,
  SECURITY_CONFIG,
  SECURITY_RISK_SCORES
} from './constants';

/**
 * Todas las configuraciones de límites en un solo objeto
 */
export const LIMITS = {
  FILE: FILE_LIMITS,
  TEXT: TEXT_LIMITS,
  TOKEN: TOKEN_LIMITS,
  RATE: RATE_LIMITS
} as const;

/**
 * Todas las configuraciones de tiempo en un solo objeto
 */
export const TIME_CONFIG = {
  TIMEOUTS,
  RETRY: RETRY_CONFIG
} as const;

/**
 * Todos los mensajes del sistema en un solo objeto
 */
export const MESSAGES = {
  ERROR: ERROR_MESSAGES,
  SUCCESS: SUCCESS_MESSAGES,
  VALIDATION: VALIDATION_MESSAGES
} as const;

/**
 * Todas las configuraciones de seguridad en un solo objeto
 */
export const SECURITY = {
  CONFIG: SECURITY_CONFIG,
  RISK_SCORES: SECURITY_RISK_SCORES
} as const;
