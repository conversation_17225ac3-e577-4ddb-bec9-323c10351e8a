// src/lib/supabase/admin.ts
// Cliente administrativo de Supabase para operaciones del servidor

import { createClient } from '@supabase/supabase-js';

// Cliente admin con privilegios elevados
export const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Tipos para las nuevas tablas
export interface StripeTransaction {
  id: string;
  stripe_session_id: string;
  stripe_customer_id?: string;
  user_email: string;
  user_name?: string;
  plan_id: string;
  amount: number;
  currency: string;
  payment_status: string;
  subscription_id?: string;
  user_id?: string;
  metadata?: any;
  created_at: string;
  activated_at?: string;
  expires_at?: string;
}

export interface UserPlanHistory {
  id: string;
  user_id: string;
  old_plan?: string;
  new_plan: string;
  changed_by: 'system' | 'admin' | 'user';
  reason?: string;
  transaction_id?: string;
  created_at: string;
}

export interface FeatureAccessLog {
  id: string;
  user_id: string;
  feature_name: string;
  access_granted: boolean;
  plan_at_time: string;
  tokens_used: number;
  denial_reason?: string;
  created_at: string;
}

export interface ExtendedUserProfile {
  id: string;
  user_id: string;
  subscription_plan: 'free' | 'usuario' | 'pro';
  monthly_token_limit: number;
  current_month_tokens: number;
  current_month: string;
  payment_verified: boolean;
  stripe_customer_id?: string;
  stripe_subscription_id?: string; // ID de la suscripción activa en Stripe
  plan_expires_at?: string;
  last_payment_date?: string;
  auto_renew: boolean;
  plan_features: any;
  security_flags: any;
  terms_accepted_at?: string;
  created_at: string;
  updated_at: string;
}

// Funciones de utilidad para operaciones administrativas
export class SupabaseAdminService {
  
  // Crear transacción de Stripe
  static async createStripeTransaction(transaction: Omit<StripeTransaction, 'id' | 'created_at'>): Promise<StripeTransaction> {
    const { data, error } = await supabaseAdmin
      .from('stripe_transactions')
      .insert([transaction])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating stripe transaction:', error);
      throw new Error(`Failed to create transaction: ${error.message}`);
    }
    
    return data;
  }
  
  // Obtener transacción por session ID
  static async getTransactionBySessionId(sessionId: string): Promise<StripeTransaction | null> {
    const { data, error } = await supabaseAdmin
      .from('stripe_transactions')
      .select('*')
      .eq('stripe_session_id', sessionId)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching transaction:', error);
      throw new Error(`Failed to fetch transaction: ${error.message}`);
    }
    
    return data;
  }
  
  // Crear usuario con invitación
  static async createUserWithInvitation(email: string, userData: any) {
    console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {
      email,
      userData,
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(
      email,
      {
        data: userData,
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`
      }
    );

    console.log('📊 [SUPABASE_ADMIN] Invitation result:', {
      hasData: !!data,
      hasUser: !!data?.user,
      userId: data?.user?.id,
      userEmail: data?.user?.email,
      userAud: data?.user?.aud,
      userRole: data?.user?.role,
      emailConfirmed: data?.user?.email_confirmed_at,
      userMetadata: data?.user?.user_metadata,
      appMetadata: data?.user?.app_metadata,
      error: error?.message,
      errorCode: error?.status,
      fullError: error
    });

    if (error) {
      console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {
        message: error.message,
        status: error.status,
        details: error
      });
      throw new Error(`Failed to create user invitation: ${error.message}`);
    }

    return data;
  }

  // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación
  static async createUserWithPassword(email: string, password: string, userData: any, sendConfirmationEmail: boolean = true) {
    console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {
      email,
      userData,
      sendConfirmationEmail,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      user_metadata: userData,
      email_confirm: false // No confirmar automáticamente
    });

    console.log('📊 [SUPABASE_ADMIN] User creation result:', {
      hasData: !!data,
      hasUser: !!data?.user,
      userId: data?.user?.id,
      userEmail: data?.user?.email,
      emailConfirmed: data?.user?.email_confirmed_at,
      userMetadata: data?.user?.user_metadata,
      error: error?.message,
      errorCode: error?.status
    });

    if (error) {
      console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {
        message: error.message,
        status: error.status,
        details: error
      });
      return { data: null, error };
    }

    // Enviar email de confirmación solo si se solicita
    if (data?.user && sendConfirmationEmail) {
      console.log('📧 Enviando email de confirmación...');

      const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'signup',
        email: email,
        password: password, // Requerido para generateLink
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirmed`
        }
      });

      if (emailError) {
        console.error('⚠️ Error enviando email de confirmación:', emailError);
        // No fallar completamente, el usuario puede confirmar manualmente
      } else {
        console.log('✅ Email de confirmación enviado exitosamente');
      }
    } else if (data?.user && !sendConfirmationEmail) {
      console.log('📧 Email de confirmación omitido (se enviará después del pago)');
    }

    return { data, error: null };
  }

  // Enviar email de confirmación para usuario existente
  static async sendConfirmationEmailForUser(userId: string): Promise<{ success: boolean; error?: string }> {
    console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);

    try {
      // Obtener datos del usuario
      const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);

      if (userError || !userData?.user) {
        console.error('Error obteniendo datos del usuario:', userError);
        return { success: false, error: 'Usuario no encontrado' };
      }

      const user = userData.user;

      // Para usuarios pre-registrados, actualizar el estado de confirmación directamente
      // ya que el pago exitoso confirma la intención del usuario
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        user.id,
        {
          email_confirm: true,
          user_metadata: {
            ...user.user_metadata,
            payment_verified: true,
            email_confirmed_via_payment: true,
            confirmed_at: new Date().toISOString()
          }
        }
      );

      if (updateError) {
        console.error('⚠️ Error confirmando email del usuario:', updateError);
        return { success: false, error: updateError.message };
      }

      console.log('✅ Usuario confirmado automáticamente después del pago exitoso');
      return { success: true };
    } catch (error) {
      console.error('Error en sendConfirmationEmailForUser:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Error desconocido' };
    }
  }

  // Enviar email de confirmación para usuario existente (método legacy)
  static async sendConfirmationEmail(email: string, password: string): Promise<{ success: boolean; error?: string }> {
    console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);

    const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'signup',
      email: email,
      password: password,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/confirmed`
      }
    });

    if (emailError) {
      console.error('⚠️ Error enviando email de confirmación:', emailError);
      return { success: false, error: emailError.message };
    } else {
      console.log('✅ Email de confirmación enviado exitosamente');
      return { success: true };
    }
  }

  // Crear perfil de usuario
  static async createUserProfile(profile: Partial<ExtendedUserProfile>): Promise<ExtendedUserProfile> {
    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .insert([profile])
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      throw new Error(`Failed to create user profile: ${error.message}`);
    }

    return data;
  }

  // Crear o actualizar perfil de usuario
  static async upsertUserProfile(profile: Partial<ExtendedUserProfile>): Promise<ExtendedUserProfile> {
    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .upsert([profile], { onConflict: 'user_id' })
      .select()
      .single();

    if (error) {
      console.error('Error upserting user profile:', error);
      throw new Error(`Failed to upsert user profile: ${error.message}`);
    }

    return data;
  }
  
  // Registrar cambio de plan
  static async logPlanChange(planChange: Omit<UserPlanHistory, 'id' | 'created_at'>): Promise<UserPlanHistory> {
    const { data, error } = await supabaseAdmin
      .from('user_plan_history')
      .insert([planChange])
      .select()
      .single();
    
    if (error) {
      console.error('Error logging plan change:', error);
      throw new Error(`Failed to log plan change: ${error.message}`);
    }
    
    return data;
  }
  
  // Registrar acceso a característica
  static async logFeatureAccess(accessLog: Omit<FeatureAccessLog, 'id' | 'created_at'>): Promise<FeatureAccessLog> {
    const { data, error } = await supabaseAdmin
      .from('feature_access_log')
      .insert([accessLog])
      .select()
      .single();
    
    if (error) {
      console.error('Error logging feature access:', error);
      throw new Error(`Failed to log feature access: ${error.message}`);
    }
    
    return data;
  }
  
  // Obtener perfil de usuario por ID
  static async getUserProfile(userId: string): Promise<ExtendedUserProfile | null> {
    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching user profile:', error);
      throw new Error(`Failed to fetch user profile: ${error.message}`);
    }
    
    return data;
  }
  
  // Actualizar transacción con user_id
  static async updateTransactionWithUser(transactionId: string, userId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('stripe_transactions')
      .update({
        user_id: userId,
        updated_at: new Date().toISOString()
      })
      .eq('id', transactionId);

    if (error) {
      console.error('Error updating transaction with user_id:', error);
      throw new Error(`Failed to update transaction: ${error.message}`);
    }
  }

  // Activar transacción (marcar como activada)
  static async activateTransaction(transactionId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('stripe_transactions')
      .update({ activated_at: new Date().toISOString() })
      .eq('id', transactionId);

    if (error) {
      console.error('Error activating transaction:', error);
      throw new Error(`Failed to activate transaction: ${error.message}`);
    }
  }

  // Obtener conteo de documentos del usuario
  static async getDocumentsCount(userId: string): Promise<number> {
    const { count, error } = await supabaseAdmin
      .from('documentos')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (error) {
      console.error('Error getting documents count:', error);
      return 0; // Retornar 0 en caso de error en lugar de lanzar excepción
    }

    return count || 0;
  }

  // Obtener usuario por email desde Supabase Auth
  static async getUserByEmail(email: string): Promise<{ id: string; email: string; email_confirmed_at?: string } | null> {
    try {
      const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();

      if (error) {
        console.error('Error getting user by email:', error);
        throw new Error(`Failed to get user by email: ${error.message}`);
      }

      if (!users || users.length === 0) {
        return null;
      }

      // Filtrar por email ya que la API no permite filtro directo
      const user = users.find(u => u.email === email);

      if (!user) {
        return null;
      }

      return {
        id: user.id,
        email: user.email!,
        email_confirmed_at: user.email_confirmed_at
      };
    } catch (error) {
      console.error('Error in getUserByEmail:', error);
      throw error;
    }
  }
}
