// src/components/ui/CookieBanner.tsx
// Componente del banner de cookies siguiendo el sistema de diseño de OposIA

'use client';

import React from 'react';
import Link from 'next/link';
import { FiCheck } from 'react-icons/fi';
import CookieConsentManager from '@/features/privacy/components/CookieConsentManager';

export default function CookieBanner() {
  return (
    <CookieConsentManager>
      {({ showBanner, grantConsent, isLoading }) => {
        if (!showBanner || isLoading) {
          return null;
        }

        return (
          <div
            className="fixed bottom-0 left-0 right-0 z-50 bg-gray-800 text-white shadow-lg animate-fadeInUp"
            role="dialog"
            aria-labelledby="cookie-consent-title"
            aria-describedby="cookie-consent-description"
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="py-4 flex flex-col sm:flex-row items-center justify-between">
                <div className="flex-1 flex items-start sm:items-center mb-4 sm:mb-0">
                  <div className="flex-shrink-0">
                    <span className="text-2xl">🍪</span>
                  </div>
                  <div className="ml-3">
                    <p id="cookie-consent-title" className="font-semibold">
                      Este sitio web utiliza cookies
                    </p>
                    <p id="cookie-consent-description" className="text-sm text-gray-300">
                      Utilizamos cookies esenciales para garantizar el funcionamiento correcto de la aplicación, como mantener tu sesión iniciada. No utilizamos cookies de publicidad o seguimiento de terceros. Al continuar, aceptas su uso. Para más información, consulta nuestra{' '}
                      <Link href="/politica-de-cookies" className="font-medium underline hover:text-white">
                        Política de Cookies
                      </Link>.
                    </p>
                  </div>
                </div>
                <div className="flex-shrink-0 flex items-center gap-4">
                  <button
                    onClick={grantConsent}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-blue-500"
                    aria-label="Aceptar y cerrar banner de cookies"
                  >
                    <FiCheck className="mr-2" />
                    Entendido
                  </button>
                </div>
              </div>
            </div>
            
            {/* Estilos para la animación */}
            <style jsx>{`
              @keyframes fadeInUp {
                from {
                  transform: translateY(100%);
                  opacity: 0;
                }
                to {
                  transform: translateY(0);
                  opacity: 1;
                }
              }
              .animate-fadeInUp {
                animation: fadeInUp 0.5s ease-out forwards;
              }
            `}</style>
          </div>
        );
      }}
    </CookieConsentManager>
  );
}
