@echo off
chcp 65001 >nul
title Crear ZIP de OposiAI

echo.
echo 🚀 Creando ZIP de OposiAI...
echo.

REM Crear carpeta temporal
set TEMP_DIR=OposiAI_Release
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

echo 📁 Copiando archivos esenciales...

REM Copiar archivos de configuración
copy "package.json" "%TEMP_DIR%\" >nul
copy "next.config.js" "%TEMP_DIR%\" >nul
copy "tailwind.config.js" "%TEMP_DIR%\" >nul
copy "tsconfig.json" "%TEMP_DIR%\" >nul
copy ".eslintrc.json" "%TEMP_DIR%\" >nul

REM Copiar vercel.json si existe
if exist "vercel.json" copy "vercel.json" "%TEMP_DIR%\" >nul

echo 📂 Copiando código fuente...
xcopy "src" "%TEMP_DIR%\src\" /e /i /q >nul

echo 📂 Copiando archivos públicos...
if exist "public" xcopy "public" "%TEMP_DIR%\public\" /e /i /q >nul

echo 🔧 Copiando archivos de inicio...
copy "INICIAR_APLICACION.bat" "%TEMP_DIR%\" >nul
copy "COMO_INICIAR.txt" "%TEMP_DIR%\" >nul

echo ⚙️ Creando archivo de configuración...

REM Crear .env.local
(
echo # Configuración de OposiAI - CONFIGURAR ANTES DE USAR
echo.
echo # Supabase Configuration ^(REQUERIDO^)
echo NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
echo NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
echo SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
echo.
echo # OpenAI Configuration ^(REQUERIDO^)
echo OPENAI_API_KEY=your_openai_api_key_here
echo.
echo # Stripe Configuration ^(REQUERIDO para pagos^)
echo STRIPE_SECRET_KEY=your_stripe_secret_key_here
echo NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
echo STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here
echo.
echo # Application URLs
echo NEXT_PUBLIC_APP_URL=http://localhost:3000
echo.
echo # Admin Configuration
echo ADMIN_EMAILS=<EMAIL>
echo.
echo # Email Configuration ^(opcional^)
echo RESEND_API_KEY=your_resend_api_key_here
) > "%TEMP_DIR%\.env.local"

REM Crear README básico
(
echo # OposiAI - Sistema de Preparacion de Oposiciones
echo.
echo ## Inicio Rapido
echo.
echo 1. Configurar variables de entorno: Editar .env.local con las claves reales
echo 2. Iniciar aplicacion: Ejecutar INICIAR_APLICACION.bat
echo 3. Abrir navegador: http://localhost:3000
echo.
echo ## Requisitos
echo.
echo - Node.js 18+
echo - Cuentas en Supabase, OpenAI y Stripe
echo.
echo ## Estructura
echo.
echo - src/ - Codigo fuente
echo - public/ - Archivos estaticos
echo - .env.local - Variables de entorno
echo - INICIAR_APLICACION.bat - Iniciador automatico
echo.
echo Para mas informacion, ver COMO_INICIAR.txt
) > "%TEMP_DIR%\README.md"

echo 📦 Creando archivo ZIP...

REM Usar PowerShell para crear el ZIP
powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath 'OposiAI_Release.zip' -CompressionLevel Optimal -Force"

if %errorlevel% equ 0 (
    echo ✅ ZIP creado exitosamente: OposiAI_Release.zip
    echo.
    echo 📊 Contenido incluido:
    echo    ✅ Código fuente completo ^(src/^)
    echo    ✅ Archivos de configuración
    echo    ✅ Iniciador automático ^(.bat^)
    echo    ✅ Variables de entorno ^(.env.local^)
    echo    ✅ Instrucciones de uso
    echo    ✅ Archivos públicos
    echo.
    echo 🎯 El ZIP está listo para envío de registro de propiedad intelectual
) else (
    echo ❌ Error creando el ZIP
)

REM Limpiar carpeta temporal
rmdir /s /q "%TEMP_DIR%"

echo.
pause
