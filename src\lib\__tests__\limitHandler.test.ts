// src/lib/__tests__/limitHandler.test.ts
// Tests para el manejo de límites

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { LimitHandler } from '@/lib/services/limitHandler';
import { SupabaseAdminService } from '@/lib/supabase/admin';

// Mock de Supabase Admin Service
jest.mock('@/lib/supabase/admin');
const mockSupabaseAdmin = SupabaseAdminService as jest.Mocked<typeof SupabaseAdminService>;

describe('LimitHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkUserLimits', () => {
    it('should detect warning level token usage (75%)', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 37500, // 75%
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toHaveLength(1);
      expect(limits[0].type).toBe('tokens');
      expect(limits[0].severity).toBe('warning');
      expect(limits[0].percentage).toBe(75);
      expect(limits[0].actionRequired).toBe(false);
    });

    it('should detect limit reached level (90%)', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 45000, // 90%
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toHaveLength(1);
      expect(limits[0].type).toBe('tokens');
      expect(limits[0].severity).toBe('limit_reached');
      expect(limits[0].percentage).toBe(90);
      expect(limits[0].actionRequired).toBe(true);
      expect(limits[0].upgradeOptions).toBeDefined();
    });

    it('should detect exceeded limit (100%+)', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 52000, // 104%
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toHaveLength(1);
      expect(limits[0].type).toBe('tokens');
      expect(limits[0].severity).toBe('exceeded');
      expect(limits[0].percentage).toBe(104);
      expect(limits[0].actionRequired).toBe(true);
    });

    it('should detect payment verification issues', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: false, // Payment not verified
        monthly_token_limit: 1000000,
        current_month_tokens: 10000,
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toHaveLength(1);
      expect(limits[0].type).toBe('plan');
      expect(limits[0].severity).toBe('exceeded');
      expect(limits[0].message).toContain('pago está pendiente');
      expect(limits[0].actionRequired).toBe(true);
    });

    it('should return empty array for healthy usage', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 100000, // 10%
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toHaveLength(0);
    });
  });

  describe('isActionBlocked', () => {
    it('should block action when tokens exceeded', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 50000, // At limit
        current_month: '2025-01-01'
      });

      const result = await LimitHandler.isActionBlocked('test-user', 'test_generation', 1000);

      expect(result.blocked).toBe(true);
      expect(result.reason).toContain('límite mensual');
      expect(result.limitStatus).toBeDefined();
    });

    it('should block action when payment not verified', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: false,
        monthly_token_limit: 1000000,
        current_month_tokens: 10000,
        current_month: '2025-01-01'
      });

      const result = await LimitHandler.isActionBlocked('test-user', 'ai_chat', 1000);

      expect(result.blocked).toBe(true);
      expect(result.reason).toContain('pago está pendiente');
    });

    it('should allow action when within limits', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 100000,
        current_month: '2025-01-01'
      });

      const result = await LimitHandler.isActionBlocked('test-user', 'ai_chat', 1000);

      expect(result.blocked).toBe(false);
    });

    it('should block action when would exceed remaining tokens', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 49500, // 500 tokens remaining
        current_month: '2025-01-01'
      });

      const result = await LimitHandler.isActionBlocked('test-user', 'test_generation', 1000);

      expect(result.blocked).toBe(true);
      expect(result.reason).toContain('requiere 1000 tokens pero solo tienes 500');
    });
  });

  describe('recordUsage', () => {
    it('should update token usage correctly', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 10000,
        current_month: '2025-01-01'
      });

      mockSupabaseAdmin.upsertUserProfile.mockResolvedValue(true);

      await LimitHandler.recordUsage('test-user', 'test_generation', 1000);

      expect(mockSupabaseAdmin.upsertUserProfile).toHaveBeenCalledWith(
        expect.objectContaining({
          current_month_tokens: 11000
        })
      );
    });

    it('should handle month rollover during usage recording', async () => {
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 45000,
        current_month: lastMonth.toISOString().slice(0, 7) + '-01'
      });

      mockSupabaseAdmin.upsertUserProfile.mockResolvedValue(true);

      await LimitHandler.recordUsage('test-user', 'test_generation', 1000);

      expect(mockSupabaseAdmin.upsertUserProfile).toHaveBeenCalledWith(
        expect.objectContaining({
          current_month_tokens: 1000, // Reset + new usage
          current_month: expect.stringContaining('2025-01')
        })
      );
    });

    it('should not update tokens when usage is 0', async () => {
      await LimitHandler.recordUsage('test-user', 'document_view', 0);

      expect(mockSupabaseAdmin.getUserProfile).not.toHaveBeenCalled();
      expect(mockSupabaseAdmin.upsertUserProfile).not.toHaveBeenCalled();
    });
  });

  describe('createLimitNotification', () => {
    it('should create warning notification for token usage', async () => {
      const limitStatus = {
        type: 'tokens' as const,
        severity: 'warning' as const,
        current: 37500,
        limit: 50000,
        percentage: 75,
        message: 'Has usado 75% de tus tokens mensuales',
        actionRequired: false,
        upgradeOptions: []
      };

      const notification = await LimitHandler.createLimitNotification('test-user', limitStatus);

      expect(notification.userId).toBe('test-user');
      expect(notification.type).toBe('limit_tokens');
      expect(notification.severity).toBe('info');
      expect(notification.title).toContain('Uso elevado de tokens');
      expect(notification.message).toBe(limitStatus.message);
    });

    it('should create error notification for exceeded limits', async () => {
      const limitStatus = {
        type: 'tokens' as const,
        severity: 'exceeded' as const,
        current: 52000,
        limit: 50000,
        percentage: 104,
        message: 'Has excedido tu límite mensual de tokens',
        actionRequired: true,
        upgradeOptions: [{
          plan: 'usuario',
          benefits: ['More tokens'],
          newLimit: 1000000
        }]
      };

      const notification = await LimitHandler.createLimitNotification('test-user', limitStatus);

      expect(notification.severity).toBe('error');
      expect(notification.title).toContain('excedido');
      expect(notification.actionUrl).toBe('/payment');
      expect(notification.actionText).toBe('Actualizar Plan');
    });
  });

  describe('upgrade options', () => {
    it('should provide correct upgrade options for free plan', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'free',
        payment_verified: false,
        monthly_token_limit: 50000,
        current_month_tokens: 45000,
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');
      const tokenLimit = limits.find(l => l.type === 'tokens');

      expect(tokenLimit?.upgradeOptions).toHaveLength(2);
      expect(tokenLimit?.upgradeOptions?.[0].plan).toBe('usuario');
      expect(tokenLimit?.upgradeOptions?.[1].plan).toBe('pro');
    });

    it('should provide correct upgrade options for usuario plan', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue({
        user_id: 'test-user',
        subscription_plan: 'usuario',
        payment_verified: true,
        monthly_token_limit: 1000000,
        current_month_tokens: 950000,
        current_month: '2025-01-01'
      });

      const limits = await LimitHandler.checkUserLimits('test-user');
      const tokenLimit = limits.find(l => l.type === 'tokens');

      expect(tokenLimit?.upgradeOptions).toHaveLength(1);
      expect(tokenLimit?.upgradeOptions?.[0].plan).toBe('pro');
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      mockSupabaseAdmin.getUserProfile.mockRejectedValue(new Error('Database error'));

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toEqual([]);
    });

    it('should handle null profile gracefully', async () => {
      mockSupabaseAdmin.getUserProfile.mockResolvedValue(null);

      const limits = await LimitHandler.checkUserLimits('test-user');

      expect(limits).toEqual([]);
    });
  });
});
