// src/app/upgrade-plan/page.tsx
// Página de actualización de plan para usuarios autenticados

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FiCheck, FiArrowLeft, FiStar, FiZap, FiShield, FiUsers } from 'react-icons/fi';
import { createClient } from '@/lib/supabase/client';

interface PlanFeature {
  name: string;
  included: boolean;
}

interface Plan {
  id: string;
  name: string;
  price: string;
  period: string;
  description: string;
  features: PlanFeature[];
  recommended?: boolean;
  icon: React.ReactNode;
  stripePriceId?: string;
}

interface UserProfile {
  subscription_plan: string;
  payment_verified: boolean;
}

export default function UpgradePlanPage() {
  const router = useRouter();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  const plans: Plan[] = [
    {
      id: 'usuario',
      name: 'Plan Usuario',
      price: '€10.00',
      period: '/mes',
      description: 'Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',
      icon: <FiUsers className="w-8 h-8" />,
      recommended: true,
      features: [
        { name: 'Subida de documentos', included: true },
        { name: 'Habla con tu preparador IA *', included: true },
        { name: 'Generador de test *', included: true },
        { name: 'Generador de flashcards *', included: true },
        { name: 'Generador de mapas mentales *', included: true },
        { name: '* Para las tareas en las que se haga uso de IA, el límite mensual será de 500.000 tokens.', included: true },
        { name: 'Planificación de estudios', included: false },
        { name: 'Resúmenes para el ejercicio de desarrollo para cueropos superiores A2 y A1', included: false }
      ],
      stripePriceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_USUARIO
    },
    {
      id: 'pro',
      name: 'Plan Pro',
      price: '€15.00',
      period: '/mes',
      description: 'Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',
      icon: <FiStar className="w-8 h-8" />,
      features: [
        { name: 'Subida de documentos', included: true },
        { name: 'Planificación de estudios mediante IA*', included: true },
        { name: 'Habla con tu preparador IA *', included: true },
        { name: 'Generador de test *', included: true },
        { name: 'Generador de flashcards *', included: true },
        { name: 'Generador de mapas mentales *', included: true },
        { name: 'Generación de Resúmenes para A2 y A1', included: true },
        { name: '* Para las tareas en las que se haga uso de IA, el límite mensual será de 1,000,000 de tokens.', included: true }
      ],
      stripePriceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO
    }
  ];

  const loadUserProfile = useCallback(async () => {
    try {
      const supabase = createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        router.push('/login');
        return;
      }

      // Obtener perfil del usuario
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('subscription_plan, payment_verified')
        .eq('user_id', user.id)
        .single();

      if (profileError) {
        console.error('Error loading user profile:', profileError);
      } else {
        setUserProfile(profile);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  }, [router]);

  useEffect(() => {
    loadUserProfile();
  }, [loadUserProfile]);

  const handleSelectPlan = async (plan: Plan) => {
    if (!plan.stripePriceId) {
      console.error('No Stripe price ID configured for plan:', plan.id);
      return;
    }

    setProcessingPlan(plan.id);

    try {
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: plan.stripePriceId,
          successUrl: `${window.location.origin}/app?upgrade=success`,
          cancelUrl: `${window.location.origin}/upgrade-plan?upgrade=cancelled`,
        }),
      });

      if (!response.ok) {
        throw new Error('Error creating checkout session');
      }

      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      setProcessingPlan(null);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link
              href="/app"
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <FiArrowLeft className="w-5 h-5 mr-2" />
              Volver al Dashboard
            </Link>
            
            {userProfile && (
              <div className="text-sm text-gray-600">
                Plan actual: <span className="font-medium capitalize">{userProfile.subscription_plan}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Actualiza tu Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Desbloquea todo el potencial de OposiAI con nuestros planes premium. 
            Más recursos, funciones avanzadas y soporte prioritario.
          </p>
        </div>

        {/* Plans Grid */}
        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-2xl shadow-lg overflow-hidden ${
                plan.recommended ? 'ring-2 ring-blue-500 transform scale-105' : ''
              }`}
            >
              {plan.recommended && (
                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center py-2 text-sm font-medium">
                  ⭐ Más Popular
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4 text-blue-600">
                    {plan.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {plan.description}
                  </p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 ml-1">
                      {plan.period}
                    </span>
                  </div>
                </div>

                {/* Features List */}
                <div className="mb-8">
                  {/* Características incluidas */}
                  <div className="space-y-3 mb-6">
                    <h4 className="text-sm font-semibold text-gray-900">Incluye:</h4>
                    <ul className="space-y-3">
                      {plan.features.filter(f => f.included).map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <FiCheck className="w-5 h-5 mr-3 mt-0.5 flex-shrink-0 text-green-500" />
                          <span className="text-sm text-gray-700">
                            {feature.name}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Características no incluidas */}
                  {plan.features.filter(f => !f.included).length > 0 && (
                    <div className="space-y-3 pt-4 border-t border-gray-200">
                      <h4 className="text-sm font-semibold text-gray-900">No incluye:</h4>
                      <ul className="space-y-3">
                        {plan.features.filter(f => !f.included).map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <div className="w-5 h-5 mr-3 mt-0.5 flex-shrink-0 flex items-center justify-center">
                              <div className="w-3 h-0.5 bg-red-400 rounded"></div>
                            </div>
                            <span className="text-sm text-gray-500">
                              {feature.name}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* CTA Button */}
                <button
                  onClick={() => handleSelectPlan(plan)}
                  disabled={processingPlan === plan.id || userProfile?.subscription_plan === plan.id}
                  className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 ${
                    userProfile?.subscription_plan === plan.id
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : plan.recommended
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                        : 'bg-gray-900 text-white hover:bg-gray-800'
                  } ${processingPlan === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {processingPlan === plan.id ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Procesando...
                    </div>
                  ) : userProfile?.subscription_plan === plan.id ? (
                    'Plan Actual'
                  ) : (
                    `Seleccionar ${plan.name}`
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-xl p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              ¿Por qué actualizar?
            </h3>
            <div className="grid md:grid-cols-3 gap-8 mt-8">
              <div className="text-center">
                <FiZap className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h4 className="font-semibold text-gray-900 mb-2">Más Potencia</h4>
                <p className="text-gray-600 text-sm">
                  Acceso a más tokens y recursos para estudiar sin límites
                </p>
              </div>
              <div className="text-center">
                <FiShield className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h4 className="font-semibold text-gray-900 mb-2">Funciones Avanzadas</h4>
                <p className="text-gray-600 text-sm">
                  Planificación inteligente, resúmenes A1/A2 y análisis detallado
                </p>
              </div>
              <div className="text-center">
                <FiUsers className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h4 className="font-semibold text-gray-900 mb-2">Soporte Prioritario</h4>
                <p className="text-gray-600 text-sm">
                  Atención personalizada y acceso anticipado a nuevas funciones
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
