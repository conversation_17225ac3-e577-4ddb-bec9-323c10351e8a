'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { PLANS } from '@/lib/stripe/plans';
import PlanCard from '@/components/ui/PlanCard';
import { useAuth } from '@/contexts/AuthContext';



export default function Home() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  // Redirigir usuarios autenticados a la aplicación
  useEffect(() => {
    if (!isLoading && user) {
      router.push('/app');
    }
  }, [user, isLoading, router]);

  // Si está cargando, mostrar pantalla de carga
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800">
      {/* Header */}
      <header className="relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-end items-center py-6">
            <nav className="hidden md:flex space-x-8">
              <a href="#pricing" className="text-white/80 hover:text-white transition-colors">
                Precios
              </a>
              <a href="/login" className="bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                Iniciar Sesión
              </a>
            </nav>
          </div>
        </div>

        {/* Logo centrado absolutamente */}
        <div className="absolute inset-x-0 top-5 flex justify-center items-center h-40 pt-4 pointer-events-none">
          <Image
            src="/logo.png"
            alt="OposiAI Logo"
            width={320}
            height={320}
            className="h-80 w-80 object-contain"
          />
        </div>
      </header>

      {/* Hero Section - Reducido */}
      <section className="pt-16 pb-12 text-center text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-12">
            OposiAI Tu Preparador Personal Inteligente
          </h1>
          <a
            href="#pricing"
            className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
          >
            Elige tu Plan Perfecto para Ti
          </a>
        </div>
      </section>

      {/* Features Section - Movido aquí */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              ¿Cómo Funciona OposiAI?
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📚</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Elige tu Temario</h3>
              <p className="text-gray-600 text-sm mb-4">
                Selecciona un temario predeterminado o sube tus documentos de estudio.
              </p>
              {/* Video placeholder */}
              <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <div className="flex items-center justify-center h-24">
                  <div className="text-center">
                    <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                    </svg>
                    <p className="text-xs text-gray-500">Video Tutorial</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Selecciona el Tema</h3>
              <p className="text-gray-600 text-sm mb-4">
                Escoge el tema específico que quieres preparar hoy.
              </p>
              {/* Video placeholder */}
              <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <div className="flex items-center justify-center h-24">
                  <div className="text-center">
                    <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                    </svg>
                    <p className="text-xs text-gray-500">Video Tutorial</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤖</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Usa IA para Estudiar</h3>
              <p className="text-gray-600 text-sm mb-4">
                Genera contenido automáticamente: tests, flashcards y mapas mentales.
              </p>
              {/* Video placeholder */}
              <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <div className="flex items-center justify-center h-24">
                  <div className="text-center">
                    <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                    </svg>
                    <p className="text-xs text-gray-500">Video Tutorial</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📅</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Planifica tu Tiempo</h3>
              <p className="text-gray-600 text-sm mb-4">
                Deja que la IA diseñe un plan de estudios personalizado para ti.
              </p>
              {/* Video placeholder */}
              <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <div className="flex items-center justify-center h-24">
                  <div className="text-center">
                    <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                    </svg>
                    <p className="text-xs text-gray-500">Video Tutorial</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚀</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">Alcanza tu Objetivo</h3>
              <p className="text-gray-600 text-sm mb-4">
                Sigue tu progreso y consigue tu plaza con metodología inteligente.
              </p>
              {/* Video placeholder */}
              <div className="bg-gray-100 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <div className="flex items-center justify-center h-24">
                  <div className="text-center">
                    <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                    </svg>
                    <p className="text-xs text-gray-500">Video Tutorial</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto items-stretch">
            <PlanCard
              id={PLANS.free.id}
              name={PLANS.free.name}
              price={PLANS.free.price}
              features={PLANS.free.features}
            />
            <PlanCard
              id={PLANS.usuario.id}
              name={PLANS.usuario.name}
              price={PLANS.usuario.price}
              features={PLANS.usuario.features}
              isPopular={true}
            />
            <PlanCard
              id={PLANS.pro.id}
              name={PLANS.pro.name}
              price={PLANS.pro.price}
              features={PLANS.pro.features}
            />
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            ¿Listo para Empezar?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Únete a miles de opositores que ya están usando OposiAI para conseguir su plaza.
          </p>
          <a
            href="/login"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg"
          >
            Comenzar Ahora
          </a>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">OposiAI</h3>
            <p className="text-gray-400 mb-6">Tu preparador personal inteligente</p>
            <p className="text-gray-500">&copy; 2024 OposiAI. Todos los derechos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
