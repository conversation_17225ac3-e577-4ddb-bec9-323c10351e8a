// src/app/auth/unauthorized/page.tsx
// Página para usuarios sin autorización

'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';

interface UnauthorizedInfo {
  reason: string;
  userPlan: string;
  requiredPlan: string[];
  feature: string;
  hasUser: boolean;
}

function UnauthorizedContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [info, setInfo] = useState<UnauthorizedInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadInfo = async () => {
      try {
        const supabase = createClient();
        
        // Obtener parámetros de la URL
        const reason = searchParams.get('reason') || 'Acceso denegado';
        const feature = searchParams.get('feature') || '';
        const requiredPlan = searchParams.get('required_plan')?.split(',') || [];
        
        // Verificar si hay usuario autenticado
        const { data: { user } } = await supabase.auth.getUser();
        
        let userPlan = 'none';
        
        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('subscription_plan')
            .eq('user_id', user.id)
            .single();
          
          userPlan = profile?.subscription_plan || 'none';
        }
        
        setInfo({
          reason,
          userPlan,
          requiredPlan,
          feature,
          hasUser: !!user
        });
        
        setLoading(false);
        
      } catch (error) {
        console.error('Error loading unauthorized info:', error);
        setLoading(false);
      }
    };

    loadInfo();
  }, [searchParams]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>
    );
  }

  const getPlanDisplayName = (plan: string) => {
    const names: Record<string, string> = {
      'free': 'Gratis',
      'usuario': 'Usuario',
      'pro': 'Pro'
    };
    return names[plan] || plan;
  };

  const getUpgradeMessage = () => {
    if (!info) return '';
    
    if (info.userPlan === 'none') {
      return 'Necesitas crear una cuenta para acceder a esta función.';
    }
    
    if (info.requiredPlan.length > 0) {
      const requiredPlans = info.requiredPlan.map(getPlanDisplayName).join(' o ');
      return `Esta función requiere plan ${requiredPlans}. Tu plan actual es ${getPlanDisplayName(info.userPlan)}.`;
    }
    
    return info.reason;
  };

  const getActionButtons = () => {
    if (!info) return null;
    
    if (!info.hasUser) {
      return (
        <div className="space-y-3">
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold"
          >
            Iniciar Sesión
          </button>
          <button
            onClick={() => router.push('/upgrade-plan')}
            className="w-full bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 transition-colors"
          >
            Ver Planes
          </button>
        </div>
      );
    }
    
    if (info.userPlan === 'free' && info.requiredPlan.includes('usuario')) {
      return (
        <div className="space-y-3">
          <button
            onClick={() => router.push('/payment?upgrade=usuario')}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold"
          >
            Actualizar a Plan Usuario
          </button>
          <button
            onClick={() => router.push('/app')}
            className="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors"
          >
            Volver a la App
          </button>
        </div>
      );
    }
    
    if ((info.userPlan === 'free' || info.userPlan === 'usuario') && info.requiredPlan.includes('pro')) {
      return (
        <div className="space-y-3">
          <button
            onClick={() => router.push('/payment?upgrade=pro')}
            className="w-full bg-purple-600 text-white py-3 px-6 rounded-md hover:bg-purple-700 transition-colors font-semibold"
          >
            Actualizar a Plan Pro
          </button>
          <button
            onClick={() => router.push('/app')}
            className="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors"
          >
            Volver a la App
          </button>
        </div>
      );
    }
    
    return (
      <div className="space-y-3">
        <button
          onClick={() => router.push('/upgrade-plan')}
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold"
        >
          Ver Planes
        </button>
        <button
          onClick={() => router.push('/app')}
          className="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors"
        >
          Volver a la App
        </button>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        {/* Icon */}
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10" />
          </svg>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Acceso Restringido
        </h1>

        {/* Message */}
        <p className="text-gray-600 mb-6">
          {getUpgradeMessage()}
        </p>

        {/* Feature Info */}
        {info?.feature && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-gray-800 mb-2">Función solicitada:</h3>
            <p className="text-gray-600 text-sm">{info.feature}</p>
          </div>
        )}

        {/* Plan Comparison */}
        {info?.requiredPlan && info.requiredPlan.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">Planes requeridos:</h3>
            <div className="flex justify-center space-x-2">
              {info.requiredPlan.map((plan, index) => (
                <span
                  key={index}
                  className="bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {getPlanDisplayName(plan)}
                </span>
              ))}
            </div>
            {info.hasUser && (
              <p className="text-blue-600 text-sm mt-2">
                Tu plan actual: <strong>{getPlanDisplayName(info.userPlan)}</strong>
              </p>
            )}
          </div>
        )}

        {/* Action Buttons */}
        {getActionButtons()}

        {/* Support */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            ¿Preguntas sobre los planes? <a href="/contact" className="text-blue-600 hover:underline">Contacta soporte</a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function UnauthorizedPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>
    }>
      <UnauthorizedContent />
    </Suspense>
  );
}
