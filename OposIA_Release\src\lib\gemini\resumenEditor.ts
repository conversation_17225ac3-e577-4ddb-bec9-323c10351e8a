import { PROMPT_EDICION_RESUMENES } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';

/**
 * Edita un resumen existente usando IA para condensarlo y mejorarlo
 */
export async function editarResumen(contenidoOriginal: string): Promise<string> {
  try {
    // Validar entrada
    if (!contenidoOriginal || contenidoOriginal.trim().length === 0) {
      throw new Error("No se ha proporcionado contenido válido para editar.");
    }

    // Validar que el contenido no esté vacío
    if (contenidoOriginal.trim().length < 100) {
      throw new Error("El contenido del resumen es demasiado corto para ser editado.");
    }

    console.log('📝 Iniciando edición de resumen...');
    console.log(`📄 Longitud del contenido original: ${contenidoOriginal.length} caracteres`);

    // Construir el prompt final
    const finalPrompt = PROMPT_EDICION_RESUMENES.replace('{texto_largo_del_paso_1}', contenidoOriginal);

    // Obtener configuración específica para edición de resúmenes
    const config = getOpenAIConfig('RESUMENES'); // Usamos la misma configuración que para resúmenes

    console.log(`📄 Editando resumen con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Llamar a OpenAI para editar el resumen
    const resumenEditado = await llamarOpenAI(
      [{ role: 'user', content: finalPrompt }],
      {
        model: config.model,
        max_tokens: config.max_tokens,
        temperature: config.temperature,
        activityName: 'Edición de Resumen'
      }
    );

    if (!resumenEditado || resumenEditado.trim().length === 0) {
      throw new Error("La IA no pudo generar una edición válida del resumen.");
    }

    console.log('✅ Resumen editado exitosamente');
    console.log(`📄 Longitud del contenido editado: ${resumenEditado.length} caracteres`);
    
    return resumenEditado.trim();

  } catch (error) {
    console.error('❌ Error al editar resumen:', error);
    throw error;
  }
}
