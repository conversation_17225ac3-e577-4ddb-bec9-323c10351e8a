'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Eye, FiTrash2, FiEdit3 } from 'react-icons/fi';
import { Resumen, obtenerResumenes, eliminarResumen, guardarResumenEditado } from '@/lib/supabase/resumenesService';

import { markdownToHTML } from '@/utils/markdownToHTML';

interface SummaryListProps {
  refreshTrigger?: number;
}

export default function SummaryList({ refreshTrigger }: SummaryListProps) {
  const [resumenes, setResumenes] = useState<Resumen[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [resumenSeleccionado, setResumenSeleccionado] = useState<Resumen | null>(null);
  const [mostrarModal, setMostrarModal] = useState(false);
  const [editandoResumen, setEditandoResumen] = useState<string | null>(null);
  const [mostrarModalEdicion, setMostrarModalEdicion] = useState(false);
  const [versionAMostrar, setVersionAMostrar] = useState<'original' | 'editada'>('editada');


  const cargarResumenes = async () => {
    try {
      setIsLoading(true);
      const data = await obtenerResumenes();
      setResumenes(data);
    } catch (error) {
      console.error('Error al cargar resúmenes:', error);
      toast.error('Error al cargar los resúmenes');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    cargarResumenes();
  }, [refreshTrigger]);

  const handleEliminar = async (id: string, titulo: string) => {
    if (!confirm(`¿Estás seguro de que quieres eliminar el resumen "${titulo}"?`)) {
      return;
    }

    try {
      const success = await eliminarResumen(id);
      if (success) {
        toast.success('Resumen eliminado exitosamente');
        await cargarResumenes(); // Recargar la lista
      } else {
        toast.error('Error al eliminar el resumen');
      }
    } catch (error) {
      console.error('Error al eliminar resumen:', error);
      toast.error('Error al eliminar el resumen');
    }
  };

  const handleVerResumen = (resumen: Resumen) => {
    setResumenSeleccionado(resumen);
    // Si existe contenido editado, mostrarlo por defecto. Si no, el original.
    if (resumen.editado && resumen.contenido_editado) {
      setVersionAMostrar('editada');
    } else {
      setVersionAMostrar('original');
    }
    setMostrarModal(true);
  };

  const handleEditarResumen = async (resumen: Resumen) => {
    if (!confirm(`¿Estás seguro de que quieres editar el resumen "${resumen.titulo}"? Esta acción creará una versión condensada del resumen original.`)) {
      return;
    }

    try {
      setEditandoResumen(resumen.id);
      toast.loading('Editando resumen con IA...', { id: 'editing-summary' });

      // Usar el contenido editado si existe, sino el original
      const contenidoAEditar = resumen.contenido_editado || resumen.contenido;

      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'editarResumen',
          contextos: [contenidoAEditar]
        }),
      });

      if (!response.ok) {
        throw new Error('Error en la respuesta del servidor');
      }

      const data = await response.json();

      if (!data.result) {
        throw new Error('No se recibió resultado de la edición');
      }

      // Guardar la versión editada
      const success = await guardarResumenEditado(resumen.id, data.result);

      if (success) {
        toast.success('Resumen editado exitosamente', { id: 'editing-summary' });
        await cargarResumenes(); // Recargar la lista
      } else {
        throw new Error('Error al guardar el resumen editado');
      }

    } catch (error) {
      console.error('Error al editar resumen:', error);
      toast.error('Error al editar el resumen', { id: 'editing-summary' });
    } finally {
      setEditandoResumen(null);
    }
  };



  const handleImprimir = (resumen: Resumen) => {
    try {
      // Usar la versión que se está mostrando en el modal si está abierto,
      // o la mejor versión disponible (editada si existe) si se imprime desde la lista.
      let contenidoAImprimir: string;
      let versionInfo = '';

      if (mostrarModal && resumenSeleccionado && resumenSeleccionado.id === resumen.id) {
        // Imprimiendo desde el modal
        contenidoAImprimir = versionAMostrar === 'editada' && resumenSeleccionado.contenido_editado
          ? resumenSeleccionado.contenido_editado
          : resumenSeleccionado.contenido;
        versionInfo = versionAMostrar === 'editada' && resumenSeleccionado.contenido_editado ? ' (Versión Editada)' : '';
      } else {
        // Imprimiendo desde la lista (o si el modal no está abierto para este resumen)
        contenidoAImprimir = resumen.contenido_editado || resumen.contenido;
        versionInfo = resumen.contenido_editado ? ' (Versión Editada)' : '';
      }

      const htmlContent = markdownToHTML(
        contenidoAImprimir,
        resumen.titulo + versionInfo,
        {
          createdAt: resumen.creado_en,
          instructions: resumen.instrucciones || undefined,
          author: 'OposIA'
        }
      );

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${resumen.titulo}</title>
              <style>
                * { box-sizing: border-box; }
                body {
                  font-family: Arial, sans-serif;
                  margin: 20mm;
                  line-height: 1.6;
                  color: #333;
                  font-size: 12px;
                }
                h1 {
                  color: #2563eb;
                  border-bottom: 2px solid #2563eb;
                  padding-bottom: 10px;
                  margin: 0 0 20px 0;
                  font-size: 24px;
                  text-align: left;
                }
                h2 {
                  color: #1e40af;
                  border-bottom: 1px solid #cbd5e1;
                  padding-bottom: 5px;
                  margin: 25px 0 15px 0;
                  font-size: 20px;
                  text-align: left;
                }
                h3 {
                  color: #1e40af;
                  margin: 20px 0 10px 0;
                  font-size: 16px;
                  text-align: left;
                }
                p {
                  margin: 12px 0;
                  text-align: justify;
                  text-justify: inter-word;
                  hyphens: auto;
                  word-wrap: break-word;
                }
                strong {
                  color: #1e40af;
                  font-weight: bold;
                }
                em {
                  font-style: italic;
                  color: #64748b;
                }
                ul, ol {
                  margin: 12px 0;
                  padding-left: 20px;
                }
                li {
                  margin: 6px 0;
                  text-align: justify;
                }
                blockquote {
                  margin: 15px 0;
                  padding: 12px 15px;
                  background-color: #f8fafc;
                  border-left: 4px solid #2563eb;
                  font-style: italic;
                  text-align: justify;
                }
                .metadata {
                  font-size: 11px;
                  color: #64748b;
                  margin-bottom: 25px;
                  padding: 12px;
                  background-color: #f8fafc;
                  border-radius: 5px;
                  border: 1px solid #e2e8f0;
                }
                .metadata p {
                  margin: 4px 0;
                  text-align: left;
                }
                @media print {
                  body {
                    margin: 20mm;
                    font-size: 12px;
                  }
                  .metadata {
                    background-color: #f9f9f9;
                    border: 1px solid #ddd;
                  }
                  blockquote {
                    background-color: #f9f9f9;
                  }
                }
              </style>
            </head>
            <body>
              ${htmlContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      }
    } catch (error) {
      console.error('Error al imprimir:', error);
      toast.error('Error al preparar la impresión');
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Cargando resúmenes...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-800">📚 Mis Resúmenes</h3>
        <button
          onClick={cargarResumenes}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
          </svg>
          Actualizar
        </button>
      </div>

      {/* Información sobre la funcionalidad de edición */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <FiEdit3 className="h-5 w-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">✨ Función de Edición con IA</h4>
            <p className="text-sm text-blue-700 mt-1">
              Puedes usar la IA para condensar y refinar tus resúmenes. La edición mantiene toda la información esencial
              mientras elimina redundancias, creando un texto más conciso y estructurado (3.200-3.800 palabras).
              El resumen original se conserva siempre. Para acceder a ambos, haz clic en "Ver" en el resumen.
            </p>
          </div>
        </div>
      </div>

      {resumenes.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-gray-600 mb-2">No tienes resúmenes creados</p>
          <p className="text-sm text-gray-500">
            Selecciona un documento y genera tu primer resumen para empezar a estudiar de manera más eficiente.
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {resumenes.map((resumen) => (
            <div key={resumen.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{resumen.titulo}</h4>
                    {resumen.editado && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        ✨ Editado
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Creado: {formatearFecha(resumen.creado_en)}
                    {resumen.editado && resumen.fecha_edicion && (
                      <span className="ml-2 text-green-600">
                        • Editado: {formatearFecha(resumen.fecha_edicion)}
                      </span>
                    )}
                  </p>
                  {resumen.instrucciones && (
                    <p className="text-xs text-gray-500 mb-2 italic">
                      "{resumen.instrucciones.substring(0, 100)}{resumen.instrucciones.length > 100 ? '...' : ''}"
                    </p>
                  )}
                  <p className="text-xs text-gray-400">
                    Contenido original: ~{resumen.contenido.split(/\s+/).length} palabras
                    {resumen.contenido_editado && (
                      <span className="ml-2 text-green-600">
                        • Versión editada: ~{resumen.contenido_editado.split(/\s+/).length} palabras
                      </span>
                    )}
                  </p>
                </div>
                <div className="flex flex-wrap gap-2 ml-4">
                  <button
                    onClick={() => handleVerResumen(resumen)}
                    className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Ver resumen completo"
                  >
                    <FiEye className="w-3 h-3" />
                    Ver
                  </button>

                  <button
                    onClick={() => handleEditarResumen(resumen)}
                    disabled={editandoResumen === resumen.id}
                    className="flex items-center gap-1 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Editar resumen con IA"
                  >
                    <FiEdit3 className="w-3 h-3" />
                    {editandoResumen === resumen.id ? 'Editando...' : 'Editar'}
                  </button>

                  <button
                    onClick={() => handleImprimir(resumen)}
                    className="flex items-center gap-1 bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Imprimir resumen"
                  >
                    <FiPrinter className="w-3 h-3" />
                    Imprimir
                  </button>
                  <button
                    onClick={() => handleEliminar(resumen.id, resumen.titulo)}
                    className="flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded focus:outline-none focus:shadow-outline transition-colors"
                    title="Eliminar resumen"
                  >
                    <FiTrash2 className="w-3 h-3" />
                    Eliminar
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal para ver resumen completo */}
      {mostrarModal && resumenSeleccionado && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setMostrarModal(false)}
          />
          {/* Modal */}
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-11/12 max-w-4xl max-h-5/6 bg-white rounded-lg shadow-xl z-50 overflow-hidden">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">{resumenSeleccionado.titulo}</h3>
              <button
                onClick={() => setMostrarModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Selector de versión si hay contenido editado */}
            {resumenSeleccionado.editado && resumenSeleccionado.contenido_editado && (
              <div className="p-4 border-b border-gray-200 flex space-x-2">
                <button
                  onClick={() => setVersionAMostrar('editada')}
                  className={`px-3 py-1 rounded text-xs font-medium transition-colors
                    ${versionAMostrar === 'editada' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  title="Versión concisa refinada por IA"
                >
                  ✨ Versión Editada
                </button>
                <button
                  onClick={() => setVersionAMostrar('original')}
                  className={`px-3 py-1 rounded text-xs font-medium transition-colors
                    ${versionAMostrar === 'original' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  title="Primer borrador generado por IA"
                >
                  📜 Versión Original
                </button>
              </div>
            )}
            <div className="p-4 overflow-y-auto max-h-96">
              <div className="mb-4 text-sm text-gray-600">
                <p><strong>Creado:</strong> {formatearFecha(resumenSeleccionado.creado_en)}</p>
                {resumenSeleccionado.editado && resumenSeleccionado.fecha_edicion && (
                  <p><strong>Editado:</strong> {formatearFecha(resumenSeleccionado.fecha_edicion)}</p>
                )}
                {resumenSeleccionado.instrucciones && (
                  <p><strong>Instrucciones:</strong> {resumenSeleccionado.instrucciones}</p>
                )}
                {resumenSeleccionado.editado && resumenSeleccionado.contenido_editado && (
                  <div className="mt-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs">
                    {versionAMostrar === 'editada' ? (
                      <p className="text-green-800 font-medium">
                        ✨ Mostrando versión editada por IA (condensada y refinada).
                      </p>
                    ) : (
                      <p className="text-blue-800 font-medium">
                        📜 Mostrando versión original.
                      </p>
                    )}
                  </div>
                )}
              </div>
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{
                  __html: ((versionAMostrar === 'editada' && resumenSeleccionado.contenido_editado)
                            ? resumenSeleccionado.contenido_editado
                            : resumenSeleccionado.contenido)
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
                    .replace(/^\- (.*$)/gim, '<li>$1</li>')
                    .replace(/\n/g, '<br />')
                }}
              />
            </div>
            <div className="p-4 border-t border-gray-200 flex justify-between items-center">
              <div className="flex gap-2">

                <button
                  onClick={() => handleImprimir(resumenSeleccionado)}
                  className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors"
                >
                  <FiPrinter className="w-4 h-4" />
                  Imprimir Versión Actual
                </button>
              </div>
              <button
                onClick={() => setMostrarModal(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded focus:outline-none focus:shadow-outline transition-colors"
              >
                Cerrar
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
