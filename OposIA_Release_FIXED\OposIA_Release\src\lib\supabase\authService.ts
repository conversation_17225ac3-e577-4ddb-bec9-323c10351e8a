import { supabase } from './supabaseClient';
import { Session, User } from '@supabase/supabase-js';

/**
 * Inicia sesión con email y contraseña
 */
export async function iniciarSesion(email: string, password: string): Promise<{
  user: User | null;
  session: Session | null; // Added session
  error: string | null;
}> {
  try {
    // Verificar que el email y la contraseña no estén vacíos
    if (!email || !password) {
      return {
        user: null,
        session: null, // Added session
        error: 'Por favor, ingresa tu email y contraseña'
      };
    }

    // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.trim(),
      password: password,
    });

    if (error) {
      // Manejar específicamente el error de sincronización de tiempo
      if (error.message.includes('issued in the future') ||
          error.message.includes('clock for skew')) {
        return {
          user: null,
          session: null, // Added session
          error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'
        };
      }

      // Manejar error de credenciales inválidas de forma más amigable
      if (error.message.includes('Invalid login credentials')) {
        return {
          user: null,
          session: null, // Added session
          error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'
        };
      }

      return { user: null, session: null, error: error.message }; // Added session
    }

    // Ensure data.user and data.session exist before returning
    if (data && data.user && data.session) {
      // Esperar un momento para asegurar que las cookies se establezcan
      // Esto es importante para que el middleware pueda detectar la sesión
      await new Promise(resolve => setTimeout(resolve, 800));

      // Verificar que la sesión esté disponible después de establecer las cookies
      await supabase.auth.getSession();

      return { user: data.user, session: data.session, error: null }; // Added session
    } else {
      // This case should ideally not be reached if Supabase call is successful
      // but provides a fallback if data or its properties are unexpectedly null/undefined.
      return { user: null, session: null, error: 'Respuesta inesperada del servidor al iniciar sesión.' };
    }

  } catch (e: any) { // Changed 'error' to 'e' to avoid conflict with 'error' from signInWithPassword
    // Check if 'e' is an Error object and has a message property
    const errorMessage = (e instanceof Error && e.message) ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';
    return {
      user: null,
      session: null, // Added session
      error: errorMessage
    };
  }
}

/**
 * Cierra la sesión del usuario actual
 */
export async function cerrarSesion(): Promise<{ error: string | null }> {
  try {
    console.log('🔓 Iniciando proceso de logout...');

    // Cerrar sesión con scope 'global' para limpiar tanto local como servidor
    const { error } = await supabase.auth.signOut({ scope: 'global' });

    if (error) {
      console.error('❌ Error en signOut:', error.message);
      return { error: error.message };
    }

    console.log('✅ SignOut exitoso');

    // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage
    if (typeof window !== 'undefined') {
      console.log('🧹 Limpiando almacenamiento local...');

      // Limpiar localStorage de Supabase
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('sb-') || key.includes('supabase')) {
          console.log('🗑️ Eliminando localStorage:', key);
          localStorage.removeItem(key);
        }
      });

      // Limpiar sessionStorage de Supabase
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('sb-') || key.includes('supabase')) {
          console.log('🗑️ Eliminando sessionStorage:', key);
          sessionStorage.removeItem(key);
        }
      });

      // Limpiar todas las cookies de Supabase
      document.cookie.split(";").forEach(function(c) {
        const eqPos = c.indexOf("=");
        const name = eqPos > -1 ? c.substr(0, eqPos).trim() : c.trim();
        if (name.startsWith('sb-') || name.includes('supabase')) {
          console.log('🍪 Eliminando cookie:', name);
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname;
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
        }
      });

      console.log('✅ Limpieza de almacenamiento completada');
    }

    return { error: null };
  } catch (error) {
    console.error('❌ Error inesperado en logout:', error);
    return { error: 'Ha ocurrido un error inesperado al cerrar sesión' };
  }
}

/**
 * Obtiene la sesión actual del usuario
 */
export async function obtenerSesion(): Promise<{
  session: Session | null;
  error: string | null;
}> {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      // Si el error es "Auth session missing", es un caso esperado cuando no hay sesión
      if (error.message === 'Auth session missing!') {
        return { session: null, error: null };
      }

      return { session: null, error: error.message };
    }

    return { session: data.session, error: null };
  } catch (error) {
    return {
      session: null,
      error: 'Ha ocurrido un error inesperado al obtener la sesión'
    };
  }
}

/**
 * Obtiene el usuario actual
 */
export async function obtenerUsuarioActual(): Promise<{
  user: User | null;
  error: string | null;
}> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      // Si el error es "Auth session missing", es un caso esperado cuando no hay sesión
      if (error.message === 'Auth session missing!') {
        return { user: null, error: null };
      }

      return { user: null, error: error.message };
    }

    return { user, error: null };
  } catch (error) {
    return {
      user: null,
      error: 'Ha ocurrido un error inesperado al obtener el usuario actual'
    };
  }
}

/**
 * Verifica si el usuario está autenticado
 */
export async function estaAutenticado(): Promise<boolean> {
  const { session } = await obtenerSesion();
  return session !== null;
}
