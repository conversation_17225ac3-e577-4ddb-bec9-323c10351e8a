// src/features/privacy/types/cookie.types.ts
// Tipos TypeScript para la gestión de cookies y consentimiento

export interface CookieConsent {
  granted: boolean;
  timestamp: Date;
  version: string;
}

export interface CookiePreferences {
  functional: boolean;
  analytics?: boolean;
  marketing?: boolean;
}

export interface CookieConsentState {
  hasConsent: boolean;
  preferences: CookiePreferences;
  lastUpdated: Date | null;
}

export type CookieConsentStatus = 'pending' | 'granted' | 'denied';

export interface CookieStorageData {
  consent: CookieConsent;
  preferences: CookiePreferences;
}
