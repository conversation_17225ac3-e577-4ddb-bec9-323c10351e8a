// src/features/privacy/services/CookieStorageRepository.ts
// Repository Pattern para abstracción del almacenamiento de cookies

import { CookieConsent, CookiePreferences, CookieStorageData } from '../types/cookie.types';
import { 
  COOKIE_CONSENT_KEY, 
  COOKIE_PREFERENCES_KEY, 
  DEFAULT_COOKIE_PREFERENCES 
} from '../constants/cookie.constants';

export interface CookieStorageRepository {
  getConsent(): Promise<CookieConsent | null>;
  setConsent(consent: CookieConsent): Promise<void>;
  clearConsent(): Promise<void>;
  getPreferences(): Promise<CookiePreferences>;
  setPreferences(preferences: CookiePreferences): Promise<void>;
}

export class LocalStorageCookieRepository implements CookieStorageRepository {
  private isClient(): boolean {
    return typeof window !== 'undefined';
  }

  async getConsent(): Promise<CookieConsent | null> {
    if (!this.isClient()) return null;
    
    try {
      const stored = localStorage.getItem(COOKIE_CONSENT_KEY);
      if (!stored) return null;
      
      const parsed = JSON.parse(stored);
      return {
        ...parsed,
        timestamp: new Date(parsed.timestamp)
      };
    } catch (error) {
      console.error('Error reading cookie consent from localStorage:', error);
      return null;
    }
  }

  async setConsent(consent: CookieConsent): Promise<void> {
    if (!this.isClient()) return;
    
    try {
      localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consent));
    } catch (error) {
      console.error('Error saving cookie consent to localStorage:', error);
    }
  }

  async clearConsent(): Promise<void> {
    if (!this.isClient()) return;
    
    try {
      localStorage.removeItem(COOKIE_CONSENT_KEY);
      localStorage.removeItem(COOKIE_PREFERENCES_KEY);
    } catch (error) {
      console.error('Error clearing cookie consent from localStorage:', error);
    }
  }

  async getPreferences(): Promise<CookiePreferences> {
    if (!this.isClient()) return DEFAULT_COOKIE_PREFERENCES;
    
    try {
      const stored = localStorage.getItem(COOKIE_PREFERENCES_KEY);
      if (!stored) return DEFAULT_COOKIE_PREFERENCES;
      
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error reading cookie preferences from localStorage:', error);
      return DEFAULT_COOKIE_PREFERENCES;
    }
  }

  async setPreferences(preferences: CookiePreferences): Promise<void> {
    if (!this.isClient()) return;
    
    try {
      localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error saving cookie preferences to localStorage:', error);
    }
  }
}
