// src/features/profile/components/AccountInfo.tsx
// Componente para mostrar y editar información básica de la cuenta

'use client';

import React, { useState } from 'react';
import { 
  FiUser, 
  FiMail, 
  FiCalendar, 
  FiCreditCard, 
  FiEdit2, 
  FiSave, 
  FiX,
  FiCheck,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';

interface UserProfile {
  user: {
    id: string;
    email: string;
    name: string;
    created_at: string;
  };
  profile: {
    subscription_plan: string;
    plan_expires_at?: string;
    auto_renew: boolean;
    payment_verified: boolean;
    last_payment_date?: string;
  };
  access: {
    plan: string;
    features: string[];
    limits: any;
    currentUsage: any;
    paymentVerified: boolean;
  };
}

interface AccountInfoProps {
  userProfile: UserProfile;
  onUpdate: () => void;
}

export default function AccountInfo({ userProfile, onUpdate }: AccountInfoProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(userProfile.user.name);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editedName,
        }),
      });

      if (!response.ok) {
        throw new Error('Error actualizando perfil');
      }

      setSuccess(true);
      setIsEditing(false);
      onUpdate();

      // Limpiar mensaje de éxito después de 3 segundos
      setTimeout(() => setSuccess(false), 3000);

    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedName(userProfile.user.name);
    setIsEditing(false);
    setError(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPlanDisplayName = (plan: string) => {
    switch (plan) {
      case 'free': return 'Plan Gratuito';
      case 'usuario': return 'Plan Usuario';
      case 'pro': return 'Plan Pro';
      default: return plan;
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'usuario': return 'bg-blue-100 text-blue-800';
      case 'pro': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Información de la Cuenta</h2>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            <FiEdit2 className="w-4 h-4 mr-1" />
            Editar
          </button>
        )}
      </div>

      {/* Mensajes de estado */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
          <FiAlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
          <FiCheck className="w-5 h-5 text-green-500 mr-2" />
          <span className="text-green-700">Perfil actualizado correctamente</span>
        </div>
      )}

      <div className="space-y-6">
        {/* Información Personal */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Información Personal</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nombre */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiUser className="w-4 h-4 inline mr-1" />
                Nombre
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Tu nombre"
                />
              ) : (
                <p className="text-gray-900">{userProfile.user.name}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiMail className="w-4 h-4 inline mr-1" />
                Email
              </label>
              <p className="text-gray-900">{userProfile.user.email}</p>
              <p className="text-xs text-gray-500 mt-1">El email no se puede modificar</p>
            </div>

            {/* Fecha de registro */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiCalendar className="w-4 h-4 inline mr-1" />
                Miembro desde
              </label>
              <p className="text-gray-900">{formatDate(userProfile.user.created_at)}</p>
            </div>

            {/* ID de usuario */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID de Usuario
              </label>
              <p className="text-gray-600 font-mono text-sm">{userProfile.user.id}</p>
            </div>
          </div>

          {/* Botones de edición */}
          {isEditing && (
            <div className="flex items-center gap-3 mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <FiSave className="w-4 h-4 mr-2" />
                )}
                {saving ? 'Guardando...' : 'Guardar'}
              </button>
              <button
                onClick={handleCancel}
                disabled={saving}
                className="flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors disabled:opacity-50"
              >
                <FiX className="w-4 h-4 mr-2" />
                Cancelar
              </button>
            </div>
          )}
        </div>

        {/* Información de Suscripción */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Información de Suscripción</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Plan actual */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiCreditCard className="w-4 h-4 inline mr-1" />
                Plan Actual
              </label>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPlanColor(userProfile.profile.subscription_plan)}`}>
                {getPlanDisplayName(userProfile.profile.subscription_plan)}
              </span>
            </div>

            {/* Estado de pago */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estado de Pago
              </label>
              <div className="flex items-center">
                {userProfile.profile.payment_verified ? (
                  <>
                    <FiCheck className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-green-700">Verificado</span>
                  </>
                ) : (
                  <>
                    <FiClock className="w-4 h-4 text-yellow-500 mr-1" />
                    <span className="text-yellow-700">Pendiente</span>
                  </>
                )}
              </div>
            </div>

            {/* Renovación automática */}
            {userProfile.profile.subscription_plan !== 'free' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Renovación Automática
                </label>
                <div className="flex items-center">
                  {userProfile.profile.auto_renew ? (
                    <>
                      <FiCheck className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-green-700">Activa</span>
                    </>
                  ) : (
                    <>
                      <FiX className="w-4 h-4 text-red-500 mr-1" />
                      <span className="text-red-700">Inactiva</span>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Fecha de expiración */}
            {userProfile.profile.plan_expires_at && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {userProfile.profile.auto_renew ? 'Próxima Renovación' : 'Expira el'}
                </label>
                <p className="text-gray-900">{formatDate(userProfile.profile.plan_expires_at)}</p>
              </div>
            )}

            {/* Último pago */}
            {userProfile.profile.last_payment_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Último Pago
                </label>
                <p className="text-gray-900">{formatDate(userProfile.profile.last_payment_date)}</p>
              </div>
            )}
          </div>

          {/* Acciones de plan */}
          {userProfile.profile.subscription_plan === 'free' && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <a
                href="/upgrade-plan"
                className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                <FiCreditCard className="w-4 h-4 mr-2" />
                Actualizar Plan
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
