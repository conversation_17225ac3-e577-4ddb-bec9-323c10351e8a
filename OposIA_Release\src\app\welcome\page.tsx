// src/app/welcome/page.tsx
// Página de bienvenida para usuarios recién registrados

'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';
import { getPlanConfiguration } from '@/config/plans';

interface UserInfo {
  name: string;
  email: string;
  plan: string;
  planName: string;
  features: string[];
  tokenLimit: number;
}

function WelcomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        const supabase = createClient();
        
        // Obtener usuario actual
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError || !user) {
          router.push('/login');
          return;
        }
        
        // Obtener perfil del usuario
        const { data: profile, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', user.id)
          .single();
        
        if (profileError || !profile) {
          router.push('/payment');
          return;
        }
        
        // Obtener configuración del plan
        const planConfig = getPlanConfiguration(profile.subscription_plan);
        
        if (!planConfig) {
          router.push('/payment');
          return;
        }
        
        setUserInfo({
          name: user.user_metadata?.name || user.email?.split('@')[0] || 'Usuario',
          email: user.email || '',
          plan: profile.subscription_plan,
          planName: planConfig.name,
          features: planConfig.features,
          tokenLimit: profile.monthly_token_limit
        });
        
        setLoading(false);
        
      } catch (error) {
        console.error('Error loading user info:', error);
        router.push('/login');
      }
    };

    loadUserInfo();
  }, [router]);

  const handleContinue = () => {
    router.push('/app');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!userInfo) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-2xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            ¡Bienvenido a OposIA, {userInfo.name}!
          </h1>
          <p className="text-gray-600">
            Tu cuenta ha sido activada exitosamente
          </p>
        </div>

        {/* Plan Info */}
        <div className="bg-blue-50 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-blue-800 mb-3">
            Tu Plan: {userInfo.planName}
          </h2>
          
          {userInfo.plan !== 'free' && (
            <div className="mb-4">
              <p className="text-blue-700 mb-2">
                <strong>Límite mensual de tokens:</strong> {userInfo.tokenLimit.toLocaleString()}
              </p>
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
              <p className="text-sm text-blue-600 mt-1">0 tokens utilizados este mes</p>
            </div>
          )}
          
          <h3 className="font-semibold text-blue-800 mb-2">Características incluidas:</h3>
          <ul className="space-y-1">
            {userInfo.features.map((feature, index) => (
              <li key={index} className="flex items-center text-blue-700">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                {getFeatureDisplayName(feature)}
              </li>
            ))}
          </ul>
        </div>

        {/* Quick Start Guide */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Primeros pasos</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">1. Sube tus documentos</h3>
              <p className="text-gray-600 text-sm">
                Comienza subiendo tus materiales de estudio en formato PDF o TXT
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">2. Genera contenido</h3>
              <p className="text-gray-600 text-sm">
                Crea tests, flashcards y mapas mentales basados en tus documentos
              </p>
            </div>
            {userInfo.plan !== 'free' && (
              <>
                <div className="border border-gray-200 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-800 mb-2">3. Chatea con la IA</h3>
                  <p className="text-gray-600 text-sm">
                    Haz preguntas específicas sobre tu temario a tu preparador IA
                  </p>
                </div>
                {userInfo.plan === 'pro' && (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-2">4. Planifica tu estudio</h3>
                    <p className="text-gray-600 text-sm">
                      Crea un plan de estudios personalizado con IA
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={handleContinue}
            className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors font-semibold"
          >
            Comenzar a usar OposIA
          </button>
          <button
            onClick={() => router.push('/app/profile')}
            className="flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors"
          >
            Ver mi perfil
          </button>
        </div>

        {/* Support */}
        <div className="mt-6 pt-4 border-t border-gray-200 text-center">
          <p className="text-sm text-gray-500">
            ¿Necesitas ayuda? <a href="/contact" className="text-blue-600 hover:underline">Contacta nuestro soporte</a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function WelcomePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    }>
      <WelcomeContent />
    </Suspense>
  );
}

import { getFeatureDisplayName } from '@/config/features';
