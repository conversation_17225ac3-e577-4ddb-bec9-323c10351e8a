// ===== Archivo: src/middleware.ts (VERSIÓN FINAL Y RECOMENDADA) =====

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  // Crea un cliente de Supabase para este request específico.
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options })
          response = NextResponse.next({
            request: { headers: request.headers },
          })
          response.cookies.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: '', ...options })
          response = NextResponse.next({
            request: { headers: request.headers },
          })
          response.cookies.set({ name, value: '', ...options })
        },
      },
    }
  )

  // Esta llamada es la más importante. `getSession()` (o `getUser()`)
  // lee las cookies, refresca la sesión si es necesario, y actualiza las
  // cookies en el objeto 'response' para mantener todo sincronizado.
  await supabase.auth.getSession()

  // Devuelve la respuesta, que ahora contiene las cookies de sesión actualizadas.
  return response
}

export const config = {
  matcher: [
    /*
     * Coincide con todas las rutas de petición excepto las de archivos estáticos
     * y de optimización de imágenes.
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
