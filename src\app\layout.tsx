import type { Metadata } from 'next';
import './globals.css';
import ClientLayout from '@/features/shared/components/ClientLayout';

export const metadata: Metadata = {
  title: 'OposIA - Asistente IA para Oposiciones',
  description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/logo.png', type: 'image/png' },
    ],
    apple: [
      { url: '/icon-192.png', sizes: '192x192', type: 'image/png' },
    ],
  },
  manifest: '/manifest.json',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body className="font-sans bg-gray-100">
        <ClientLayout>
          {children}
        </ClientLayout>
      </body>
    </html>
  );
}
