'use client';

import React, { useState, useEffect } from 'react';
import {
  FiBook,
  FiFileText,
  FiCheckSquare,
  FiCalendar,
  FiMessageSquare,
  FiLayers,
  FiSettings,
  FiChevronDown,
  FiChevronRight,
  FiRefreshCw,
  FiPlus,
  FiList,
  FiCreditCard,
  FiTarget,
  FiBookOpen,
  FiMenu,
  FiChevronsLeft
} from 'react-icons/fi';

import { TabType, MenuItem, SidebarMenuProps } from '@/types/ui';

const SidebarMenu: React.FC<SidebarMenuProps> = ({ activeTab, onTabChange, children }) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  // Cargar estado colapsado desde localStorage
  useEffect(() => {
    const savedCollapsedState = localStorage.getItem('sidebarCollapsed');
    if (savedCollapsedState) {
      setIsCollapsed(JSON.parse(savedCollapsedState));
    }
  }, []);

  // Guardar estado colapsado en localStorage
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Principal',
      icon: <FiRefreshCw />,
      color: 'bg-gradient-to-r from-blue-600 to-purple-600'
    },
    {
      id: 'mi-temario-group',
      label: 'Mi Temario',
      icon: <FiBook />,
      color: 'bg-green-600',
      isGroup: true,
      children: [
        {
          id: 'temario',
          label: 'Mi Temario',
          icon: <FiBook />,
          color: 'bg-green-600'
        },
        {
          id: 'gestionar',
          label: 'Gestionar Documentos',
          icon: <FiSettings />,
          color: 'bg-gray-600'
        }
      ]
    },
    {
      id: 'planEstudios',
      label: 'Mi Plan de Estudios',
      icon: <FiCalendar />,
      color: 'bg-teal-600'
    },
    {
      id: 'preguntas',
      label: 'Habla con tu preparador',
      icon: <FiMessageSquare />,
      color: 'bg-blue-600'
    },
    {
      id: 'herramientas-group',
      label: 'Herramientas de estudio',
      icon: <FiTarget />,
      color: 'bg-purple-600',
      isGroup: true,
      children: [
        {
          id: 'flashcards-group',
          label: 'Flashcards',
          icon: <FiCreditCard />,
          color: 'bg-orange-500',
          isGroup: true,
          children: [
            {
              id: 'flashcards',
              label: 'Generador de Flashcards',
              icon: <FiPlus />,
              color: 'bg-orange-500'
            },
            {
              id: 'misFlashcards',
              label: 'Mis Flashcards',
              icon: <FiCreditCard />,
              color: 'bg-emerald-600'
            }
          ]
        },
        {
          id: 'tests-group',
          label: 'Tests',
          icon: <FiCheckSquare />,
          color: 'bg-indigo-600',
          isGroup: true,
          children: [
            {
              id: 'tests',
              label: 'Generador de Tests',
              icon: <FiPlus />,
              color: 'bg-indigo-600'
            },
            {
              id: 'misTests',
              label: 'Mis Tests',
              icon: <FiCheckSquare />,
              color: 'bg-pink-600'
            }
          ]
        }
      ]
    },
    {
      id: 'resumenes',
      label: 'Resúmenes',
      icon: <FiBookOpen />,
      color: 'bg-green-600'
    },
    {
      id: 'mapas',
      label: 'Mapas Mentales',
      icon: <FiLayers />,
      color: 'bg-purple-600'
    }
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isExpanded = (itemId: string) => expandedItems.includes(itemId);

  const collapseAllMenus = () => {
    setExpandedItems([]);
  };

  const toggleSidebarCollapse = () => {
    setIsCollapsed(prev => {
      const newState = !prev;
      // Si se colapsa el sidebar, también colapsar todos los submenús
      if (newState) {
        setExpandedItems([]);
      }
      return newState;
    });
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = activeTab === item.id && !item.isGroup;
    const expanded = isExpanded(item.id as string);

    // En modo colapsado, no mostrar submenús anidados (level > 0)
    if (isCollapsed && level > 0) {
      return null;
    }

    return (
      <div key={`${item.id}-${level}`}>
        <div
          className={`flex items-center ${isCollapsed ? 'justify-center tooltip-hover' : 'justify-between'} px-${isCollapsed ? '2' : (2 + level * 2)} py-2 rounded-lg transition-all duration-300 cursor-pointer ${
            isActive && !hasChildren
              ? `text-white ${item.color} shadow-md`
              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
          }`}
          onClick={() => {
            if (isCollapsed && hasChildren) {
              // En modo colapsado, expandir el sidebar al hacer clic en un grupo
              setIsCollapsed(false);
              toggleExpanded(item.id as string);
            } else if (hasChildren || item.isGroup) {
              toggleExpanded(item.id as string);
            } else {
              collapseAllMenus();
              onTabChange(item.id as TabType);
            }
          }}
          title={isCollapsed ? item.label : undefined}
          data-tooltip={isCollapsed ? item.label : undefined}
          aria-label={item.label}
        >
          <div className="flex items-center">
            <span className={`${isCollapsed ? '' : 'mr-2'} text-sm`}>{item.icon}</span>
            {!isCollapsed && <span className="text-sm font-medium">{item.label}</span>}
          </div>
          {hasChildren && !isCollapsed && (
            <span className="text-xs">
              {expanded ? <FiChevronDown /> : <FiChevronRight />}
            </span>
          )}
        </div>

        {hasChildren && expanded && !isCollapsed && (
          <div className="ml-2 mt-1 space-y-1">
            {item.children!.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`${isCollapsed ? 'w-16' : 'w-80'} flex-shrink-0 space-y-4 sidebar-transition`}>
      <div className="bg-white rounded-xl shadow-sm p-4 sticky top-6">
        {/* Botón toggle */}
        <div className="flex items-center justify-between mb-4">
          {!isCollapsed && (
            <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wider px-2">
              Menú de Estudio
            </h2>
          )}
          <button
            onClick={toggleSidebarCollapse}
            className="p-1 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            title={isCollapsed ? 'Expandir menú' : 'Colapsar menú'}
            aria-label={isCollapsed ? 'Expandir menú de navegación' : 'Colapsar menú de navegación'}
            aria-expanded={!isCollapsed}
          >
            {isCollapsed ? <FiMenu className="w-4 h-4 text-gray-600" /> : <FiChevronsLeft className="w-4 h-4 text-gray-600" />}
          </button>
        </div>

        <nav className="space-y-1">
          {menuItems.map(item => renderMenuItem(item))}
        </nav>
      </div>
      {!isCollapsed && children}
    </div>
  );
};

export default SidebarMenu;
