import React, { useState, useEffect } from 'react';
import { FiTrash2, FiFileText, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import { Documento, obtenerDocumentos, eliminarDocumento } from '../../../lib/supabase';
import toast from 'react-hot-toast';

interface DocumentManagerProps {
  onDocumentDeleted?: () => void;
}

export default function DocumentManager({ onDocumentDeleted }: DocumentManagerProps) {
  const [documentos, setDocumentos] = useState<Documento[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);

  const cargarDocumentos = async () => {
    setIsLoading(true);
    try {
      const docs = await obtenerDocumentos();
      setDocumentos(docs);
    } catch (error) {
      console.error('Error al cargar documentos:', error);
      toast.error('Error al cargar documentos');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    cargarDocumentos();
  }, []);

  const handleEliminarDocumento = async (documentoId: string) => {
    setDeletingId(documentoId);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Eliminando documento...');
      
      const success = await eliminarDocumento(documentoId);
      
      if (success) {
        toast.success('Documento eliminado exitosamente', { id: loadingToastId });
        // Actualizar la lista local
        setDocumentos(prev => prev.filter(doc => doc.id !== documentoId));
        // Notificar al componente padre
        onDocumentDeleted?.();
      } else {
        toast.error('Error al eliminar el documento', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error al eliminar documento:', error);
      toast.error('Error al eliminar el documento', { id: loadingToastId });
    } finally {
      setDeletingId(null);
      setShowConfirmDialog(null);
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <FiRefreshCw className="animate-spin text-blue-500 mr-2" />
        <span>Cargando documentos...</span>
      </div>
    );
  }

  if (documentos.length === 0) {
    return (
      <div className="text-center p-8 text-gray-500">
        <FiFileText className="mx-auto text-4xl mb-4" />
        <p>No hay documentos subidos aún.</p>
        <p className="text-sm">Sube tu primer documento para comenzar.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        Gestionar Documentos ({documentos.length})
      </h3>
      
      <div className="space-y-3">
        {documentos.map((documento) => (
          <div
            key={documento.id}
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <FiFileText className="text-blue-500 mr-2 flex-shrink-0" />
                  <h4 className="font-medium text-gray-900 truncate">
                    {documento.titulo}
                  </h4>
                  {documento.numero_tema && (
                    <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Tema {documento.numero_tema}
                    </span>
                  )}
                  {documento.categoria && (
                    <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                      {documento.categoria}
                    </span>
                  )}
                </div>
                
                <div className="text-sm text-gray-600">
                  <p>Subido: {formatearFecha(documento.creado_en)}</p>
                  <p>Caracteres: {documento.contenido.length.toLocaleString()}</p>
                  {documento.tipo_original && (
                    <p>Tipo: {documento.tipo_original.toUpperCase()}</p>
                  )}
                </div>
              </div>
              
              <button
                onClick={() => setShowConfirmDialog(documento.id)}
                disabled={deletingId === documento.id}
                className="ml-4 p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                title="Eliminar documento"
              >
                {deletingId === documento.id ? (
                  <FiRefreshCw className="animate-spin" />
                ) : (
                  <FiTrash2 />
                )}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Diálogo de confirmación */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <FiAlertTriangle className="text-red-500 mr-3" />
              <h3 className="text-lg font-semibold">Confirmar eliminación</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              ¿Estás seguro de que quieres eliminar este documento? Esta acción no se puede deshacer.
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={() => handleEliminarDocumento(showConfirmDialog)}
                className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors"
              >
                Eliminar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
