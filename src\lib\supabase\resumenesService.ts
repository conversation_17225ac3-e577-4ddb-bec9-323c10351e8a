import { supabase, Resumen } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

// Exportar el tipo Resumen para uso en otros archivos
export type { Resumen };

/**
 * Obtiene todos los resúmenes del usuario actual
 */
export async function obtenerResumenes(): Promise<Resumen[]> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('resumenes')
      .select('*')
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener resúmenes:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener resúmenes:', error);
    return [];
  }
}

/**
 * Obtiene un resumen específico por ID
 */
export async function obtenerResumenPorId(id: string): Promise<Resumen | null> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('resumenes')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener resumen:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener resumen:', error);
    return null;
  }
}

/**
 * Verifica si ya existe un resumen para un documento específico
 */
export async function existeResumenParaDocumento(documentoId: string): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.log('No hay usuario autenticado para verificar resumen');
      return false;
    }

    console.log(`Verificando si existe resumen para documento ${documentoId} y usuario ${user.id}`);

    const { data, error } = await supabase
      .from('resumenes')
      .select('id')
      .eq('user_id', user.id)
      .eq('documento_id', documentoId)
      .maybeSingle(); // Usar maybeSingle() en lugar de single() para evitar errores cuando no hay resultados

    if (error) {
      console.error('Error al verificar resumen existente:', error);
      return false;
    }

    const existe = !!data;
    console.log(`Resultado verificación resumen: ${existe ? 'existe' : 'no existe'}`);
    return existe;
  } catch (error) {
    console.error('Error al verificar resumen existente:', error);
    return false;
  }
}

/**
 * Guarda un nuevo resumen en la base de datos
 */
export async function guardarResumen(
  documentoId: string,
  titulo: string,
  contenido: string,
  instrucciones?: string
): Promise<string | null> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado para guardar resumen');
      return null;
    }

    console.log(`Intentando guardar resumen para documento ${documentoId} y usuario ${user.id}`);

    // Verificar si ya existe un resumen para este documento
    const existe = await existeResumenParaDocumento(documentoId);
    if (existe) {
      console.error('Ya existe un resumen para este documento');
      return null;
    }

    const resumenData = {
      user_id: user.id,
      documento_id: documentoId,
      titulo: titulo.trim(),
      contenido: contenido.trim(),
      instrucciones: instrucciones?.trim() || null
    };

    console.log('Datos del resumen a insertar:', {
      ...resumenData,
      contenido: `${resumenData.contenido.substring(0, 100)}...` // Solo mostrar los primeros 100 caracteres
    });

    const { data, error } = await supabase
      .from('resumenes')
      .insert([resumenData])
      .select()
      .single();

    if (error) {
      console.error('Error al guardar resumen en Supabase:', error);
      return null;
    }

    if (!data?.id) {
      console.error('No se recibió ID del resumen guardado');
      return null;
    }

    console.log(`Resumen guardado exitosamente con ID: ${data.id}`);
    return data.id;
  } catch (error) {
    console.error('Error al guardar resumen:', error);
    return null;
  }
}

/**
 * Actualiza un resumen existente
 */
export async function actualizarResumen(
  id: string,
  titulo: string,
  contenido: string,
  instrucciones?: string
): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return false;
    }

    const { error } = await supabase
      .from('resumenes')
      .update({
        titulo,
        contenido,
        instrucciones,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error al actualizar resumen:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al actualizar resumen:', error);
    return false;
  }
}

/**
 * Elimina un resumen
 */
export async function eliminarResumen(id: string): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return false;
    }

    const { error } = await supabase
      .from('resumenes')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error al eliminar resumen:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al eliminar resumen:', error);
    return false;
  }
}

/**
 * Guarda la versión editada de un resumen
 */
export async function guardarResumenEditado(
  resumenId: string,
  contenidoEditado: string
): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado para guardar resumen editado');
      return false;
    }

    console.log(`Guardando versión editada del resumen ${resumenId}`);

    const { error } = await supabase
      .from('resumenes')
      .update({
        contenido_editado: contenidoEditado.trim(),
        editado: true,
        fecha_edicion: new Date().toISOString(),
        actualizado_en: new Date().toISOString()
      })
      .eq('id', resumenId)
      .eq('user_id', user.id); // Verificar que el resumen pertenece al usuario

    if (error) {
      console.error('Error al guardar resumen editado en Supabase:', error);
      return false;
    }

    console.log('✅ Resumen editado guardado exitosamente');
    return true;

  } catch (error) {
    console.error('Error al guardar resumen editado:', error);
    return false;
  }
}
