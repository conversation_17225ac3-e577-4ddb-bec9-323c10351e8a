// src/features/privacy/hooks/useCookiePreferences.ts
// Hook para gestión específica de preferencias de cookies

'use client';

import { useState, useEffect, useCallback } from 'react';
import { CookiePreferences } from '../types/cookie.types';
import { CookieConsentService } from '../services/CookieConsentService';
import { LocalStorageCookieRepository } from '../services/CookieStorageRepository';
import { DEFAULT_COOKIE_PREFERENCES } from '../constants/cookie.constants';

// Instancia singleton del servicio
const cookieRepository = new LocalStorageCookieRepository();
const cookieConsentService = new CookieConsentService(cookieRepository);

export function useCookiePreferences() {
  const [preferences, setPreferences] = useState<CookiePreferences>(DEFAULT_COOKIE_PREFERENCES);
  const [isLoading, setIsLoading] = useState(true);

  // <PERSON>gar preferencias iniciales
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const currentPreferences = await cookieRepository.getPreferences();
        setPreferences(currentPreferences);
      } catch (error) {
        console.error('Error loading cookie preferences:', error);
        setPreferences(DEFAULT_COOKIE_PREFERENCES);
      } finally {
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, []);

  const updatePreferences = useCallback(async (newPreferences: Partial<CookiePreferences>) => {
    try {
      const updatedPreferences = { ...preferences, ...newPreferences };
      await cookieConsentService.updatePreferences(updatedPreferences);
      setPreferences(updatedPreferences);
    } catch (error) {
      console.error('Error updating cookie preferences:', error);
    }
  }, [preferences]);

  const resetPreferences = useCallback(async () => {
    try {
      await cookieConsentService.updatePreferences(DEFAULT_COOKIE_PREFERENCES);
      setPreferences(DEFAULT_COOKIE_PREFERENCES);
    } catch (error) {
      console.error('Error resetting cookie preferences:', error);
    }
  }, []);

  const togglePreference = useCallback(async (type: keyof CookiePreferences) => {
    if (type === 'functional') {
      // Las cookies funcionales no se pueden desactivar
      return;
    }

    const newValue = !preferences[type];
    await updatePreferences({ [type]: newValue });
  }, [preferences, updatePreferences]);

  return {
    preferences,
    isLoading,
    updatePreferences,
    resetPreferences,
    togglePreference,
  };
}
