# Guía de Testing - OposiAI

Esta guía proporciona estrategias y patrones para testing en el proyecto OposiAI.

## 📋 Tabla de Contenidos

- [Filosofía de Testing](#filosofía-de-testing)
- [Tipos de Tests](#tipos-de-tests)
- [Configuración](#configuración)
- [Patrones de Testing](#patrones-de-testing)
- [Mocking](#mocking)
- [Coverage](#coverage)
- [CI/CD](#cicd)

## 🎯 Filosofía de Testing

### Principios

1. **Testing Pyramid**: Más tests unitarios, menos tests de integración, pocos tests E2E
2. **Test-Driven Development (TDD)**: Escribir tests antes que el código cuando sea posible
3. **Behavior-Driven Testing**: Tests que describen comportamiento, no implementación
4. **Fast Feedback**: Tests rápidos que se ejecuten frecuentemente
5. **Reliable Tests**: Tests determinísticos que no fallen aleatoriamente

### Estrategia de Testing

```
        /\
       /  \
      / E2E \     ← Pocos, lentos, costosos
     /______\
    /        \
   / Integration \  ← Algunos, moderados
  /______________\
 /                \
/ Unit Tests       \  ← Muchos, rápidos, baratos
/____________________\
```

## 🧪 Tipos de Tests

### 1. Unit Tests

**Objetivo**: Probar funciones, hooks y componentes de forma aislada.

```typescript
// Ejemplo: Test de utilidad
import { formatDate } from '@/lib/utils/dateUtils';

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2025-01-15T10:30:00Z');
    const result = formatDate(date, 'dd/MM/yyyy');
    expect(result).toBe('15/01/2025');
  });

  it('should handle invalid dates', () => {
    const result = formatDate(null, 'dd/MM/yyyy');
    expect(result).toBe('Invalid Date');
  });
});
```

### 2. Component Tests

**Objetivo**: Probar componentes React con sus props y interacciones.

```typescript
// Ejemplo: Test de componente
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from './Button';

describe('Button', () => {
  it('should render with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('should call onClick when clicked', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    await user.click(screen.getByRole('button'));
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when loading', () => {
    render(<Button loading>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

### 3. Hook Tests

**Objetivo**: Probar hooks personalizados de forma aislada.

```typescript
// Ejemplo: Test de hook
import { renderHook, act } from '@testing-library/react';
import { useCounter } from './useCounter';

describe('useCounter', () => {
  it('should initialize with default value', () => {
    const { result } = renderHook(() => useCounter());
    expect(result.current.count).toBe(0);
  });

  it('should increment count', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });

  it('should initialize with custom value', () => {
    const { result } = renderHook(() => useCounter(10));
    expect(result.current.count).toBe(10);
  });
});
```

### 4. Service Tests

**Objetivo**: Probar servicios y lógica de negocio.

```typescript
// Ejemplo: Test de servicio
import { authService } from './authService';
import { createClient } from '@/lib/supabase/supabaseClient';

jest.mock('@/lib/supabase/supabaseClient');

describe('authService', () => {
  const mockSupabase = {
    auth: {
      signInWithPassword: jest.fn(),
      signOut: jest.fn()
    }
  };

  beforeEach(() => {
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
    jest.clearAllMocks();
  });

  describe('signIn', () => {
    it('should sign in user successfully', async () => {
      const mockUser = { id: '123', email: '<EMAIL>' };
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });

      const result = await authService.signIn('<EMAIL>', 'password');

      expect(result.user).toEqual(mockUser);
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password'
      });
    });

    it('should handle sign in error', async () => {
      const mockError = new Error('Invalid credentials');
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: mockError
      });

      await expect(
        authService.signIn('<EMAIL>', 'wrong-password')
      ).rejects.toThrow('Invalid credentials');
    });
  });
});
```

### 5. Integration Tests

**Objetivo**: Probar flujos completos de usuario.

```typescript
// Ejemplo: Test de integración
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider } from '@/contexts/AuthContext';
import { LoginPage } from '@/app/(auth)/login/page';

describe('Login Flow', () => {
  it('should complete login flow successfully', async () => {
    const user = userEvent.setup();
    
    render(
      <AuthProvider>
        <LoginPage />
      </AuthProvider>
    );

    // Fill login form
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    
    // Submit form
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    // Wait for redirect or success message
    await waitFor(() => {
      expect(screen.getByText(/welcome/i)).toBeInTheDocument();
    });
  });
});
```

## ⚙️ Configuración

### Jest Configuration

```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/__tests__/**',
  ],
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50,
    },
    './src/lib/services/': {
      branches: 60,
      functions: 60,
      lines: 60,
      statements: 60,
    },
  },
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
  ],
};

module.exports = createJestConfig(customJestConfig);
```

### Test Setup

```javascript
// jest.setup.js
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Mock Supabase
jest.mock('@/lib/supabase/supabaseClient', () => ({
  createClient: jest.fn(),
}));

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
```

## 🎭 Patrones de Testing

### 1. Arrange-Act-Assert (AAA)

```typescript
describe('calculateTokenCost', () => {
  it('should calculate token cost correctly', () => {
    // Arrange
    const text = 'Hello world';
    const expectedCost = 3;

    // Act
    const result = calculateTokenCost(text);

    // Assert
    expect(result).toBe(expectedCost);
  });
});
```

### 2. Given-When-Then

```typescript
describe('UserProfile component', () => {
  describe('given a user with premium plan', () => {
    const premiumUser = { ...mockUser, plan: 'premium' };

    describe('when component renders', () => {
      it('then should show premium badge', () => {
        render(<UserProfile user={premiumUser} />);
        expect(screen.getByText('Premium')).toBeInTheDocument();
      });
    });
  });
});
```

### 3. Test Factories

```typescript
// testUtils.ts
export const createMockUser = (overrides = {}) => ({
  id: '123',
  email: '<EMAIL>',
  name: 'Test User',
  plan: 'free',
  createdAt: new Date(),
  ...overrides,
});

export const createMockDocument = (overrides = {}) => ({
  id: '456',
  title: 'Test Document',
  content: 'Test content',
  userId: '123',
  ...overrides,
});

// Usage in tests
describe('DocumentList', () => {
  it('should render documents', () => {
    const documents = [
      createMockDocument({ title: 'Doc 1' }),
      createMockDocument({ title: 'Doc 2' }),
    ];

    render(<DocumentList documents={documents} />);
    
    expect(screen.getByText('Doc 1')).toBeInTheDocument();
    expect(screen.getByText('Doc 2')).toBeInTheDocument();
  });
});
```

### 4. Custom Render

```typescript
// testUtils.tsx
import { render } from '@testing-library/react';
import { AuthProvider } from '@/contexts/AuthContext';

export const renderWithAuth = (ui, { user = null, ...options } = {}) => {
  const Wrapper = ({ children }) => (
    <AuthProvider initialUser={user}>
      {children}
    </AuthProvider>
  );

  return render(ui, { wrapper: Wrapper, ...options });
};

// Usage
describe('Dashboard', () => {
  it('should show user dashboard when authenticated', () => {
    const user = createMockUser();
    renderWithAuth(<Dashboard />, { user });
    
    expect(screen.getByText('Welcome back!')).toBeInTheDocument();
  });
});
```

## 🎭 Mocking

### 1. Module Mocking

```typescript
// Mock entire module
jest.mock('@/lib/services/authService', () => ({
  authService: {
    signIn: jest.fn(),
    signOut: jest.fn(),
    getCurrentUser: jest.fn(),
  },
}));

// Partial module mocking
jest.mock('@/lib/utils/dateUtils', () => ({
  ...jest.requireActual('@/lib/utils/dateUtils'),
  formatDate: jest.fn(),
}));
```

### 2. Function Mocking

```typescript
describe('UserService', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should fetch user data', async () => {
    const mockFetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ id: '123', name: 'Test User' }),
    });

    global.fetch = mockFetch;

    const user = await userService.getById('123');

    expect(mockFetch).toHaveBeenCalledWith('/api/users/123');
    expect(user).toEqual({ id: '123', name: 'Test User' });
  });
});
```

### 3. Timer Mocking

```typescript
describe('useDebounce', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should debounce value changes', () => {
    const { result, rerender } = renderHook(
      ({ value }) => useDebounce(value, 500),
      { initialProps: { value: 'initial' } }
    );

    expect(result.current).toBe('initial');

    rerender({ value: 'updated' });
    expect(result.current).toBe('initial'); // Still old value

    jest.advanceTimersByTime(500);
    expect(result.current).toBe('updated'); // Now updated
  });
});
```

### 4. API Mocking

```typescript
// Using MSW (Mock Service Worker)
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/users/:id', (req, res, ctx) => {
    const { id } = req.params;
    return res(
      ctx.json({
        id,
        name: 'Test User',
        email: '<EMAIL>',
      })
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

## 📊 Coverage

### Coverage Goals

```javascript
// jest.config.js
coverageThreshold: {
  global: {
    branches: 50,
    functions: 50,
    lines: 50,
    statements: 50,
  },
  './src/lib/services/': {
    branches: 60,
    functions: 60,
    lines: 60,
    statements: 60,
  },
  './src/lib/utils/': {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80,
  },
}
```

### Coverage Commands

```bash
# Generate coverage report
npm run test:coverage

# View coverage in browser
open coverage/lcov-report/index.html

# Coverage for specific files
npm test -- --coverage --collectCoverageFrom="src/features/auth/**/*.ts"
```

### Coverage Best Practices

1. **Focus on Critical Paths**: Priorizar coverage en lógica de negocio crítica
2. **Don't Chase 100%**: 80-90% es generalmente suficiente
3. **Quality over Quantity**: Mejor pocos tests buenos que muchos tests malos
4. **Ignore Generated Code**: Excluir archivos generados del coverage

## 🚀 CI/CD

### GitHub Actions

```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type check
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

### Pre-commit Hooks

```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "npm run lint:fix",
      "npm run type-check",
      "npm test -- --findRelatedTests --passWithNoTests"
    ]
  }
}
```

## 📚 Recursos Adicionales

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [MSW Documentation](https://mswjs.io/docs/)

---

¡Happy testing! 🧪
