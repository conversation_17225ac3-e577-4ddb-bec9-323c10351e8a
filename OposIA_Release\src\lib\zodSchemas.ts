// Directorio para esquemas Zod reutilizables
import { z } from 'zod';

export const DocumentoSchema = z.object({
  id: z.string().optional(),
  titulo: z.string().min(1).max(200),
  contenido: z.string().min(1),
  categoria: z.string().optional().nullable(),
  numero_tema: z.union([z.number().int().positive(), z.string(), z.null(), z.undefined()]).optional(),
  creado_en: z.string().optional(),
  actualizado_en: z.string().optional(),
  user_id: z.string().optional(),
  tipo_original: z.string().optional(),
});

export const PreguntaSchema = z.object({
  pregunta: z.string().min(1).max(500),
  documentos: z.array(DocumentoSchema).min(1),
});

export const GenerarTestSchema = z.object({
  action: z.literal('generarTest'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)),
  cantidad: z.number().int().min(1).max(50).optional(),
});

export const GenerarFlashcardsSchema = z.object({
  action: z.literal('generarFlashcards'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)),
  cantidad: z.number().int().min(1).max(50).optional(),
});

export const GenerarMapaMentalSchema = z.object({
  action: z.literal('generarMapaMental'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)),
});

export const GenerarPlanEstudiosSchema = z.object({
  action: z.literal('generarPlanEstudios'),
  peticion: z.string().min(1), // temarioId viene en peticion para consistencia
  contextos: z.array(z.string()).optional(), // contextos opcionales para mantener interfaz estándar
});

export const GenerarResumenSchema = z.object({
  action: z.literal('generarResumen'),
  peticion: z.string().min(1).max(1000), // titulo|categoria|numero_tema|instrucciones
  contextos: z.array(z.string().min(1)).length(1), // exactamente un documento
});

export const EditarResumenSchema = z.object({
  action: z.literal('editarResumen'),
  contextos: z.array(z.string().min(1)).length(1), // exactamente el contenido del resumen a editar
});

// Renombrado para reflejar que es un endpoint de AI general, no específicamente Gemini
export const ApiAIInputSchema = z.union([
  PreguntaSchema,
  GenerarTestSchema,
  GenerarFlashcardsSchema,
  GenerarMapaMentalSchema,
  GenerarPlanEstudiosSchema,
  GenerarResumenSchema,
  EditarResumenSchema,
]);

// Mantener compatibilidad con código existente
export const ApiGeminiInputSchema = ApiAIInputSchema;
