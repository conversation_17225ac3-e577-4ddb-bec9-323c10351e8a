// src/lib/services/freeAccountService.ts
// Servicio para gestión automatizada de cuentas gratuitas

import { SupabaseAdminService, ExtendedUserProfile, supabaseAdmin } from '@/lib/supabase/admin';
import { getPlanConfiguration, getTokenLimitForPlan } from '@/config/plans';
import { randomUUID } from 'crypto';

export interface CreateFreeAccountRequest {
  email: string;
  name?: string;
}

export interface FreeAccountResult {
  success: boolean;
  userId?: string;
  profileId?: string;
  error?: string;
  expiresAt?: string;
}

// FreeAccountStatus y otras interfaces/clases no cambian
export interface FreeAccountStatus {
  isActive: boolean;
  expiresAt: string | null;
  daysRemaining: number;
  hoursRemaining: number;
  usageCount: {
    documents: number;
    tests: number;
    flashcards: number;
    mindMaps: number;
    tokens: number;
  };
  limits: {
    documents: number;
    tests: number;
    flashcards: number;
    mindMaps: number;
    tokens: number;
  };
}

export class FreeAccountService {

  /**
   * Crear cuenta gratuita automáticamente
   * Ahora crea el usuario directamente y genera un enlace de tipo 'recovery' para establecer la contraseña.
   */
  static async createFreeAccount(request: CreateFreeAccountRequest): Promise<FreeAccountResult> {
    try {
      console.log('🆓 Iniciando creación de cuenta gratuita (flujo de invitación):', request.email);

      // 1. Validar que el email no esté ya registrado
      try {
        const existingUser = await SupabaseAdminService.getUserByEmail(request.email);
        if (existingUser) {
           return { success: false, error: 'El email ya está registrado en el sistema.' };
        }
      } catch (error) {
        if (error instanceof Error && error.message.toLowerCase().includes('user not found')) {
          console.log('Usuario no existe en Auth, continuando con la invitación.');
        } else {
          console.warn('Advertencia al verificar usuario existente, se continuará con el intento de invitación:', error);
        }
      }

      // 2. Calcular fecha de expiración (5 días desde ahora)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 5);

      // 3. Preparar metadatos para el usuario invitado
      const userDataForCreation = {
        name: request.name || request.email.split('@')[0],
        plan: 'free',
        free_account: true,
        expires_at: expiresAt.toISOString(),
        created_via: 'free_invitation_flow',
        registration_type: 'automatic_free_invitation',
        requires_password_setup: true
      };

      console.log('📧 Invitando nuevo usuario con datos:', {
        email: request.email,
        userData: userDataForCreation,
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/confirm-invitation`
      });

      // 4. Invitar al usuario. Esto crea el usuario y envía el email de invitación.
      const { data: { user: newUser }, error: inviteError } = await supabaseAdmin.auth.admin.inviteUserByEmail(
        request.email,
        {
          data: userDataForCreation,
          redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/confirm-invitation`,
        }
      );

      if (inviteError) {
        console.error('❌ Error invitando al usuario:', inviteError);
        if (inviteError.message.includes('User already registered')) {
            return { success: false, error: 'Ya existe una cuenta con este email.' };
        }
        throw new Error(`Error invitando al usuario: ${inviteError.message}`);
      }
      if (!newUser) {
        throw new Error('Usuario no devuelto después de la invitación.');
      }

      console.log('✅ Usuario invitado exitosamente a Supabase Auth:', newUser.id);

      // 5. Crear perfil de usuario y registrar historial de forma atómica
      const planConfig = getPlanConfiguration('free');
      if (!planConfig) {
        await supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup
        throw new Error('Configuración de plan gratuito no encontrada');
      }

      const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
      const profileDataForRPC = {
        subscription_plan: 'free',
        monthly_token_limit: getTokenLimitForPlan('free'),
        current_month_tokens: 0,
        current_month: currentMonth,
        payment_verified: true,
        stripe_customer_id: null,
        stripe_subscription_id: null,
        last_payment_date: null,
        auto_renew: false,
        plan_expires_at: expiresAt.toISOString(),
        plan_features: planConfig.features,
        security_flags: {
          created_via_free_invitation_flow: true,
          free_account: true,
          expires_at: expiresAt.toISOString(),
          activation_date: new Date().toISOString(),
          usage_count: { documents: 0, tests: 0, flashcards: 0, mindMaps: 0, tokens: 0 }
        }
      };

      const { data: creationResult, error: rpcError } = await supabaseAdmin
        .rpc('create_user_profile_and_history', {
          p_user_id: newUser.id,
          p_transaction_id: null,
          p_profile_data: profileDataForRPC
        })
        .single();

      if (rpcError) {
        console.error('❌ Error al ejecutar la función RPC create_user_profile_and_history:', rpcError);
        await supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup
        throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);
      }

      const profileId = (creationResult as any).created_profile_id;
      console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);

      console.log('🎉 Cuenta gratuita creada exitosamente con flujo de invitación.');

      return {
        success: true,
        userId: newUser.id,
        profileId: profileId,
        expiresAt: expiresAt.toISOString(),
      };

    } catch (error) {
      console.error('❌ Error crítico en la creación de cuenta gratuita:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido al crear cuenta gratuita'
      };
    }
  }

  // Las funciones getFreeAccountStatus, incrementUsageCount, canPerformAction, cleanupExpiredAccounts
  // permanecen igual que antes, ya que su lógica no depende directamente de cómo se creó el usuario,
  // sino de los datos en user_profiles.

  /**
   * Verificar estado de cuenta gratuita
   */
  static async getFreeAccountStatus(userId: string): Promise<FreeAccountStatus | null> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);

      if (!profile || profile.subscription_plan !== 'free') {
        return null;
      }

      const now = new Date();
      const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;

      if (!expiresAt) {
        return null;
      }

      const isActive = now < expiresAt;
      const timeDiff = expiresAt.getTime() - now.getTime();
      const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
      const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));

      const usageCount = profile.security_flags?.usage_count || {
        documents: 0, tests: 0, flashcards: 0, mindMaps: 0, tokens: 0
      };
      usageCount.tokens = profile.current_month_tokens || 0;

      const planConfig = getPlanConfiguration('free');
      const limits = {
        documents: planConfig?.limits.documents || 1,
        tests: planConfig?.limits.testsForTrial || 10,
        flashcards: planConfig?.limits.flashcardsForTrial || 10,
        mindMaps: planConfig?.limits.mindMapsForTrial || 2,
        tokens: planConfig?.limits.tokensForTrial || 50000
      };
      
      // Calcular progressPercentage
      const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms
      const creationDate = new Date(profile.created_at || Date.now()); // Usar created_at o now si no está
      const activationDate = profile.security_flags?.activation_date ? new Date(profile.security_flags.activation_date) : creationDate;
      const timeElapsed = now.getTime() - activationDate.getTime();
      const progressPercentage = Math.min(100, Math.max(0, (timeElapsed / totalDuration) * 100));


      return {
        isActive,
        expiresAt: expiresAt.toISOString(),
        daysRemaining,
        hoursRemaining,
        usageCount,
        limits,
        // @ts-ignore Asegurar que progressPercentage está en el tipo si es necesario
        progressPercentage: Math.round(progressPercentage),
      };

    } catch (error) {
      console.error('Error obteniendo estado de cuenta gratuita:', error);
      return null;
    }
  }

  /**
   * Incrementar contador de uso
   */
  static async incrementUsageCount(
    userId: string,
    feature: 'documents' | 'tests' | 'flashcards' | 'mindMaps',
    amount: number = 1
  ): Promise<boolean> {
    try {
      const profile = await SupabaseAdminService.getUserProfile(userId);

      if (!profile || profile.subscription_plan !== 'free') {
        return false;
      }

      const currentUsage = profile.security_flags?.usage_count || {
        documents: 0, tests: 0, flashcards: 0, mindMaps: 0, tokens: 0
      };

      currentUsage[feature] = (currentUsage[feature] || 0) + amount;

      const updateData: Partial<ExtendedUserProfile> = {
        security_flags: {
          ...profile.security_flags,
          usage_count: currentUsage
        },
        updated_at: new Date().toISOString()
      };

      await supabaseAdmin
        .from('user_profiles')
        .update(updateData)
        .eq('user_id', userId);

      return true;

    } catch (error) {
      console.error('Error incrementando contador de uso:', error);
      return false;
    }
  }

  /**
   * Verificar si se puede realizar una acción
   */
  static async canPerformAction(
    userId: string,
    feature: 'documents' | 'tests' | 'flashcards' | 'mindMaps' | 'tokens',
    amount: number = 1
  ): Promise<{ allowed: boolean; reason?: string; remaining?: number }> {
    try {
      const status = await this.getFreeAccountStatus(userId);

      if (!status) {
        return { allowed: false, reason: 'Cuenta no encontrada o no es gratuita' };
      }

      if (!status.isActive) {
        return { allowed: false, reason: 'Cuenta gratuita expirada' };
      }

      const currentUsage = status.usageCount[feature] || 0;
      const limit = status.limits[feature];
      const remaining = limit - currentUsage;

      if (currentUsage + amount > limit) {
        return {
          allowed: false,
          reason: `Límite de ${feature} alcanzado (${currentUsage}/${limit})`,
          remaining: Math.max(0, remaining)
        };
      }

      return {
        allowed: true,
        remaining: remaining - amount
      };

    } catch (error) {
      console.error('Error verificando acción:', error);
      return { allowed: false, reason: 'Error interno' };
    }
  }

  /**
   * Limpiar cuentas gratuitas expiradas
   */
  static async cleanupExpiredAccounts(): Promise<{
    cleaned: number;
    errors: string[];
  }> {
    try {
      console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');

      const now = new Date().toISOString();

      const { data: expiredProfiles, error } = await supabaseAdmin
        .from('user_profiles')
        .select('user_id, id')
        .eq('subscription_plan', 'free')
        .lt('plan_expires_at', now)
        // Añadir condición para asegurar que no se desactiven cuentas ya deshabilitadas
        .neq('security_flags ->> account_disabled', 'true');


      if (error) {
        throw new Error(`Error buscando cuentas expiradas: ${error.message}`);
      }

      if (!expiredProfiles || expiredProfiles.length === 0) {
        console.log('✅ No hay cuentas expiradas para limpiar');
        return { cleaned: 0, errors: [] };
      }

      console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas para procesar`);

      const errors: string[] = [];
      let cleaned = 0;

      for (const profile of expiredProfiles) {
        try {
          // Desactivar usuario en auth
          await supabaseAdmin.auth.admin.updateUserById(profile.user_id, {
            user_metadata: { account_disabled: true, disabled_reason: 'free_account_expired' }
          });

          // Marcar perfil como inactivo
          // Obtener security_flags existentes para no sobrescribirlas
          const { data: currentProfileData } = await supabaseAdmin
            .from('user_profiles')
            .select('security_flags')
            .eq('user_id', profile.user_id)
            .single();

          const existingFlags = currentProfileData?.security_flags || {};

          await supabaseAdmin
            .from('user_profiles')
            .update({
              payment_verified: false, // Marcar pago como no verificado
              security_flags: {
                ...existingFlags, // Mantener flags existentes
                account_disabled: true,
                disabled_at: new Date().toISOString(),
                disabled_reason: 'free_account_expired'
              }
            })
            .eq('user_id', profile.user_id);

          cleaned++;

        } catch (cleanupError) {
          const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);

      return { cleaned, errors };

    } catch (error) {
      console.error('❌ Error en limpieza de cuentas:', error);
      return {
        cleaned: 0,
        errors: [error instanceof Error ? error.message : 'Error desconocido']
      };
    }
  }
}